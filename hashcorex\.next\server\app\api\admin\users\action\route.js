"use strict";(()=>{var e={};e.id=6843,e.ids=[6843],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>x,DY:()=>f,Lx:()=>h,Oj:()=>y,b9:()=>w,qc:()=>g});var a=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await a.Ay.hash(e,12),l=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},w=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let r,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await d(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await l(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>{let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||r.push("Password must contain at least one special character"),{valid:0===r.length,errors:r}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72708:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),d=t(31183),l=t(6710);async function c(e){try{let r,{authenticated:t,user:a}=await (0,u.b9)(e);if(!t||!a)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,u.qc)(a.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{userId:s,action:i}=await e.json();if(!s||!i)return o.NextResponse.json({error:"User ID and action are required"},{status:400});let n=await d.prisma.user.findUnique({where:{id:s},select:{id:!0,email:!0,firstName:!0,lastName:!0,role:!0,isActive:!0}});if(!n)return o.NextResponse.json({error:"User not found"},{status:404});let c="",p={};switch(i){case"activate":r=await d.prisma.user.update({where:{id:s},data:{isActive:!0}}),c="USER_ACTIVATED",p={targetUserId:s,targetUserEmail:n.email};break;case"deactivate":r=await d.prisma.user.update({where:{id:s},data:{isActive:!1}}),c="USER_DEACTIVATED",p={targetUserId:s,targetUserEmail:n.email};break;case"promote":if("ADMIN"===n.role)return o.NextResponse.json({error:"User is already an admin"},{status:400});r=await d.prisma.user.update({where:{id:s},data:{role:"ADMIN"}}),c="USER_PROMOTED_TO_ADMIN",p={targetUserId:s,targetUserEmail:n.email};break;case"demote":if("USER"===n.role)return o.NextResponse.json({error:"User is already a regular user"},{status:400});r=await d.prisma.user.update({where:{id:s},data:{role:"USER"}}),c="USER_DEMOTED_FROM_ADMIN",p={targetUserId:s,targetUserEmail:n.email};break;default:return o.NextResponse.json({error:"Invalid action"},{status:400})}return await l.AJ.create({action:c,userId:a.id,details:p,ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:`User ${i}d successfully`,data:r})}catch(e){return console.error("Admin user action error:",e),o.NextResponse.json({success:!1,error:"Failed to perform user action"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/action/route",pathname:"/api/admin/users/action",filename:"route",bundlePath:"app/api/admin/users/action/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\action\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:f}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(72708));module.exports=a})();