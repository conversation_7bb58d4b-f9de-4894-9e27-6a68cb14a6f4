"use strict";(()=>{var e={};e.id=628,e.ids=[628],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>x,DY:()=>h,Lx:()=>w,Oj:()=>y,b9:()=>f,qc:()=>k});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await s.Ay.hash(e,12),l=async(e,t)=>await s.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},h=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await d(e.password),i=!1;do s=m(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),k=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},82932:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{POST:()=>l});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),d=r(6710);async function l(e,{params:t}){try{let{authenticated:r,user:s}=await (0,u.b9)(e);if(!r||!s)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("ADMIN"!==s.role)return o.NextResponse.json({success:!1,error:"Access denied"},{status:403});let{ticketId:a}=await t,{message:i}=await e.json();if(!i||!i.trim())return o.NextResponse.json({success:!1,error:"Message is required"},{status:400});let n=await d.OZ.findById(a);if(!n)return o.NextResponse.json({success:!1,error:"Ticket not found"},{status:404});if("CLOSED"===n.status)return o.NextResponse.json({success:!1,error:"Cannot add response to closed ticket"},{status:400});let l=await d.o1.create({ticketId:a,userId:s.id,message:i.trim(),isAdmin:!0});return"OPEN"===n.status&&await d.OZ.updateStatus(a,"IN_PROGRESS"),o.NextResponse.json({success:!0,data:l,message:"Admin response added successfully"})}catch(e){return console.error("Admin ticket response creation error:",e),o.NextResponse.json({success:!1,error:"Failed to add admin response"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/support/tickets/[ticketId]/responses/route",pathname:"/api/admin/support/tickets/[ticketId]/responses",filename:"route",bundlePath:"app/api/admin/support/tickets/[ticketId]/responses/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\[ticketId]\\responses\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=c;function h(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306],()=>r(82932));module.exports=s})();