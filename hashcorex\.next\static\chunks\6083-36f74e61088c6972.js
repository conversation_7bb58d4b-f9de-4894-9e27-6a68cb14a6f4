"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6083],{3741:(e,t,r)=>{r.d(t,{$:()=>i});var s=r(5155),l=r(2115),n=r(2085),a=r(9434);let o=(0,n.F)("inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg",{variants:{variant:{primary:"bg-yellow-500 text-white focus:ring-yellow-500",secondary:"bg-gray-200 text-gray-800 focus:ring-gray-500",success:"bg-emerald-500 text-white focus:ring-emerald-500",danger:"bg-red-500 text-white focus:ring-red-500",warning:"bg-yellow-500 text-white focus:ring-yellow-500",destructive:"bg-red-600 text-white focus:ring-red-500",outline:"border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500",ghost:"text-gray-600 focus:ring-yellow-500 rounded-lg",link:"text-yellow-600 underline-offset-4 focus:ring-yellow-500",premium:"bg-slate-800 text-white focus:ring-slate-500",glass:"glass-morphism text-slate-900 backdrop-blur-xl border border-white/20"},size:{sm:"h-10 px-4 text-sm rounded-lg",md:"h-12 px-6 text-base rounded-xl",lg:"h-14 px-8 text-lg rounded-xl",xl:"h-16 px-10 text-xl rounded-2xl font-bold",icon:"h-12 w-12 rounded-xl"}},defaultVariants:{variant:"primary",size:"md"}}),i=l.forwardRef((e,t)=>{let{className:r,variant:l,size:n,loading:i,leftIcon:c,rightIcon:d,children:x,disabled:u,...h}=e;return(0,s.jsxs)("button",{className:(0,a.cn)(o({variant:l,size:n,className:r})),ref:t,disabled:u||i,...h,children:[i&&(0,s.jsx)("div",{className:"mr-2",children:(0,s.jsx)("div",{className:"spinner"})}),c&&!i&&(0,s.jsx)("span",{className:"mr-2",children:c}),x,d&&!i&&(0,s.jsx)("span",{className:"ml-2",children:d})]})});i.displayName="Button"},7508:(e,t,r)=>{r.d(t,{Lc:()=>a,hK:()=>o,NC:()=>n,MX:()=>l,Kj:()=>i});var s=r(5155);r(2115);let l=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"2",y1:"8",x2:"22",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"12",x2:"22",y2:"12",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"16",x2:"22",y2:"16",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"6",y1:"4",x2:"6",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"10",y1:"4",x2:"10",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"14",y1:"4",x2:"14",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"4",x2:"18",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("circle",{cx:"20",cy:"2",r:"1",fill:"currentColor"}),(0,s.jsx)("path",{d:"M19 1l1 1-1 1-1-1z",fill:"currentColor"})]})},n=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("rect",{x:"2",y:"6",width:"20",height:"12",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("rect",{x:"4",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"4",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("circle",{cx:"20",cy:"4",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"20",y1:"4",x2:"20",y2:"6",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"2",x2:"22",y2:"2",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"19",y1:"1",x2:"21",y2:"3",stroke:"currentColor",strokeWidth:"1"}),(0,s.jsx)("line",{x1:"21",y1:"1",x2:"19",y2:"3",stroke:"currentColor",strokeWidth:"1"})]})},a=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 12h8M12 8v8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"12",y1:"6",x2:"12",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"18",stroke:"currentColor",strokeWidth:"2"})]})},o=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("path",{d:"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z",fill:"currentColor"})]})},i=e=>{let{className:t,size:r=24}=e;return(0,s.jsxs)("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:t,children:[(0,s.jsx)("line",{x1:"12",y1:"12",x2:"12",y2:"22",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("circle",{cx:"12",cy:"12",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"10",y1:"22",x2:"14",y2:"22",stroke:"currentColor",strokeWidth:"2"})]})}},8928:(e,t,r)=>{r.d(t,{$n:()=>s.$,Zp:()=>o,Wu:()=>d,aR:()=>i,ZB:()=>c,pd:()=>x,Rh:()=>f,aF:()=>m,G_:()=>j,eC:()=>C});var s=r(3741),l=r(5155),n=r(2115),a=r(9434);let o=n.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,l.jsx)("div",{ref:t,className:(0,a.cn)("rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl overflow-hidden",r),...n,children:s})});o.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,l.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6 pb-4",r),...n,children:s})});i.displayName="CardHeader";let c=n.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,l.jsx)("h3",{ref:t,className:(0,a.cn)("text-xl font-semibold leading-none tracking-tight text-dark-900",r),...n,children:s})});c.displayName="CardTitle",n.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,l.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-gray-500",r),...n,children:s})}).displayName="CardDescription";let d=n.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,l.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...n,children:s})});d.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,l.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...n,children:s})}).displayName="CardFooter";let x=n.forwardRef((e,t)=>{let{className:r,type:s,label:n,error:o,leftIcon:i,rightIcon:c,...d}=e;return(0,l.jsxs)("div",{className:"w-full",children:[n&&(0,l.jsx)("label",{className:"block text-sm font-medium text-dark-700 mb-2",children:n}),(0,l.jsxs)("div",{className:"relative",children:[i&&(0,l.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,l.jsx)("span",{className:"text-gray-400",children:i})}),(0,l.jsx)("input",{type:s,className:(0,a.cn)("flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300",i&&"pl-12",c&&"pr-12",o&&"border-red-500 focus:ring-red-500 focus:border-red-500",r),ref:t,...d}),c&&(0,l.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,l.jsx)("span",{className:"text-gray-400",children:c})})]}),o&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]})});x.displayName="Input";var u=r(7650),h=r(4416);let m=e=>{let{isOpen:t,onClose:r,title:o,children:i,size:c="md",showCloseButton:d=!0,darkMode:x=!1}=e;if((0,n.useEffect)(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),(0,n.useEffect)(()=>{let e=e=>{"Escape"===e.key&&r()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[t,r]),!t)return null;let m=(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:r}),(0,l.jsxs)("div",{className:(0,a.cn)("relative w-full rounded-xl shadow-xl transform transition-all",x?"bg-slate-800":"bg-white",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[c]),onClick:e=>e.stopPropagation(),children:[(0,l.jsxs)("div",{className:(0,a.cn)("flex items-center justify-between p-6 border-b",x?"border-slate-700":"border-gray-200"),children:[(0,l.jsx)("h2",{className:(0,a.cn)("text-xl font-semibold",x?"text-white":"text-dark-900"),children:o}),d&&(0,l.jsx)(s.$,{variant:"ghost",size:"icon",onClick:r,className:"h-8 w-8 rounded-full",children:(0,l.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,l.jsx)("div",{className:"p-6",children:i})]})]});return(0,u.createPortal)(m,document.body)},f=e=>{let{size:t="md",className:r,text:s}=e;return(0,l.jsxs)("div",{className:(0,a.cn)("flex flex-col items-center justify-center",r),children:[(0,l.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-solar-500",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[t])}),s&&(0,l.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:s})]})};var g=r(1243),w=r(646),y=r(1284);let p=e=>{let{isOpen:t,onClose:r,onConfirm:n,title:o,message:i,confirmText:c="Confirm",cancelText:d="Cancel",variant:x="default",darkMode:m=!1,loading:f=!1}=e;if(!t)return null;let p=(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:r}),(0,l.jsxs)("div",{className:(0,a.cn)("relative w-full max-w-md rounded-xl shadow-xl transform transition-all",m?"bg-slate-800 border border-slate-700":"bg-white"),onClick:e=>e.stopPropagation(),children:[(0,l.jsxs)("div",{className:(0,a.cn)("flex items-center justify-between p-6 border-b",m?"border-slate-700":"border-gray-200"),children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(x){case"danger":return(0,l.jsx)(g.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,l.jsx)(g.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,l.jsx)(w.A,{className:"h-6 w-6 text-green-500"});default:return(0,l.jsx)(y.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,l.jsx)("h2",{className:(0,a.cn)("text-lg font-semibold",m?"text-white":"text-gray-900"),children:o})]}),(0,l.jsx)(s.$,{variant:"ghost",size:"icon",onClick:r,disabled:f,className:"h-8 w-8 rounded-full",children:(0,l.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,l.jsx)("div",{className:"p-6",children:"string"==typeof i?(0,l.jsx)("p",{className:(0,a.cn)("text-sm leading-relaxed",m?"text-slate-300":"text-gray-600"),children:i}):(0,l.jsx)("div",{className:(0,a.cn)("text-sm leading-relaxed",m?"text-slate-300":"text-gray-600"),children:i})}),(0,l.jsxs)("div",{className:(0,a.cn)("flex items-center justify-end space-x-3 p-6 border-t",m?"border-slate-700":"border-gray-200"),children:[(0,l.jsx)(s.$,{variant:"outline",onClick:r,disabled:f,className:(0,a.cn)(m?"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white":""),children:d}),(0,l.jsx)(s.$,{variant:(()=>{switch(x){case"danger":return"destructive";case"warning":return"warning";default:return"default"}})(),onClick:n,disabled:f,className:(0,a.cn)(f&&"opacity-50 cursor-not-allowed"),children:f?"Processing...":c})]})]})]});return(0,u.createPortal)(p,document.body)},j=()=>{let[e,t]=n.useState({isOpen:!1,title:"",message:"",onConfirm:()=>{}}),[r,s]=n.useState(!1),a=()=>{r||t(e=>({...e,isOpen:!1}))};return{showConfirm:e=>{t({isOpen:!0,...e,onConfirm:async()=>{s(!0);try{await e.onConfirm(),t(e=>({...e,isOpen:!1}))}catch(e){console.error("Confirm action failed:",e)}finally{s(!1)}}})},hideConfirm:a,ConfirmDialog:()=>(0,l.jsx)(p,{isOpen:e.isOpen,onClose:a,onConfirm:e.onConfirm,title:e.title,message:e.message,variant:e.variant,confirmText:e.confirmText,cancelText:e.cancelText,darkMode:e.darkMode,loading:r}),loading:r}};var b=r(5339);let v=e=>{let{isOpen:t,onClose:r,title:n,message:o,variant:i="info",darkMode:c=!1,showCloseButton:d=!0,buttonText:x="OK",size:m="md"}=e;if(!t)return null;let f=(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:r,children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,l.jsxs)("div",{className:(0,a.cn)("relative w-full rounded-xl shadow-xl transform transition-all",c?"bg-slate-800 border border-slate-700":"bg-white",{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"}[m]),onClick:e=>e.stopPropagation(),children:[(0,l.jsxs)("div",{className:(0,a.cn)("flex items-center justify-between p-6 border-b",c?"border-slate-700":"border-gray-200"),children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(i){case"error":return(0,l.jsx)(b.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,l.jsx)(g.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,l.jsx)(w.A,{className:"h-6 w-6 text-green-500"});default:return(0,l.jsx)(y.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,l.jsx)("h2",{className:(0,a.cn)("text-lg font-semibold",c?"text-white":"text-gray-900"),children:n})]}),d&&(0,l.jsx)(s.$,{variant:"ghost",size:"icon",onClick:r,className:"h-8 w-8 rounded-full",children:(0,l.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,l.jsx)("div",{className:"p-6",children:"string"==typeof o?(0,l.jsx)("p",{className:(0,a.cn)("text-sm leading-relaxed",c?"text-slate-300":"text-gray-600"),children:o}):(0,l.jsx)("div",{className:(0,a.cn)("text-sm leading-relaxed",c?"text-slate-300":"text-gray-600"),children:o})}),(0,l.jsx)("div",{className:(0,a.cn)("flex items-center justify-end p-6 border-t",c?"border-slate-700":"border-gray-200"),children:(0,l.jsx)(s.$,{variant:(()=>{switch(i){case"error":return"danger";case"warning":return"warning";case"success":return"success";default:return"primary"}})(),onClick:r,className:"min-w-[80px]",children:x})})]})]});return(0,u.createPortal)(f,document.body)},C=()=>{let[e,t]=n.useState({isOpen:!1,title:"",message:""}),r=()=>{t(e=>({...e,isOpen:!1}))};return{showMessage:e=>{t({isOpen:!0,...e})},hideMessage:r,MessageBoxComponent:()=>(0,l.jsx)(v,{...e,onClose:r})}}},9434:(e,t,r)=>{r.d(t,{D1:()=>m,Oj:()=>x,Yq:()=>i,ZU:()=>h,ZV:()=>o,cn:()=>n,jI:()=>u,lW:()=>d,r6:()=>c,vv:()=>a});var s=r(2596),l=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.QP)((0,s.$)(t))}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return new Intl.NumberFormat("en-US",{minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}function i(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function d(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),Promise.resolve()}catch(e){return Promise.reject(e)}finally{document.body.removeChild(t)}}function x(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}function u(e){return e>=1e3?"".concat((e/1e3).toFixed(1),"K TH/s"):"".concat(e.toFixed(2)," TH/s")}function h(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),l=Math.floor(r%864e5/36e5);return{days:s,hours:l,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}function m(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),l=Math.floor(r%864e5/36e5);return{days:s,hours:l,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}}}]);