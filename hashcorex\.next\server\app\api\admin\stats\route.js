"use strict";(()=>{var e={};e.id=9759,e.ids=[9759],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>w,Lx:()=>f,Oj:()=>y,b9:()=>h,qc:()=>x});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await a.Ay.hash(e,12),d=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=m(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},w=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await l(e.password),i=!1;do a=p(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},f=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},61166:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};r.r(a),r.d(a,{GET:()=>d});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),l=r(31183);async function d(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let[a,s,i,n,d]=await Promise.all([l.prisma.$transaction([l.prisma.user.count(),l.prisma.user.count({where:{isActive:!0}})]),l.prisma.$transaction([l.prisma.user.count({where:{kycStatus:"PENDING",kycDocuments:{some:{}}}}),l.prisma.user.count({where:{kycStatus:"APPROVED"}})]),l.prisma.$transaction([l.prisma.miningUnit.aggregate({_sum:{thsAmount:!0,investmentAmount:!0}}),l.prisma.miningUnit.aggregate({where:{status:"ACTIVE"},_sum:{thsAmount:!0}})]),l.prisma.$transaction([l.prisma.transaction.aggregate({where:{type:{in:["MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS"]},status:"COMPLETED"},_sum:{amount:!0}}),l.prisma.transaction.aggregate({where:{type:"PURCHASE",status:"COMPLETED"},_sum:{amount:!0}})]),l.prisma.$transaction([l.prisma.withdrawalRequest.count({where:{status:"PENDING"}}),l.prisma.withdrawalRequest.aggregate({where:{status:"COMPLETED"},_sum:{amount:!0}})])]),c=n[1]._sum.amount||0,m={totalUsers:a[0],activeUsers:a[1],pendingKYC:s[0],approvedKYC:s[1],totalInvestments:c,totalEarningsDistributed:n[0]._sum.amount||0,totalTHSSold:i[0]._sum.thsAmount||0,activeTHS:i[1]._sum.thsAmount||0,pendingWithdrawals:d[0],totalWithdrawals:d[1]._sum.amount||0};return o.NextResponse.json({success:!0,data:m})}catch(e){return console.error("Admin stats fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch admin statistics"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\stats\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:h}=c;function w(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(61166));module.exports=a})();