"use strict";(()=>{var e={};e.id=9409,e.ids=[9409],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16168:(e,n,i)=>{i.r(n),i.d(n,{patchFetch:()=>E,routeModule:()=>u,serverHooks:()=>p,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var t={};i.r(t),i.d(t,{POST:()=>d});var r=i(96559),a=i(48088),s=i(37719),o=i(32190),c=i(92731),l=i(6710);async function d(e){try{let n=e.headers.get("authorization"),i=process.env.CRON_SECRET||"default-secret";if(n!==`Bearer ${i}`)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});console.log("Starting daily ROI calculation cron job...");let t=await (0,c.eB)();console.log(`Expired ${t} old mining units`);let r=await (0,c.WL)();console.log(`Processed ${r.length} mining units for daily ROI`);let a=r.reduce((e,n)=>e+n.earnings,0),s=r.filter(e=>e.expired).length;return await l.AJ.create({action:"DAILY_ROI_CRON_EXECUTED",details:{unitsProcessed:r.length,totalEarnings:a,expiredUnits:s,oldUnitsExpired:t,executionTime:new Date().toISOString()}}),o.NextResponse.json({success:!0,message:"Daily ROI calculation completed",data:{unitsProcessed:r.length,totalEarnings:a,expiredUnits:s,oldUnitsExpired:t}})}catch(e){return console.error("Daily ROI cron job error:",e),await l.AJ.create({action:"DAILY_ROI_CRON_ERROR",details:{error:e.message,stack:e.stack,timestamp:new Date().toISOString()}}),o.NextResponse.json({success:!1,error:"Daily ROI calculation failed"},{status:500})}}let u=new r.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/cron/daily-roi/route",pathname:"/api/cron/daily-roi",filename:"route",bundlePath:"app/api/cron/daily-roi/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\daily-roi\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:p}=u;function E(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39794:(e,n,i)=>{i.d(n,{Py:()=>s,k8:()=>l,kp:()=>c});var t=i(31183),r=i(6710);async function a(e){return await t.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,n,i,s,c){let l=await a(e);if(0===l.length)throw Error("No active mining units found for earnings allocation");let d=[],u=n;for(let e of l){var g;if(u<=0)break;let n=Math.max(0,5*(g=e).investmentAmount-(g.miningEarnings+g.referralEarnings+g.binaryEarnings));if(n<=0)continue;let r=Math.min(u,n);if(r>0){let a={};switch(i){case"MINING_EARNINGS":a.miningEarnings={increment:r};break;case"DIRECT_REFERRAL":a.referralEarnings={increment:r};break;case"BINARY_BONUS":a.binaryEarnings={increment:r}}a.totalEarned={increment:r},await t.prisma.miningUnit.update({where:{id:e.id},data:a}),await t.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:i,amount:r,description:c}}),d.push({miningUnitId:e.id,amount:r,remainingCapacity:n-r}),u-=r;let l=await t.prisma.miningUnit.findUnique({where:{id:e.id}});l&&function(e){let n=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=n}(l)&&await o(e.id,"5x_investment_reached")}}return u>0&&(console.warn(`Unable to allocate ${u} to mining units - all units at capacity`),await r.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:n,allocatedAmount:n-u,overflowAmount:u,earningType:i,reason:"all_units_at_capacity"}})),d}async function o(e,n){let i=await t.prisma.miningUnit.findUnique({where:{id:e}});if(!i)throw Error(`Mining unit ${e} not found`);await t.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let a=i.miningEarnings+i.referralEarnings+i.binaryEarnings;await r.AJ.create({action:"MINING_UNIT_EXPIRED",userId:i.userId,details:{miningUnitId:e,reason:n,totalEarned:a,miningEarnings:i.miningEarnings,referralEarnings:i.referralEarnings,binaryEarnings:i.binaryEarnings,investmentAmount:i.investmentAmount,multiplier:a/i.investmentAmount}}),console.log(`Mining unit ${e} expired due to ${n}. Total earnings: ${a}`)}async function c(e){return await t.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function l(e){return await t.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},96330:e=>{e.exports=require("@prisma/client")}};var n=require("../../../../webpack-runtime.js");n.C(e);var i=e=>n(n.s=e),t=n.X(0,[4447,580,3306,5112],()=>i(16168));module.exports=t})();