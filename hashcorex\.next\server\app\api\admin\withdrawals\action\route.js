"use strict";(()=>{var e={};e.id=1767,e.ids=[1767],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,a,t)=>{t.d(a,{DT:()=>x,DY:()=>m,Lx:()=>f,Oj:()=>y,b9:()=>h,qc:()=>E});var r=t(85663),s=t(43205),i=t.n(s),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"7d",u=async e=>await r.Ay.hash(e,12),l=async(e,a)=>await r.Ay.compare(e,a),c=e=>i().sign(e,o,{expiresIn:d}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},w=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",a="HC";for(let t=0;t<8;t++)a+=e.charAt(Math.floor(Math.random()*e.length));return a},h=async e=>{let a=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!a)return{authenticated:!1,user:null};let t=p(a);if(!t)return{authenticated:!1,user:null};let r=await n.Gy.findByEmail(t.email);return r?{authenticated:!0,user:r}:{authenticated:!1,user:null}},m=async e=>{let a,r;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");a=t.id}let s=await u(e.password),i=!1;do r=w(),i=!await n.Gy.findByReferralId(r);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:r});if(a){let{placeUserByReferralType:r}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await r(a,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},f=async e=>{let a=await n.Gy.findByEmail(e.email);if(!a||!await l(e.password,a.password))throw Error("Invalid email or password");return{token:c({userId:a.id,email:a.email}),user:{id:a.id,email:a.email,referralId:a.referralId,kycStatus:a.kycStatus}}},y=e=>{let a=[];return e.length<8&&a.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||a.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||a.push("Password must contain at least one lowercase letter"),/\d/.test(e)||a.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||a.push("Password must contain at least one special character"),{valid:0===a.length,errors:a}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),E=async e=>{let a=await n.Gy.findById(e);return a?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65430:(e,a,t)=>{t.r(a),t.d(a,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>h});var r={};t.r(r),t.d(r,{POST:()=>c});var s=t(96559),i=t(48088),n=t(37719),o=t(32190),d=t(12909),u=t(31183),l=t(6710);async function c(e){try{let a,{authenticated:t,user:r}=await (0,d.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,d.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{withdrawalId:s,action:i,rejectionReason:n,transactionHash:c}=await e.json();if(!s||!i)return o.NextResponse.json({error:"Withdrawal ID and action are required"},{status:400});let p=await u.prisma.withdrawalRequest.findUnique({where:{id:s},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}});if(!p)return o.NextResponse.json({error:"Withdrawal request not found"},{status:404});let w="",h={withdrawalId:s,userId:p.userId,amount:p.amount,userEmail:p.user.email};switch(i){case"APPROVE":a=await u.prisma.withdrawalRequest.update({where:{id:s},data:{status:"APPROVED",processedBy:r.id,processedAt:new Date}}),await u.prisma.transaction.updateMany({where:{reference:s,type:"WITHDRAWAL",status:"PENDING"},data:{status:"COMPLETED"}}),w="WITHDRAWAL_APPROVED";break;case"REJECT":if(!n)return o.NextResponse.json({error:"Rejection reason is required"},{status:400});let m=parseFloat(await l.rs.get("withdrawalFeeFixed")||"3"),f=parseFloat(await l.rs.get("withdrawalFeePercentage")||"1"),y=p.amount*f/100,x=p.amount+(m+y),E=await l.k_.getOrCreate(p.userId);await l.k_.updateBalance(p.userId,{availableBalance:E.availableBalance+x}),a=await u.prisma.withdrawalRequest.update({where:{id:s},data:{status:"REJECTED",processedBy:r.id,processedAt:new Date,rejectionReason:n}}),await u.prisma.transaction.updateMany({where:{reference:s,type:"WITHDRAWAL",status:"PENDING"},data:{status:"FAILED"}}),w="WITHDRAWAL_REJECTED",h.rejectionReason=n,h.restoredAmount=x;break;case"COMPLETE":if(!c)return o.NextResponse.json({error:"Transaction hash is required"},{status:400});a=await u.prisma.withdrawalRequest.update({where:{id:s},data:{status:"COMPLETED",processedBy:r.id,processedAt:new Date,txid:c}});let R=await u.prisma.transaction.findFirst({where:{reference:s,type:"WITHDRAWAL"}});R&&await u.prisma.transaction.update({where:{id:R.id},data:{status:"COMPLETED",description:`${R.description} - TX: ${c}`}}),w="WITHDRAWAL_COMPLETED",h.transactionHash=c;break;default:return o.NextResponse.json({error:"Invalid action"},{status:400})}return await l.AJ.create({action:w,userId:r.id,details:h,ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:`Withdrawal ${i.toLowerCase()}d successfully`,data:a})}catch(e){return console.error("Admin withdrawal action error:",e),o.NextResponse.json({success:!1,error:"Failed to perform withdrawal action"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/withdrawals/action/route",pathname:"/api/admin/withdrawals/action",filename:"route",bundlePath:"app/api/admin/withdrawals/action/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\withdrawals\\action\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:w,workUnitAsyncStorage:h,serverHooks:m}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:h})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var a=require("../../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[4447,580,5315,3306],()=>t(65430));module.exports=r})();