"use strict";(()=>{var e={};e.id=5892,e.ids=[5892],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>h,Lx:()=>y,Oj:()=>w,b9:()=>f,qc:()=>x});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await s.Ay.hash(e,12),u=async(e,t)=>await s.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:l}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},h=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await d(e.password),i=!1;do s=m(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71088:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{POST:()=>u});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(12909),d=r(6710);async function u(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==r.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{testUserId:s,testSide:a,testPoints:i}=await e.json();if(!s||!a||!i)return o.NextResponse.json({success:!1,error:"Missing required test parameters"},{status:400});let n=parseFloat(await d.rs.get("MAX_BINARY_POINTS_PER_SIDE")||"10"),u=await d.FW.findByUserId(s),c=u?.leftPoints||0,p=u?.rightPoints||0;console.log("Binary Points Limit Test:"),console.log(`Max Points Per Side: ${n}`),console.log(`Current Left Points: ${c}`),console.log(`Current Right Points: ${p}`),console.log(`Test Side: ${a}`),console.log(`Test Points to Add: ${i}`);let m=0,f=!1,h="";if("LEFT"===a?c>=n?(h=`Left side has reached maximum (${c}/${n}). No points can be added.`,f=!1):(m=Math.min(i,n-c),f=!0,h=`Can add ${m} points to left side (${m<i?"capped at limit":"full amount"})`):p>=n?(h=`Right side has reached maximum (${p}/${n}). No points can be added.`,f=!1):(m=Math.min(i,n-p),f=!0,h=`Can add ${m} points to right side (${m<i?"capped at limit":"full amount"})`),console.log(`Result: ${h}`),f&&m>0){let e="LEFT"===a?{leftPoints:m}:{rightPoints:m};await d.FW.upsert({userId:s,...e}),console.log(`Successfully added ${m} points to ${a} side for user ${s}`)}let y=await d.FW.findByUserId(s);return o.NextResponse.json({success:!0,message:"Binary points limit test completed",data:{settings:{maxPointsPerSide:n},before:{leftPoints:c,rightPoints:p},test:{side:a,requestedPoints:i,pointsAdded:m,canAddPoints:f,reason:h},after:{leftPoints:y?.leftPoints||0,rightPoints:y?.rightPoints||0}}})}catch(e){return console.error("Binary points limit test error:",e),o.NextResponse.json({success:!1,error:"Failed to test binary points limit"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/binary-points/test-limit/route",pathname:"/api/admin/binary-points/test-limit",filename:"route",bundlePath:"app/api/admin/binary-points/test-limit/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\test-limit\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=c;function h(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306],()=>r(71088));module.exports=s})();