"use strict";(()=>{var e={};e.id=8401,e.ids=[8401],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>I,DY:()=>w,Lx:()=>h,Oj:()=>y,b9:()=>f,qc:()=>x});var a=r(85663),s=r(43205),n=r.n(s),i=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await a.Ay.hash(e,12),d=async(e,t)=>await a.Ay.compare(e,t),c=e=>n().sign(e,o,{expiresIn:u}),p=e=>{try{return n().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await i.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},w=async e=>{let t,a;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await i.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await l(e.password),n=!1;do a=m(),n=!await i.Gy.findByReferralId(a);while(!n);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},I=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},17732:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>c,POST:()=>d});var s=r(96559),n=r(48088),i=r(37719),o=r(32190),u=r(12909),l=r(6710);async function d(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userId:a,amount:s,type:n,reason:i,description:d}=await e.json();if(!a||!s||!n||!i)return o.NextResponse.json({success:!1,error:"Missing required fields: userId, amount, type, reason"},{status:400});if(!["CREDIT","DEBIT"].includes(n))return o.NextResponse.json({success:!1,error:"Type must be either CREDIT or DEBIT"},{status:400});let c=parseFloat(s);if(isNaN(c)||c<=0)return o.NextResponse.json({success:!1,error:"Amount must be a positive number"},{status:400});let p=await l.Gy.findById(a);if(!p)return o.NextResponse.json({success:!1,error:"User not found"},{status:404});let m=await l.k_.getOrCreate(a),f="CREDIT"===n?c:-c,w=m.availableBalance+f;if("DEBIT"===n&&w<0)return o.NextResponse.json({success:!1,error:`Insufficient balance. Current: ${m.availableBalance}, Requested debit: ${c}`},{status:400});await l.k_.updateBalance(a,{availableBalance:w});let h="CREDIT"===n?"ADMIN_CREDIT":"ADMIN_DEBIT",y=d||`Admin ${n.toLowerCase()}: ${i}`,I=await l.DR.create({userId:a,type:h,amount:f,description:y,status:"COMPLETED"});return await l.AJ.create({action:"WALLET_ADJUSTMENT",adminId:r.id,userId:a,details:{type:n,amount:c,reason:i,description:y,previousBalance:m.availableBalance,newBalance:w,transactionId:I.id,targetUser:{id:p.id,email:p.email,name:`${p.firstName} ${p.lastName}`}},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:`Wallet ${n.toLowerCase()} completed successfully`,data:{transactionId:I.id,userId:a,type:n,amount:c,previousBalance:m.availableBalance,newBalance:w,reason:i,processedBy:r.email,processedAt:new Date}})}catch(t){console.error("Wallet adjustment error:",t);try{let{user:r}=await (0,u.b9)(e);r&&await l.AJ.create({action:"WALLET_ADJUSTMENT_ERROR",adminId:r.id,details:{error:t instanceof Error?t.message:"Unknown error",stack:t instanceof Error?t.stack:void 0},ipAddress:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"})}catch(e){console.error("Failed to log wallet adjustment error:",e)}return o.NextResponse.json({success:!1,error:"Failed to adjust wallet balance"},{status:500})}}async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),s=a.get("userId"),n=parseInt(a.get("limit")||"50"),i=parseInt(a.get("offset")||"0"),d=await l.DR.findByUserId(s||"",{types:["ADMIN_CREDIT","ADMIN_DEBIT"],limit:Math.min(n,100),offset:i,includeUser:!0});return o.NextResponse.json({success:!0,data:{transactions:d.map(e=>({id:e.id,userId:e.userId,type:e.type,amount:e.amount,description:e.description,status:e.status,createdAt:e.createdAt,user:e.user?{email:e.user.email,firstName:e.user.firstName,lastName:e.user.lastName}:null})),pagination:{limit:n,offset:i,hasMore:d.length===n}}})}catch(e){return console.error("Wallet adjustment history error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch wallet adjustment history"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/wallet/adjust/route",pathname:"/api/admin/wallet/adjust",filename:"route",bundlePath:"app/api/admin/wallet/adjust/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\wallet\\adjust\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:w}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(17732));module.exports=a})();