'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui';
import { Container, Grid, Flex, PublicLayout } from '@/components/layout';
import { ArrowRight, UserPlus, Shield, DollarSign, TrendingUp } from 'lucide-react';

export default function HowItWorksPage() {
  return (
    <PublicLayout>

      {/* Hero Section */}
      <section className="py-12 md:py-20 bg-gradient-to-br from-solar-50 to-eco-50">
        <Container>
          <div className="text-center max-w-4xl mx-auto px-4">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-dark-900 mb-4 md:mb-6">
              How <span className="text-solar-500">HashCoreX</span> Works
            </h1>
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed">
              Simple, transparent, and profitable. Learn how our solar-powered
              mining platform generates real returns for investors.
            </p>
          </div>
        </Container>
      </section>

      {/* Process Steps */}
      <section className="py-20">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark-900 mb-4">
              Get Started in 4 Simple Steps
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              From registration to earning, here&apos;s how you can start mining
              with renewable energy.
            </p>
          </div>

          <div className="max-w-5xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Step 1 */}
              <div className="text-center relative">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-solar-100 rounded-full mb-6">
                  <UserPlus className="h-10 w-10 text-solar-600" />
                </div>
                <h3 className="text-xl font-semibold text-dark-900 mb-3">1. Register</h3>
                <p className="text-gray-600 mb-4">
                  Create your account with email verification and secure your profile.
                </p>
                <div className="hidden lg:block absolute top-10 -right-4 text-solar-300">
                  <ArrowRight className="h-6 w-6" />
                </div>
              </div>

              {/* Step 2 */}
              <div className="text-center relative">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-eco-100 rounded-full mb-6">
                  <Shield className="h-10 w-10 text-eco-600" />
                </div>
                <h3 className="text-xl font-semibold text-dark-900 mb-3">2. Verify</h3>
                <p className="text-gray-600 mb-4">
                  Complete KYC verification by uploading your ID and selfie for security.
                </p>
                <div className="hidden lg:block absolute top-10 -right-4 text-eco-300">
                  <ArrowRight className="h-6 w-6" />
                </div>
              </div>

              {/* Step 3 */}
              <div className="text-center relative">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-solar-100 rounded-full mb-6">
                  <DollarSign className="h-10 w-10 text-solar-600" />
                </div>
                <h3 className="text-xl font-semibold text-dark-900 mb-3">3. Buy TH/s</h3>
                <p className="text-gray-600 mb-4">
                  Purchase mining power starting from $50 using USDT (TRC20).
                </p>
                <div className="hidden lg:block absolute top-10 -right-4 text-solar-300">
                  <ArrowRight className="h-6 w-6" />
                </div>
              </div>

              {/* Step 4 */}
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-eco-100 rounded-full mb-6">
                  <TrendingUp className="h-10 w-10 text-eco-600" />
                </div>
                <h3 className="text-xl font-semibold text-dark-900 mb-3">4. Earn</h3>
                <p className="text-gray-600 mb-4">
                  Receive daily mining returns and weekly payouts to your wallet.
                </p>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* ROI Explanation */}
      <section className="py-20 bg-gray-50">
        <Container>
          <Grid cols={{ default: 1, lg: 2 }} gap={12} className="items-center">
            <div>
              <h2 className="text-4xl font-bold text-dark-900 mb-6">
                Understanding Your Returns
              </h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-solar-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    1
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-dark-900 mb-2">Daily Mining ROI</h3>
                    <p className="text-gray-600">
                      Earn 0.3% - 0.7% daily returns based on actual mining performance
                      from our solar-powered facilities. Higher TH/s amounts get better rates.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-eco-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    2
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-dark-900 mb-2">Weekly Payouts</h3>
                    <p className="text-gray-600">
                      All accumulated earnings are transferred to your wallet every 
                      Saturday at 15:00 UTC automatically.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-solar-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    3
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-dark-900 mb-2">12-Month Duration</h3>
                    <p className="text-gray-600">
                      Mining units are active for 12 months or until you earn 5x 
                      your initial investment, whichever comes first.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-dark-900 mb-6 text-center">
                Example Investment
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600">Investment Amount</span>
                  <span className="font-semibold text-dark-900">$1,000</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600">TH/s Purchased</span>
                  <span className="font-semibold text-dark-900">20 TH/s</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600">Daily ROI (0.4-0.6%)</span>
                  <span className="font-semibold text-eco-600">$4.00-$6.00</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-600">Weekly Earnings</span>
                  <span className="font-semibold text-eco-600">$28.00-$42.00</span>
                </div>
                <div className="flex justify-between items-center py-3 bg-solar-50 rounded-lg px-4">
                  <span className="text-gray-600">Monthly Potential</span>
                  <span className="font-bold text-solar-600 text-lg">$120.00-$180.00</span>
                </div>
              </div>
            </div>
          </Grid>
        </Container>
      </section>

      {/* Binary Network */}
      <section className="py-20">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark-900 mb-4">
              Binary Referral Network
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Earn additional income by building your referral network with our 
              innovative binary system.
            </p>
          </div>

          <Grid cols={{ default: 1, lg: 3 }} gap={8}>
            <div className="text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-solar-100 rounded-full mb-4">
                <span className="text-2xl font-bold text-solar-600">10%</span>
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Direct Referral</h3>
              <p className="text-gray-600">
                Earn 10% commission on every direct referral&apos;s investment immediately.
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-eco-100 rounded-full mb-4">
                <span className="text-2xl font-bold text-eco-600">$10</span>
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Fixed Matching</h3>
              <p className="text-gray-600">
                Each matched binary point earns exactly $10 in fixed rewards.
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-solar-100 rounded-full mb-4">
                <span className="text-lg font-bold text-solar-600">Daily</span>
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Binary Matching</h3>
              <p className="text-gray-600">
                Weekly matching every Saturday at 15:00 UTC with up to 2,000 points per side.
              </p>
            </div>
          </Grid>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-solar-500 to-eco-500 text-white">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Ready to Start Mining?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of investors earning daily returns with sustainable mining. 
              Get started in just a few minutes.
            </p>
            <Flex justify="center" gap={4} className="flex-col sm:flex-row">
              <Link href="/register">
                <Button 
                  size="xl" 
                  variant="secondary"
                  className="bg-white text-dark-900 hover:bg-gray-100 w-full sm:w-auto"
                >
                  Create Account
                </Button>
              </Link>
              <Link href="/about">
                <Button 
                  size="xl" 
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-dark-900 w-full sm:w-auto"
                >
                  Learn More
                </Button>
              </Link>
            </Flex>
          </div>
        </Container>
      </section>
    </PublicLayout>
  );
}
