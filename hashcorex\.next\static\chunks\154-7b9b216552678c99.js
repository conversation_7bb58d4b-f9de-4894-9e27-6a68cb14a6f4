"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[154],{394:(t,e,n)=>{function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}n.d(e,{A:()=>i})},469:(t,e,n)=>{n.d(e,{G:()=>C});var r,i,o,a=n(4498),u=n(1235),l=0,s=0,c=0,f=0,h=0,d=0,p="object"==typeof performance&&performance.now?performance:Date,m="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function y(){return h||(m(v),h=p.now()+d)}function v(){h=0}function g(){this._call=this._time=this._next=null}function _(t,e,n){var r=new g;return r.restart(t,e,n),r}function x(){h=(f=p.now())+d,l=s=0;try{y(),++l;for(var t,e=i;e;)(t=h-e._time)>=0&&e._call.call(void 0,t),e=e._next;--l}finally{l=0,function(){for(var t,e,n=i,r=1/0;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:i=e);o=t,b(r)}(),h=0}}function w(){var t=p.now(),e=t-f;e>1e3&&(d-=e,f=t)}function b(t){!l&&(s&&(s=clearTimeout(s)),t-h>24?(t<1/0&&(s=setTimeout(x,t-p.now()-d)),c&&(c=clearInterval(c))):(c||(f=p.now(),c=setInterval(w,1e3)),l=1,m(x)))}function A(t,e,n){var r=new g;return e=null==e?0:+e,r.restart(n=>{r.stop(),t(n+e)},e,n),r}g.prototype=_.prototype={constructor:g,restart:function(t,e,n){if("function"!=typeof t)throw TypeError("callback is not a function");n=(null==n?y():+n)+(null==e?0:+e),this._next||o===this||(o?o._next=this:i=this,o=this),this._call=t,this._time=n,b()},stop:function(){this._call&&(this._call=null,this._time=1/0,b())}};var k=(0,u.A)("start","end","cancel","interrupt"),M=[];function z(t,e,n,r,i,o){var a=t.__transition;if(a){if(n in a)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function o(l){var s,c,f,h;if(1!==n.state)return u();for(s in i)if((h=i[s]).name===n.name){if(3===h.state)return A(o);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[s]):+s<e&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[s])}if(A(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(l))}),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(s=0,n.state=3,r=Array(f=n.tween.length),c=-1;s<f;++s)(h=n.tween[s].value.call(t,t.__data__,n.index,n.group))&&(r[++c]=h);r.length=c+1}}function a(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(u),n.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),u())}function u(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=_(function(t){n.state=1,n.timer.restart(o,n.delay,n.time),n.delay<=t&&o(t-n.delay)},0,n.time)}(t,n,{name:e,index:r,group:i,on:k,tween:M,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function E(t,e){var n=j(t,e);if(n.state>0)throw Error("too late; already scheduled");return n}function S(t,e){var n=j(t,e);if(n.state>3)throw Error("too late; already running");return n}function j(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw Error("transition not found");return n}function C(t,e){var n,r,i,o=t.__transition,a=!0;if(o){for(i in e=null==e?null:e+"",o){if((n=o[i]).name!==e){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete o[i]}a&&delete t.__transition}}function P(t,e){return t*=1,e*=1,function(n){return t*(1-n)+e*n}}var N=180/Math.PI,O={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function T(t,e,n,r,i,o){var a,u,l;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(l=t*n+e*r)&&(n-=t*l,r-=e*l),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,l/=u),t*r<e*n&&(t=-t,e=-e,l=-l,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(e,t)*N,skewX:Math.atan(l)*N,scaleX:a,scaleY:u}}function R(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u,l,s,c,f=[],h=[];return o=t(o),a=t(a),!function(t,r,i,o,a,u){if(t!==i||r!==o){var l=a.push("translate(",null,e,null,n);u.push({i:l-4,x:P(t,i)},{i:l-2,x:P(r,o)})}else(i||o)&&a.push("translate("+i+e+o+n)}(o.translateX,o.translateY,a.translateX,a.translateY,f,h),u=o.rotate,l=a.rotate,u!==l?(u-l>180?l+=360:l-u>180&&(u+=360),h.push({i:f.push(i(f)+"rotate(",null,r)-2,x:P(u,l)})):l&&f.push(i(f)+"rotate("+l+r),s=o.skewX,c=a.skewX,s!==c?h.push({i:f.push(i(f)+"skewX(",null,r)-2,x:P(s,c)}):c&&f.push(i(f)+"skewX("+c+r),!function(t,e,n,r,o,a){if(t!==n||e!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:P(t,n)},{i:u-2,x:P(e,r)})}else(1!==n||1!==r)&&o.push(i(o)+"scale("+n+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,f,h),o=a=null,function(t){for(var e,n=-1,r=h.length;++n<r;)f[(e=h[n]).i]=e.x(t);return f.join("")}}}var $=R(function(t){let e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?O:T(e.a,e.b,e.c,e.d,e.e,e.f)},"px, ","px)","deg)"),I=R(function(t){return null==t?O:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?T((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):O},", ",")",")"),q=n(6102);function D(t,e,n){var r=t._id;return t.each(function(){var t=S(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)}),function(t){return j(t,r).value[e]}}function B(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function X(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function L(){}var Y="\\s*([+-]?\\d+)\\s*",V="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",H="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",G=/^#([0-9a-f]{3,8})$/,U=RegExp(`^rgb\\(${Y},${Y},${Y}\\)$`),F=RegExp(`^rgb\\(${H},${H},${H}\\)$`),W=RegExp(`^rgba\\(${Y},${Y},${Y},${V}\\)$`),K=RegExp(`^rgba\\(${H},${H},${H},${V}\\)$`),J=RegExp(`^hsl\\(${V},${H},${H}\\)$`),Q=RegExp(`^hsla\\(${V},${H},${H},${V}\\)$`),Z={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function tt(){return this.rgb().formatHex()}function te(){return this.rgb().formatRgb()}function tn(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=G.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?tr(e):3===n?new ta(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?ti(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?ti(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=U.exec(t))?new ta(e[1],e[2],e[3],1):(e=F.exec(t))?new ta(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=W.exec(t))?ti(e[1],e[2],e[3],e[4]):(e=K.exec(t))?ti(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=J.exec(t))?th(e[1],e[2]/100,e[3]/100,1):(e=Q.exec(t))?th(e[1],e[2]/100,e[3]/100,e[4]):Z.hasOwnProperty(t)?tr(Z[t]):"transparent"===t?new ta(NaN,NaN,NaN,0):null}function tr(t){return new ta(t>>16&255,t>>8&255,255&t,1)}function ti(t,e,n,r){return r<=0&&(t=e=n=NaN),new ta(t,e,n,r)}function to(t,e,n,r){var i;return 1==arguments.length?((i=t)instanceof L||(i=tn(i)),i)?new ta((i=i.rgb()).r,i.g,i.b,i.opacity):new ta:new ta(t,e,n,null==r?1:r)}function ta(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function tu(){return`#${tf(this.r)}${tf(this.g)}${tf(this.b)}`}function tl(){let t=ts(this.opacity);return`${1===t?"rgb(":"rgba("}${tc(this.r)}, ${tc(this.g)}, ${tc(this.b)}${1===t?")":`, ${t})`}`}function ts(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function tc(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function tf(t){return((t=tc(t))<16?"0":"")+t.toString(16)}function th(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new tp(t,e,n,r)}function td(t){if(t instanceof tp)return new tp(t.h,t.s,t.l,t.opacity);if(t instanceof L||(t=tn(t)),!t)return new tp;if(t instanceof tp)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),o=Math.max(e,n,r),a=NaN,u=o-i,l=(o+i)/2;return u?(a=e===o?(n-r)/u+(n<r)*6:n===o?(r-e)/u+2:(e-n)/u+4,u/=l<.5?o+i:2-o-i,a*=60):u=l>0&&l<1?0:a,new tp(a,u,l,t.opacity)}function tp(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function tm(t){return(t=(t||0)%360)<0?t+360:t}function ty(t){return Math.max(0,Math.min(1,t||0))}function tv(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}function tg(t,e,n,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*n+(1+3*t+3*o-3*a)*r+a*i)/6}B(L,tn,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:tt,formatHex:tt,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return td(this).formatHsl()},formatRgb:te,toString:te}),B(ta,to,X(L,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new ta(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new ta(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new ta(tc(this.r),tc(this.g),tc(this.b),ts(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:tu,formatHex:tu,formatHex8:function(){return`#${tf(this.r)}${tf(this.g)}${tf(this.b)}${tf((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:tl,toString:tl})),B(tp,function(t,e,n,r){return 1==arguments.length?td(t):new tp(t,e,n,null==r?1:r)},X(L,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tp(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tp(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new ta(tv(t>=240?t-240:t+120,i,r),tv(t,i,r),tv(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new tp(tm(this.h),ty(this.s),ty(this.l),ts(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=ts(this.opacity);return`${1===t?"hsl(":"hsla("}${tm(this.h)}, ${100*ty(this.s)}%, ${100*ty(this.l)}%${1===t?")":`, ${t})`}`}}));let t_=t=>()=>t;function tx(t,e){var n,r,i=e-t;return i?(n=t,r=i,function(t){return n+t*r}):t_(isNaN(t)?e:t)}let tw=function t(e){var n,r=1==(n=+e)?tx:function(t,e){var r,i,o;return e-t?(r=t,i=e,r=Math.pow(r,o=n),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):t_(isNaN(t)?e:t)};function i(t,e){var n=r((t=to(t)).r,(e=to(e)).r),i=r(t.g,e.g),o=r(t.b,e.b),a=tx(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function tb(t){return function(e){var n,r,i=e.length,o=Array(i),a=Array(i),u=Array(i);for(n=0;n<i;++n)r=to(e[n]),o[n]=r.r||0,a[n]=r.g||0,u[n]=r.b||0;return o=t(o),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=u(t),r+""}}}tb(function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],o=t[r+1],a=r>0?t[r-1]:2*i-o,u=r<e-1?t[r+2]:2*o-i;return tg((n-r/e)*e,a,i,o,u)}}),tb(function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),i=t[(r+e-1)%e],o=t[r%e],a=t[(r+1)%e],u=t[(r+2)%e];return tg((n-r/e)*e,i,o,a,u)}});var tA=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tk=RegExp(tA.source,"g");function tM(t,e){var n;return("number"==typeof e?P:e instanceof tn?tw:(n=tn(e))?(e=n,tw):function(t,e){var n,r,i,o,a,u=tA.lastIndex=tk.lastIndex=0,l=-1,s=[],c=[];for(t+="",e+="";(i=tA.exec(t))&&(o=tk.exec(e));)(a=o.index)>u&&(a=e.slice(u,a),s[l]?s[l]+=a:s[++l]=a),(i=i[0])===(o=o[0])?s[l]?s[l]+=o:s[++l]=o:(s[++l]=null,c.push({i:l,x:P(i,o)})),u=tk.lastIndex;return u<e.length&&(a=e.slice(u),s[l]?s[l]+=a:s[++l]=a),s.length<2?c[0]?(n=c[0].x,function(t){return n(t)+""}):(r=e,function(){return r}):(e=c.length,function(t){for(var n,r=0;r<e;++r)s[(n=c[r]).i]=n.x(t);return s.join("")})})(t,e)}var tz=n(3875),tE=n(394),tS=n(9293),tj=a.Ay.prototype.constructor,tC=n(1774);function tP(t){return function(){this.style.removeProperty(t)}}var tN=0;function tO(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}var tT=a.Ay.prototype;tO.prototype=(function(t){return(0,a.Ay)().transition(t)}).prototype={constructor:tO,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,tE.A)(t));for(var r=this._groups,i=r.length,o=Array(i),a=0;a<i;++a)for(var u,l,s=r[a],c=s.length,f=o[a]=Array(c),h=0;h<c;++h)(u=s[h])&&(l=t.call(u,u.__data__,h,s))&&("__data__"in u&&(l.__data__=u.__data__),f[h]=l,z(f[h],e,n,h,f,j(u,n)));return new tO(o,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,tS.A)(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var l,s=r[u],c=s.length,f=0;f<c;++f)if(l=s[f]){for(var h,d=t.call(l,l.__data__,f,s),p=j(l,n),m=0,y=d.length;m<y;++m)(h=d[m])&&z(h,e,n,m,d,p);o.push(d),a.push(l)}return new tO(o,a,e,n)},selectChild:tT.selectChild,selectChildren:tT.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,tz.A)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a=e[i],u=a.length,l=r[i]=[],s=0;s<u;++s)(o=a[s])&&t.call(o,o.__data__,s,a)&&l.push(o);return new tO(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var e=this._groups,n=t._groups,r=e.length,i=n.length,o=Math.min(r,i),a=Array(r),u=0;u<o;++u)for(var l,s=e[u],c=n[u],f=s.length,h=a[u]=Array(f),d=0;d<f;++d)(l=s[d]||c[d])&&(h[d]=l);for(;u<r;++u)a[u]=e[u];return new tO(a,this._parents,this._name,this._id)},selection:function(){return new tj(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=++tN,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],l=u.length,s=0;s<l;++s)if(a=u[s]){var c=j(a,e);z(a,t,n,s,u,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new tO(r,this._parents,t,n)},call:tT.call,nodes:tT.nodes,node:tT.node,size:tT.size,empty:tT.empty,each:tT.each,on:function(t,e){var n,r,i,o,a,u,l=this._id;return arguments.length<2?j(this.node(),l).on.on(t):this.each((n=l,r=t,i=e,u=(r+"").trim().split(/^|\s+/).every(function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t})?E:S,function(){var t=u(this,n),e=t.on;e!==o&&(a=(o=e).copy()).on(r,i),t.on=a}))},attr:function(t,e){var n=(0,q.A)(t),r="transform"===n?I:tM;return this.attrTween(t,"function"==typeof e?(n.local?function(t,e,n){var r,i,o;return function(){var a,u,l=n(this);return null==l?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local))===(u=l+"")?null:a===r&&u===i?o:(i=u,o=e(r=a,l))}}:function(t,e,n){var r,i,o;return function(){var a,u,l=n(this);return null==l?void this.removeAttribute(t):(a=this.getAttribute(t))===(u=l+"")?null:a===r&&u===i?o:(i=u,o=e(r=a,l))}})(n,r,D(this,"attr."+t,e)):null==e?(n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(n):(n.local?function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=e(r=a,n)}}:function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=e(r=a,n)}})(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw Error();var r=(0,q.A)(t);return this.tween(n,(r.local?function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttributeNS(t.space,t.local,i.call(this,e))}),n}return i._value=e,i}:function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttribute(t,i.call(this,e))}),n}return i._value=e,i})(r,e))},style:function(t,e,n){var r,i,o,a,u,l,s,c,f,h,d,p,m,y,v,g,_,x,w,b,A,k="transform"==(t+="")?$:tM;return null==e?this.styleTween(t,(r=t,function(){var t=(0,tC.j)(this,r),e=(this.style.removeProperty(r),(0,tC.j)(this,r));return t===e?null:t===i&&e===o?a:a=k(i=t,o=e)})).on("end.style."+t,tP(t)):"function"==typeof e?this.styleTween(t,(u=t,l=D(this,"style."+t,e),function(){var t=(0,tC.j)(this,u),e=l(this),n=e+"";return null==e&&(this.style.removeProperty(u),n=e=(0,tC.j)(this,u)),t===n?null:t===s&&n===c?f:(c=n,f=k(s=t,e))})).each((h=this._id,_="end."+(g="style."+(d=t)),function(){var t=S(this,h),e=t.on,n=null==t.value[g]?v||(v=tP(d)):void 0;(e!==p||y!==n)&&(m=(p=e).copy()).on(_,y=n),t.on=m})):this.styleTween(t,(x=t,A=e+"",function(){var t=(0,tC.j)(this,x);return t===A?null:t===w?b:b=k(w=t,e)}),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw Error();return this.tween(r,function(t,e,n){var r,i;function o(){var o=e.apply(this,arguments);return o!==i&&(r=(i=o)&&function(e){this.style.setProperty(t,o.call(this,e),n)}),r}return o._value=e,o}(t,e,null==n?"":n))},text:function(t){var e,n;return this.tween("text","function"==typeof t?(e=D(this,"text",t),function(){var t=e(this);this.textContent=null==t?"":t}):(n=null==t?"":t+"",function(){this.textContent=n}))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw Error();return this.tween(e,function(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&function(t){this.textContent=r.call(this,t)}),e}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=j(this.node(),n).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==e?function(t,e){var n,r;return function(){var i=S(this,t),o=i.tween;if(o!==n){r=n=o;for(var a=0,u=r.length;a<u;++a)if(r[a].name===e){(r=r.slice()).splice(a,1);break}}i.tween=r}}:function(t,e,n){var r,i;if("function"!=typeof n)throw Error();return function(){var o=S(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:e,value:n},l=0,s=i.length;l<s;++l)if(i[l].name===e){i[l]=u;break}l===s&&i.push(u)}o.tween=i}})(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){E(this,t).delay=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){E(this,t).delay=e}})(e,t)):j(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){S(this,t).duration=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){S(this,t).duration=e}})(e,t)):j(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw Error();return function(){S(this,t).ease=e}}(e,t)):j(this.node(),e).ease},easeVarying:function(t){var e;if("function"!=typeof t)throw Error();return this.each((e=this._id,function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw Error();S(this,e).ease=n}))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise(function(o,a){var u={value:a},l={value:function(){0==--i&&o()}};n.each(function(){var n=S(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(u),e._.interrupt.push(u),e._.end.push(l)),n.on=e}),0===i&&o()})},[Symbol.iterator]:tT[Symbol.iterator]};var tR={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};a.Ay.prototype.interrupt=function(t){return this.each(function(){C(this,t)})},a.Ay.prototype.transition=function(t){var e,n;t instanceof tO?(e=t._id,t=t._name):(e=++tN,(n=tR).time=y(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],l=u.length,s=0;s<l;++s)(a=u[s])&&z(a,t,e,s,u,n||function(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw Error(`transition ${e} not found`);return n}(a,e));return new tO(r,this._parents,t,e)}},901:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(8229)._(n(2115)).default.createContext(null)},978:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("arrow-down-left",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]])},1193:(t,e)=>{function n(t){var e;let{config:n,src:r,width:i,quality:o}=t,a=o||(null==(e=n.qualities)?void 0:e.reduce((t,e)=>Math.abs(e-75)<Math.abs(t-75)?e:t))||75;return n.path+"?url="+encodeURIComponent(r)+"&w="+i+"&q="+a+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return r}}),n.__next_img_default=!0;let r=n},1235:(t,e,n)=>{n.d(e,{A:()=>u});var r={value:()=>{}};function i(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new o(r)}function o(t){this._=t}function a(t,e,n){for(var i=0,o=t.length;i<o;++i)if(t[i].name===e){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=n&&t.push({name:e,value:n}),t}o.prototype=i.prototype={constructor:o,on:function(t,e){var n,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:e}}),o=-1,u=i.length;if(arguments.length<2){for(;++o<u;)if((n=(t=i[o]).type)&&(n=function(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}(r[n],t.name)))return n;return}if(null!=e&&"function"!=typeof e)throw Error("invalid callback: "+e);for(;++o<u;)if(n=(t=i[o]).type)r[n]=a(r[n],t.name,e);else if(null==e)for(n in r)r[n]=a(r[n],t.name,null);return this},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new o(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=Array(n),o=0;o<n;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,n=r.length;o<n;++o)r[o].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}};let u=i},1264:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImgProps",{enumerable:!0,get:function(){return l}}),n(3230);let r=n(5100),i=n(5840),o=["-moz-initial","fill","none","scale-down",void 0];function a(t){return void 0!==t.default}function u(t){return void 0===t?t:"number"==typeof t?Number.isFinite(t)?t:NaN:"string"==typeof t&&/^[0-9]+$/.test(t)?parseInt(t,10):NaN}function l(t,e){var n,l;let s,c,f,{src:h,sizes:d,unoptimized:p=!1,priority:m=!1,loading:y,className:v,quality:g,width:_,height:x,fill:w=!1,style:b,overrideSrc:A,onLoad:k,onLoadingComplete:M,placeholder:z="empty",blurDataURL:E,fetchPriority:S,decoding:j="async",layout:C,objectFit:P,objectPosition:N,lazyBoundary:O,lazyRoot:T,...R}=t,{imgConf:$,showAltText:I,blurComplete:q,defaultLoader:D}=e,B=$||i.imageConfigDefault;if("allSizes"in B)s=B;else{let t=[...B.deviceSizes,...B.imageSizes].sort((t,e)=>t-e),e=B.deviceSizes.sort((t,e)=>t-e),r=null==(n=B.qualities)?void 0:n.sort((t,e)=>t-e);s={...B,allSizes:t,deviceSizes:e,qualities:r}}if(void 0===D)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let X=R.loader||D;delete R.loader,delete R.srcSet;let L="__next_img_default"in X;if(L){if("custom"===s.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let t=X;X=e=>{let{config:n,...r}=e;return t(r)}}if(C){"fill"===C&&(w=!0);let t={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[C];t&&(b={...b,...t});let e={responsive:"100vw",fill:"100vw"}[C];e&&!d&&(d=e)}let Y="",V=u(_),H=u(x);if((l=h)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let t=a(h)?h.default:h;if(!t.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(t)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!t.height||!t.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(t)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=t.blurWidth,f=t.blurHeight,E=E||t.blurDataURL,Y=t.src,!w)if(V||H){if(V&&!H){let e=V/t.width;H=Math.round(t.height*e)}else if(!V&&H){let e=H/t.height;V=Math.round(t.width*e)}}else V=t.width,H=t.height}let G=!m&&("lazy"===y||void 0===y);(!(h="string"==typeof h?h:Y)||h.startsWith("data:")||h.startsWith("blob:"))&&(p=!0,G=!1),s.unoptimized&&(p=!0),L&&!s.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(p=!0);let U=u(g),F=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:P,objectPosition:N}:{},I?{}:{color:"transparent"},b),W=q||"empty"===z?null:"blur"===z?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:V,heightInt:H,blurWidth:c,blurHeight:f,blurDataURL:E||"",objectFit:F.objectFit})+'")':'url("'+z+'")',K=o.includes(F.objectFit)?"fill"===F.objectFit?"100% 100%":"cover":F.objectFit,J=W?{backgroundSize:K,backgroundPosition:F.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:W}:{},Q=function(t){let{config:e,src:n,unoptimized:r,width:i,quality:o,sizes:a,loader:u}=t;if(r)return{src:n,srcSet:void 0,sizes:void 0};let{widths:l,kind:s}=function(t,e,n){let{deviceSizes:r,allSizes:i}=t;if(n){let t=/(^|\s)(1?\d?\d)vw/g,e=[];for(let r;r=t.exec(n);)e.push(parseInt(r[2]));if(e.length){let t=.01*Math.min(...e);return{widths:i.filter(e=>e>=r[0]*t),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof e?{widths:r,kind:"w"}:{widths:[...new Set([e,2*e].map(t=>i.find(e=>e>=t)||i[i.length-1]))],kind:"x"}}(e,i,a),c=l.length-1;return{sizes:a||"w"!==s?a:"100vw",srcSet:l.map((t,r)=>u({config:e,src:n,quality:o,width:t})+" "+("w"===s?t:r+1)+s).join(", "),src:u({config:e,src:n,quality:o,width:l[c]})}}({config:s,src:h,unoptimized:p,width:V,quality:U,sizes:d,loader:X});return{props:{...R,loading:G?"lazy":y,fetchPriority:S,width:V,height:H,decoding:j,className:v,style:{...F,...J},sizes:Q.sizes,srcSet:Q.srcSet,src:A||Q.src},meta:{unoptimized:p,priority:m,placeholder:z,fill:w}}}},1469:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}(e,{default:function(){return l},getImageProps:function(){return u}});let r=n(8229),i=n(1264),o=n(3063),a=r._(n(1193));function u(t){let{props:e}=(0,i.getImgProps)(t,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,n]of Object.entries(e))void 0===n&&delete e[t];return{props:e}}let l=o.Image},1774:(t,e,n)=>{n.d(e,{A:()=>i,j:()=>o});var r=n(7271);function i(t,e,n){return arguments.length>1?this.each((null==e?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof e?function(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}:function(t,e,n){return function(){this.style.setProperty(t,e,n)}})(t,e,null==n?"":n)):o(this.node(),t)}function o(t,e){return t.style.getPropertyValue(e)||(0,r.A)(t).getComputedStyle(t,null).getPropertyValue(e)}},2464:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=n(8229)._(n(2115)).default.createContext({})},2903:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(4498);function i(t){return"string"==typeof t?new r.LN([[document.querySelector(t)]],[document.documentElement]):new r.LN([[t]],r.zr)}},3063:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Image",{enumerable:!0,get:function(){return x}});let r=n(8229),i=n(6966),o=n(5155),a=i._(n(2115)),u=r._(n(7650)),l=r._(n(5564)),s=n(1264),c=n(5840),f=n(6752);n(3230);let h=n(901),d=r._(n(1193)),p=n(6654),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function y(t,e,n,r,i,o,a){let u=null==t?void 0:t.src;t&&t["data-loaded-src"]!==u&&(t["data-loaded-src"]=u,("decode"in t?t.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(t.parentElement&&t.isConnected){if("empty"!==e&&i(!0),null==n?void 0:n.current){let e=new Event("load");Object.defineProperty(e,"target",{writable:!1,value:t});let r=!1,i=!1;n.current({...e,nativeEvent:e,currentTarget:t,target:t,isDefaultPrevented:()=>r,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{r=!0,e.preventDefault()},stopPropagation:()=>{i=!0,e.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(t)}}))}function v(t){return a.use?{fetchPriority:t}:{fetchpriority:t}}let g=(0,a.forwardRef)((t,e)=>{let{src:n,srcSet:r,sizes:i,height:u,width:l,decoding:s,className:c,style:f,fetchPriority:h,placeholder:d,loading:m,unoptimized:g,fill:_,onLoadRef:x,onLoadingCompleteRef:w,setBlurComplete:b,setShowAltText:A,sizesInput:k,onLoad:M,onError:z,...E}=t,S=(0,a.useCallback)(t=>{t&&(z&&(t.src=t.src),t.complete&&y(t,d,x,w,b,g,k))},[n,d,x,w,b,z,g,k]),j=(0,p.useMergedRef)(e,S);return(0,o.jsx)("img",{...E,...v(h),loading:m,width:l,height:u,decoding:s,"data-nimg":_?"fill":"1",className:c,style:f,sizes:i,srcSet:r,src:n,ref:j,onLoad:t=>{y(t.currentTarget,d,x,w,b,g,k)},onError:t=>{A(!0),"empty"!==d&&b(!0),z&&z(t)}})});function _(t){let{isAppRouter:e,imgAttributes:n}=t,r={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...v(n.fetchPriority)};return e&&u.default.preload?(u.default.preload(n.src,r),null):(0,o.jsx)(l.default,{children:(0,o.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...r},"__nimg-"+n.src+n.srcSet+n.sizes)})}let x=(0,a.forwardRef)((t,e)=>{let n=(0,a.useContext)(h.RouterContext),r=(0,a.useContext)(f.ImageConfigContext),i=(0,a.useMemo)(()=>{var t;let e=m||r||c.imageConfigDefault,n=[...e.deviceSizes,...e.imageSizes].sort((t,e)=>t-e),i=e.deviceSizes.sort((t,e)=>t-e),o=null==(t=e.qualities)?void 0:t.sort((t,e)=>t-e);return{...e,allSizes:n,deviceSizes:i,qualities:o}},[r]),{onLoad:u,onLoadingComplete:l}=t,p=(0,a.useRef)(u);(0,a.useEffect)(()=>{p.current=u},[u]);let y=(0,a.useRef)(l);(0,a.useEffect)(()=>{y.current=l},[l]);let[v,x]=(0,a.useState)(!1),[w,b]=(0,a.useState)(!1),{props:A,meta:k}=(0,s.getImgProps)(t,{defaultLoader:d.default,imgConf:i,blurComplete:v,showAltText:w});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(g,{...A,unoptimized:k.unoptimized,placeholder:k.placeholder,fill:k.fill,onLoadRef:p,onLoadingCompleteRef:y,setBlurComplete:x,setShowAltText:b,sizesInput:t.sizes,ref:e}),k.priority?(0,o.jsx)(_,{isAppRouter:!n,imgAttributes:A}):null]})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},3875:(t,e,n)=>{function r(t){return function(){return this.matches(t)}}function i(t){return function(e){return e.matches(t)}}n.d(e,{A:()=>r,j:()=>i})},4357:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4498:(t,e,n)=>{n.d(e,{LN:()=>q,Ay:()=>B,zr:()=>I});var r=n(394),i=n(9293),o=n(3875),a=Array.prototype.find;function u(){return this.firstElementChild}var l=Array.prototype.filter;function s(){return Array.from(this.children)}function c(t){return Array(t.length)}function f(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function h(t,e,n,r,i,o){for(var a,u=0,l=e.length,s=o.length;u<s;++u)(a=e[u])?(a.__data__=o[u],r[u]=a):n[u]=new f(t,o[u]);for(;u<l;++u)(a=e[u])&&(i[u]=a)}function d(t,e,n,r,i,o,a){var u,l,s,c=new Map,h=e.length,d=o.length,p=Array(h);for(u=0;u<h;++u)(l=e[u])&&(p[u]=s=a.call(l,l.__data__,u,e)+"",c.has(s)?i[u]=l:c.set(s,l));for(u=0;u<d;++u)s=a.call(t,o[u],u,o)+"",(l=c.get(s))?(r[u]=l,l.__data__=o[u],c.delete(s)):n[u]=new f(t,o[u]);for(u=0;u<h;++u)(l=e[u])&&c.get(p[u])===l&&(i[u]=l)}function p(t){return t.__data__}function m(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}f.prototype={constructor:f,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var y=n(6102),v=n(1774);function g(t){return t.trim().split(/^|\s+/)}function _(t){return t.classList||new x(t)}function x(t){this._node=t,this._names=g(t.getAttribute("class")||"")}function w(t,e){for(var n=_(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function b(t,e){for(var n=_(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function A(){this.textContent=""}function k(){this.innerHTML=""}function M(){this.nextSibling&&this.parentNode.appendChild(this)}function z(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}x.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var E=n(5131);function S(t){var e=(0,y.A)(t);return(e.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===E.g&&e.documentElement.namespaceURI===E.g?e.createElement(t):e.createElementNS(n,t)}})(e)}function j(){return null}function C(){var t=this.parentNode;t&&t.removeChild(this)}function P(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function N(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function O(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,o=e.length;r<o;++r)(n=e[r],t.type&&n.type!==t.type||n.name!==t.name)?e[++i]=n:this.removeEventListener(n.type,n.listener,n.options);++i?e.length=i:delete this.__on}}}function T(t,e,n){return function(){var r,i=this.__on,o=function(t){e.call(this,t,this.__data__)};if(i){for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=n),r.value=e;return}}this.addEventListener(t.type,o,n),r={type:t.type,name:t.name,value:e,listener:o,options:n},i?i.push(r):this.__on=[r]}}var R=n(7271);function $(t,e,n){var r=(0,R.A)(t),i=r.CustomEvent;"function"==typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}var I=[null];function q(t,e){this._groups=t,this._parents=e}function D(){return new q([[document.documentElement]],I)}q.prototype=D.prototype={constructor:q,select:function(t){"function"!=typeof t&&(t=(0,r.A)(t));for(var e=this._groups,n=e.length,i=Array(n),o=0;o<n;++o)for(var a,u,l=e[o],s=l.length,c=i[o]=Array(s),f=0;f<s;++f)(a=l[f])&&(u=t.call(a,a.__data__,f,l))&&("__data__"in a&&(u.__data__=a.__data__),c[f]=u);return new q(i,this._parents)},selectAll:function(t){if("function"==typeof t){var e;e=t,t=function(){var t;return t=e.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=(0,i.A)(t);for(var n=this._groups,r=n.length,o=[],a=[],u=0;u<r;++u)for(var l,s=n[u],c=s.length,f=0;f<c;++f)(l=s[f])&&(o.push(t.call(l,l.__data__,f,s)),a.push(l));return new q(o,a)},selectChild:function(t){var e;return this.select(null==t?u:(e="function"==typeof t?t:(0,o.j)(t),function(){return a.call(this.children,e)}))},selectChildren:function(t){var e;return this.selectAll(null==t?s:(e="function"==typeof t?t:(0,o.j)(t),function(){return l.call(this.children,e)}))},filter:function(t){"function"!=typeof t&&(t=(0,o.A)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var a,u=e[i],l=u.length,s=r[i]=[],c=0;c<l;++c)(a=u[c])&&t.call(a,a.__data__,c,u)&&s.push(a);return new q(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,p);var n=e?d:h,r=this._parents,i=this._groups;"function"!=typeof t&&(w=t,t=function(){return w});for(var o=i.length,a=Array(o),u=Array(o),l=Array(o),s=0;s<o;++s){var c=r[s],f=i[s],m=f.length,y="object"==typeof(x=t.call(c,c&&c.__data__,s,r))&&"length"in x?x:Array.from(x),v=y.length,g=u[s]=Array(v),_=a[s]=Array(v);n(c,f,g,_,l[s]=Array(m),y,e);for(var x,w,b,A,k=0,M=0;k<v;++k)if(b=g[k]){for(k>=M&&(M=k+1);!(A=_[M])&&++M<v;);b._next=A||null}}return(a=new q(a,r))._enter=u,a._exit=l,a},enter:function(){return new q(this._enter||this._groups.map(c),this._parents)},exit:function(){return new q(this._exit||this._groups.map(c),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(i=e(i))&&(i=i.selection()),null==n?o.remove():n(o),r&&i?r.merge(i).order():i},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,o=r.length,a=Math.min(i,o),u=Array(i),l=0;l<a;++l)for(var s,c=n[l],f=r[l],h=c.length,d=u[l]=Array(h),p=0;p<h;++p)(s=c[p]||f[p])&&(d[p]=s);for(;l<i;++l)u[l]=n[l];return new q(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=m);for(var n=this._groups,r=n.length,i=Array(r),o=0;o<r;++o){for(var a,u=n[o],l=u.length,s=i[o]=Array(l),c=0;c<l;++c)(a=u[c])&&(s[c]=a);s.sort(e)}return new q(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(let e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,o=e[n],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,e){var n=(0,y.A)(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof e?n.local?function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}:function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}:n.local?function(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}:function(t,e){return function(){this.setAttribute(t,e)}})(n,e))},style:v.A,property:function(t,e){return arguments.length>1?this.each((null==e?function(t){return function(){delete this[t]}}:"function"==typeof e?function(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}:function(t,e){return function(){this[t]=e}})(t,e)):this.node()[t]},classed:function(t,e){var n=g(t+"");if(arguments.length<2){for(var r=_(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"==typeof e?function(t,e){return function(){(e.apply(this,arguments)?w:b)(this,t)}}:e?function(t){return function(){w(this,t)}}:function(t){return function(){b(this,t)}})(n,e))},text:function(t){return arguments.length?this.each(null==t?A:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?k:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(M)},lower:function(){return this.each(z)},append:function(t){var e="function"==typeof t?t:S(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})},insert:function(t,e){var n="function"==typeof t?t:S(t),i=null==e?j:"function"==typeof e?e:(0,r.A)(e);return this.select(function(){return this.insertBefore(n.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each(C)},clone:function(t){return this.select(t?N:P)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}),a=o.length;if(arguments.length<2){var u=this.node().__on;if(u){for(var l,s=0,c=u.length;s<c;++s)for(r=0,l=u[s];r<a;++r)if((i=o[r]).type===l.type&&i.name===l.name)return l.value}return}for(r=0,u=e?T:O;r<a;++r)this.each(u(o[r],e,n));return this},dispatch:function(t,e){return this.each(("function"==typeof e?function(t,e){return function(){return $(this,t,e.apply(this,arguments))}}:function(t,e){return function(){return $(this,t,e)}})(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r,i=t[e],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};let B=D},4804:(t,e,n)=>{n.d(e,{s_:()=>A,GS:()=>p});var r=n(1235),i=n(2903);let o={capture:!0,passive:!1};function a(t){t.preventDefault(),t.stopImmediatePropagation()}function u(t){return((t=Math.exp(t))+1/t)/2}let l=function t(e,n,r){function i(t,i){var o,a,l=t[0],s=t[1],c=t[2],f=i[0],h=i[1],d=i[2],p=f-l,m=h-s,y=p*p+m*m;if(y<1e-12)a=Math.log(d/c)/e,o=function(t){return[l+t*p,s+t*m,c*Math.exp(e*t*a)]};else{var v=Math.sqrt(y),g=(d*d-c*c+r*y)/(2*c*n*v),_=(d*d-c*c-r*y)/(2*d*n*v),x=Math.log(Math.sqrt(g*g+1)-g);a=(Math.log(Math.sqrt(_*_+1)-_)-x)/e,o=function(t){var r,i,o=t*a,f=u(x),h=c/(n*v)*(f*(((r=Math.exp(2*(r=e*o+x)))-1)/(r+1))-((i=Math.exp(i=x))-1/i)/2);return[l+h*p,s+h*m,c*f/u(e*o+x)]}}return o.duration=1e3*a*e/Math.SQRT2,o}return i.rho=function(e){var n=Math.max(.001,+e),r=n*n;return t(n,r,r*r)},i}(Math.SQRT2,2,4);function s(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(e.getScreenCTM().inverse())).x,r.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}var c=n(469);let f=t=>()=>t;function h(t,{sourceEvent:e,target:n,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function d(t,e,n){this.k=t,this.x=e,this.y=n}d.prototype={constructor:d,scale:function(t){return 1===t?this:new d(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new d(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var p=new d(1,0,0);function m(t){t.stopImmediatePropagation()}function y(t){t.preventDefault(),t.stopImmediatePropagation()}function v(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function g(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function _(){return this.__zoom||p}function x(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function w(){return navigator.maxTouchPoints||"ontouchstart"in this}function b(t,e,n){var r=t.invertX(e[0][0])-n[0][0],i=t.invertX(e[1][0])-n[1][0],o=t.invertY(e[0][1])-n[0][1],a=t.invertY(e[1][1])-n[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function A(){var t,e,n,u=v,A=g,k=b,M=x,z=w,E=[0,1/0],S=[[-1/0,-1/0],[1/0,1/0]],j=250,C=l,P=(0,r.A)("start","zoom","end"),N=0,O=10;function T(t){t.property("__zoom",_).on("wheel.zoom",X,{passive:!1}).on("mousedown.zoom",L).on("dblclick.zoom",Y).filter(z).on("touchstart.zoom",V).on("touchmove.zoom",H).on("touchend.zoom touchcancel.zoom",G).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function R(t,e){return(e=Math.max(E[0],Math.min(E[1],e)))===t.k?t:new d(e,t.x,t.y)}function $(t,e,n){var r=e[0]-n[0]*t.k,i=e[1]-n[1]*t.k;return r===t.x&&i===t.y?t:new d(t.k,r,i)}function I(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function q(t,e,n,r){t.on("start.zoom",function(){D(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){D(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,i=D(this,t).event(r),o=A.apply(this,t),a=null==n?I(o):"function"==typeof n?n.apply(this,t):n,u=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),l=this.__zoom,s="function"==typeof e?e.apply(this,t):e,c=C(l.invert(a).concat(u/l.k),s.invert(a).concat(u/s.k));return function(t){if(1===t)t=s;else{var e=c(t),n=u/e[2];t=new d(n,a[0]-e[0]*n,a[1]-e[1]*n)}i.zoom(null,t)}})}function D(t,e,n){return!n&&t.__zooming||new B(t,e)}function B(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=A.apply(t,e),this.taps=0}function X(t,...e){if(u.apply(this,arguments)){var n=D(this,e).event(t),r=this.__zoom,i=Math.max(E[0],Math.min(E[1],r.k*Math.pow(2,M.apply(this,arguments)))),o=s(t);if(n.wheel)(n.mouse[0][0]!==o[0]||n.mouse[0][1]!==o[1])&&(n.mouse[1]=r.invert(n.mouse[0]=o)),clearTimeout(n.wheel);else{if(r.k===i)return;n.mouse=[o,r.invert(o)],(0,c.G)(this),n.start()}y(t),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",k($(R(r,i),n.mouse[0],n.mouse[1]),n.extent,S))}}function L(t,...e){if(!n&&u.apply(this,arguments)){var r,l,f,h=t.currentTarget,d=D(this,e,!0).event(t),p=(0,i.A)(t.view).on("mousemove.zoom",function(t){if(y(t),!d.moved){var e=t.clientX-g,n=t.clientY-_;d.moved=e*e+n*n>N}d.event(t).zoom("mouse",k($(d.that.__zoom,d.mouse[0]=s(t,h),d.mouse[1]),d.extent,S))},!0).on("mouseup.zoom",function(t){var e,n,r,u;p.on("mousemove.zoom mouseup.zoom",null),e=t.view,n=d.moved,r=e.document.documentElement,u=(0,i.A)(e).on("dragstart.drag",null),n&&(u.on("click.drag",a,o),setTimeout(function(){u.on("click.drag",null)},0)),"onselectstart"in r?u.on("selectstart.drag",null):(r.style.MozUserSelect=r.__noselect,delete r.__noselect),y(t),d.event(t).end()},!0),v=s(t,h),g=t.clientX,_=t.clientY;l=(r=t.view).document.documentElement,f=(0,i.A)(r).on("dragstart.drag",a,o),"onselectstart"in l?f.on("selectstart.drag",a,o):(l.__noselect=l.style.MozUserSelect,l.style.MozUserSelect="none"),m(t),d.mouse=[v,this.__zoom.invert(v)],(0,c.G)(this),d.start()}}function Y(t,...e){if(u.apply(this,arguments)){var n=this.__zoom,r=s(t.changedTouches?t.changedTouches[0]:t,this),o=n.invert(r),a=n.k*(t.shiftKey?.5:2),l=k($(R(n,a),r,o),A.apply(this,e),S);y(t),j>0?(0,i.A)(this).transition().duration(j).call(q,l,r,t):(0,i.A)(this).call(T.transform,l,r,t)}}function V(n,...r){if(u.apply(this,arguments)){var i,o,a,l,f=n.touches,h=f.length,d=D(this,r,n.changedTouches.length===h).event(n);for(m(n),o=0;o<h;++o)l=[l=s(a=f[o],this),this.__zoom.invert(l),a.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,i=!0,d.taps=1+!!t);t&&(t=clearTimeout(t)),i&&(d.taps<2&&(e=l[0],t=setTimeout(function(){t=null},500)),(0,c.G)(this),d.start())}}function H(t,...e){if(this.__zooming){var n,r,i,o,a=D(this,e).event(t),u=t.changedTouches,l=u.length;for(y(t),n=0;n<l;++n)i=s(r=u[n],this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=i:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=i);if(r=a.that.__zoom,a.touch1){var c=a.touch0[0],f=a.touch0[1],h=a.touch1[0],d=a.touch1[1],p=(p=h[0]-c[0])*p+(p=h[1]-c[1])*p,m=(m=d[0]-f[0])*m+(m=d[1]-f[1])*m;r=R(r,Math.sqrt(p/m)),i=[(c[0]+h[0])/2,(c[1]+h[1])/2],o=[(f[0]+d[0])/2,(f[1]+d[1])/2]}else{if(!a.touch0)return;i=a.touch0[0],o=a.touch0[1]}a.zoom("touch",k($(r,i,o),a.extent,S))}}function G(t,...r){if(this.__zooming){var o,a,u=D(this,r).event(t),l=t.changedTouches,c=l.length;for(m(t),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),o=0;o<c;++o)a=l[o],u.touch0&&u.touch0[2]===a.identifier?delete u.touch0:u.touch1&&u.touch1[2]===a.identifier&&delete u.touch1;if(u.touch1&&!u.touch0&&(u.touch0=u.touch1,delete u.touch1),u.touch0)u.touch0[1]=this.__zoom.invert(u.touch0[0]);else if(u.end(),2===u.taps&&(a=s(a,this),Math.hypot(e[0]-a[0],e[1]-a[1])<O)){var f=(0,i.A)(this).on("dblclick.zoom");f&&f.apply(this,arguments)}}}return T.transform=function(t,e,n,r){var i=t.selection?t.selection():t;i.property("__zoom",_),t!==i?q(t,e,n,r):i.interrupt().each(function(){D(this,arguments).event(r).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()})},T.scaleBy=function(t,e,n,r){T.scaleTo(t,function(){var t=this.__zoom.k,n="function"==typeof e?e.apply(this,arguments):e;return t*n},n,r)},T.scaleTo=function(t,e,n,r){T.transform(t,function(){var t=A.apply(this,arguments),r=this.__zoom,i=null==n?I(t):"function"==typeof n?n.apply(this,arguments):n,o=r.invert(i),a="function"==typeof e?e.apply(this,arguments):e;return k($(R(r,a),i,o),t,S)},n,r)},T.translateBy=function(t,e,n,r){T.transform(t,function(){return k(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),A.apply(this,arguments),S)},null,r)},T.translateTo=function(t,e,n,r,i){T.transform(t,function(){var t=A.apply(this,arguments),i=this.__zoom,o=null==r?I(t):"function"==typeof r?r.apply(this,arguments):r;return k(p.translate(o[0],o[1]).scale(i.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,S)},r,i)},B.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=(0,i.A)(this.that).datum();P.call(t,this.that,new h(t,{sourceEvent:this.sourceEvent,target:T,type:t,transform:this.that.__zoom,dispatch:P}),e)}},T.wheelDelta=function(t){return arguments.length?(M="function"==typeof t?t:f(+t),T):M},T.filter=function(t){return arguments.length?(u="function"==typeof t?t:f(!!t),T):u},T.touchable=function(t){return arguments.length?(z="function"==typeof t?t:f(!!t),T):z},T.extent=function(t){return arguments.length?(A="function"==typeof t?t:f([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),T):A},T.scaleExtent=function(t){return arguments.length?(E[0]=+t[0],E[1]=+t[1],T):[E[0],E[1]]},T.translateExtent=function(t){return arguments.length?(S[0][0]=+t[0][0],S[1][0]=+t[1][0],S[0][1]=+t[0][1],S[1][1]=+t[1][1],T):[[S[0][0],S[0][1]],[S[1][0],S[1][1]]]},T.constrain=function(t){return arguments.length?(k=t,T):k},T.duration=function(t){return arguments.length?(j=+t,T):j},T.interpolate=function(t){return arguments.length?(C=t,T):C},T.on=function(){var t=P.on.apply(P,arguments);return t===P?T:t},T.clickDistance=function(t){return arguments.length?(N=(t*=1)*t,T):Math.sqrt(N)},T.tapDistance=function(t){return arguments.length?(O=+t,T):O},T}d.prototype},4870:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},5029:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return a}});let r=n(2115),i=r.useLayoutEffect,o=r.useEffect;function a(t){let{headManager:e,reduceComponentsToState:n}=t;function a(){if(e&&e.mountedInstances){let i=r.Children.toArray(Array.from(e.mountedInstances).filter(Boolean));e.updateHead(n(i,t))}}return i(()=>{var n;return null==e||null==(n=e.mountedInstances)||n.add(t.children),()=>{var n;null==e||null==(n=e.mountedInstances)||n.delete(t.children)}}),i(()=>(e&&(e._pendingUpdate=a),()=>{e&&(e._pendingUpdate=a)})),o(()=>(e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null),()=>{e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null)})),null}},5100:(t,e)=>{function n(t){let{widthInt:e,heightInt:n,blurWidth:r,blurHeight:i,blurDataURL:o,objectFit:a}=t,u=r?40*r:e,l=i?40*i:n,s=u&&l?"viewBox='0 0 "+u+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},5131:(t,e,n)=>{n.d(e,{A:()=>i,g:()=>r});var r="http://www.w3.org/1999/xhtml";let i={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},5564:(t,e,n)=>{var r=n(9509);Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}(e,{default:function(){return y},defaultHead:function(){return h}});let i=n(8229),o=n(6966),a=n(5155),u=o._(n(2115)),l=i._(n(5029)),s=n(2464),c=n(2830),f=n(7544);function h(t){void 0===t&&(t=!1);let e=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return t||e.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),e}function d(t,e){return"string"==typeof e||"number"==typeof e?t:e.type===u.default.Fragment?t.concat(u.default.Children.toArray(e.props.children).reduce((t,e)=>"string"==typeof e||"number"==typeof e?t:t.concat(e),[])):t.concat(e)}n(3230);let p=["name","httpEquiv","charSet","itemProp"];function m(t,e){let{inAmpMode:n}=e;return t.reduce(d,[]).reverse().concat(h(n).reverse()).filter(function(){let t=new Set,e=new Set,n=new Set,r={};return i=>{let o=!0,a=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){a=!0;let e=i.key.slice(i.key.indexOf("$")+1);t.has(e)?o=!1:t.add(e)}switch(i.type){case"title":case"base":e.has(i.type)?o=!1:e.add(i.type);break;case"meta":for(let t=0,e=p.length;t<e;t++){let e=p[t];if(i.props.hasOwnProperty(e))if("charSet"===e)n.has(e)?o=!1:n.add(e);else{let t=i.props[e],n=r[e]||new Set;("name"!==e||!a)&&n.has(t)?o=!1:(n.add(t),r[e]=n)}}}return o}}()).reverse().map((t,e)=>{let i=t.key||e;if(r.env.__NEXT_OPTIMIZE_FONTS&&!n&&"link"===t.type&&t.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(e=>t.props.href.startsWith(e))){let e={...t.props||{}};return e["data-href"]=e.href,e.href=void 0,e["data-optimized-fonts"]=!0,u.default.cloneElement(t,e)}return u.default.cloneElement(t,{key:i})})}let y=function(t){let{children:e}=t,n=(0,u.useContext)(s.AmpStateContext),r=(0,u.useContext)(c.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:m,headManager:r,inAmpMode:(0,f.isInAmpMode)(n),children:e})};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},5840:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}(e,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return r}});let n=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6102:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(5131);function i(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),r.A.hasOwnProperty(e)?{space:r.A[e],local:t}:t}},6516:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},6740:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},6752:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ImageConfigContext",{enumerable:!0,get:function(){return o}});let r=n(8229)._(n(2115)),i=n(5840),o=r.default.createContext(i.imageConfigDefault)},6766:(t,e,n)=>{n.d(e,{default:()=>i.a});var r=n(1469),i=n.n(r)},6767:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},6785:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7271:(t,e,n)=>{n.d(e,{A:()=>r});function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}},7544:(t,e)=>{function n(t){let{ampFirst:e=!1,hybrid:n=!1,hasQuery:r=!1}=void 0===t?{}:t;return e||n&&r}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isInAmpMode",{enumerable:!0,get:function(){return n}})},9037:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9121:(t,e,n)=>{function r(t){var e=0,n=t.children,r=n&&n.length;if(r)for(;--r>=0;)e+=n[r].value;else e=1;t.value=e}function i(t,e){t instanceof Map?(t=[void 0,t],void 0===e&&(e=a)):void 0===e&&(e=o);for(var n,r,i,u,c,f=new s(t),h=[f];n=h.pop();)if((i=e(n.data))&&(c=(i=Array.from(i)).length))for(n.children=i,u=c-1;u>=0;--u)h.push(r=i[u]=new s(i[u])),r.parent=n,r.depth=n.depth+1;return f.eachBefore(l)}function o(t){return t.children}function a(t){return Array.isArray(t)?t[1]:null}function u(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function l(t){var e=0;do t.height=e;while((t=t.parent)&&t.height<++e)}function s(t){this.data=t,this.depth=this.height=0,this.parent=null}n.d(e,{bP:()=>s,Ay:()=>i}),s.prototype=i.prototype={constructor:s,count:function(){return this.eachAfter(r)},each:function(t,e){let n=-1;for(let r of this)t.call(e,r,++n,this);return this},eachAfter:function(t,e){for(var n,r,i,o=this,a=[o],u=[],l=-1;o=a.pop();)if(u.push(o),n=o.children)for(r=0,i=n.length;r<i;++r)a.push(n[r]);for(;o=u.pop();)t.call(e,o,++l,this);return this},eachBefore:function(t,e){for(var n,r,i=this,o=[i],a=-1;i=o.pop();)if(t.call(e,i,++a,this),n=i.children)for(r=n.length-1;r>=0;--r)o.push(n[r]);return this},find:function(t,e){let n=-1;for(let r of this)if(t.call(e,r,++n,this))return r},sum:function(t){return this.eachAfter(function(e){for(var n=+t(e.data)||0,r=e.children,i=r&&r.length;--i>=0;)n+=r[i].value;e.value=n})},sort:function(t){return this.eachBefore(function(e){e.children&&e.children.sort(t)})},path:function(t){for(var e=this,n=function(t,e){if(t===e)return t;var n=t.ancestors(),r=e.ancestors(),i=null;for(t=n.pop(),e=r.pop();t===e;)i=t,t=n.pop(),e=r.pop();return i}(e,t),r=[e];e!==n;)r.push(e=e.parent);for(var i=r.length;t!==n;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,e=[t];t=t.parent;)e.push(t);return e},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore(function(e){e.children||t.push(e)}),t},links:function(){var t=this,e=[];return t.each(function(n){n!==t&&e.push({source:n.parent,target:n})}),e},copy:function(){return i(this).eachBefore(u)},[Symbol.iterator]:function*(){var t,e,n,r,i=this,o=[i];do for(t=o.reverse(),o=[];i=t.pop();)if(yield i,e=i.children)for(n=0,r=e.length;n<r;++n)o.push(e[n]);while(o.length)}}},9212:(t,e,n)=>{function r(t,e){return t.parent===e.parent?1:2}function i(t){var e=t.children;return e?e[0]:t.t}function o(t){var e=t.children;return e?e[e.length-1]:t.t}function a(t,e){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=e}function u(){var t=r,e=1,n=1,u=null;function l(r){var i=function(t){for(var e,n,r,i,o,u=new a(t,0),l=[u];e=l.pop();)if(r=e._.children)for(e.children=Array(o=r.length),i=o-1;i>=0;--i)l.push(n=e.children[i]=new a(r[i],i)),n.parent=e;return(u.parent=new a(null,0)).children=[u],u}(r);if(i.eachAfter(s),i.parent.m=-i.z,i.eachBefore(c),u)r.eachBefore(f);else{var o=r,l=r,h=r;r.eachBefore(function(t){t.x<o.x&&(o=t),t.x>l.x&&(l=t),t.depth>h.depth&&(h=t)});var d=o===l?1:t(o,l)/2,p=d-o.x,m=e/(l.x+d+p),y=n/(h.depth||1);r.eachBefore(function(t){t.x=(t.x+p)*m,t.y=t.depth*y})}return r}function s(e){var n=e.children,r=e.parent.children,a=e.i?r[e.i-1]:null;if(n){!function(t){for(var e,n=0,r=0,i=t.children,o=i.length;--o>=0;)e=i[o],e.z+=n,e.m+=n,n+=e.s+(r+=e.c)}(e);var u=(n[0].z+n[n.length-1].z)/2;a?(e.z=a.z+t(e._,a._),e.m=e.z-u):e.z=u}else a&&(e.z=a.z+t(e._,a._));e.parent.A=function(e,n,r){if(n){for(var a,u,l,s=e,c=e,f=n,h=s.parent.children[0],d=s.m,p=c.m,m=f.m,y=h.m;f=o(f),s=i(s),f&&s;)h=i(h),(c=o(c)).a=e,(l=f.z+m-s.z-d+t(f._,s._))>0&&(!function(t,e,n){var r=n/(e.i-t.i);e.c-=r,e.s+=n,t.c+=r,e.z+=n,e.m+=n}((a=f,u=r,a.a.parent===e.parent?a.a:u),e,l),d+=l,p+=l),m+=f.m,d+=s.m,y+=h.m,p+=c.m;f&&!o(c)&&(c.t=f,c.m+=m-p),s&&!i(h)&&(h.t=s,h.m+=d-y,r=e)}return r}(e,a,e.parent.A||r[0])}function c(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function f(t){t.x*=e,t.y=t.depth*n}return l.separation=function(e){return arguments.length?(t=e,l):t},l.size=function(t){return arguments.length?(u=!1,e=+t[0],n=+t[1],l):u?null:[e,n]},l.nodeSize=function(t){return arguments.length?(u=!0,e=+t[0],n=+t[1],l):u?[e,n]:null},l}n.d(e,{A:()=>u}),a.prototype=Object.create(n(9121).bP.prototype)},9293:(t,e,n)=>{function r(){return[]}function i(t){return null==t?r:function(){return this.querySelectorAll(t)}}n.d(e,{A:()=>i})},9803:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},9869:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}}]);