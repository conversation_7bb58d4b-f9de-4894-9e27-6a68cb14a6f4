(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{347:()=>{},658:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,1666,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,4105))},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},4105:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,AuthProvider:()=>l});var r=a(5155),o=a(2115);let s=(0,o.createContext)(void 0),l=e=>{let{children:t}=e,[a,l]=(0,o.useState)(null),[i,n]=(0,o.useState)(!0),[c,u]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=localStorage.getItem("auth-user");if(e)try{let t=JSON.parse(e);l(t)}catch(e){console.error("Error parsing stored user:",e),localStorage.removeItem("auth-user")}h()},[]);let h=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success?(l(t.data),localStorage.setItem("auth-user",JSON.stringify(t.data))):(l(null),localStorage.removeItem("auth-user"))}else l(null),localStorage.removeItem("auth-user")}catch(e){console.error("Auth check failed:",e),l(null),localStorage.removeItem("auth-user")}finally{n(!1),u(!0)}},d=async(e,t)=>{n(!0);try{let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),r=await a.json();if(!r.success)throw Error(r.error||"Login failed");l(r.data.user),localStorage.setItem("auth-user",JSON.stringify(r.data.user)),u(!0)}catch(e){throw e}finally{n(!1)}},f=async(e,t,a,r,o,s,l)=>{n(!0);try{let i=await fetch(l?"/api/auth/register?side=".concat(l):"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:a,password:r,confirmPassword:o,referralCode:s})}),n=await i.json();if(!n.success)throw Error(n.error||"Registration failed");await d(e,r)}catch(e){throw e}finally{n(!1)}},m=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{l(null),localStorage.removeItem("auth-user"),u(!0)}},y=async()=>{await h()};return(0,r.jsx)(s.Provider,{value:{user:a,loading:i,isInitialized:c,login:d,register:f,logout:m,refreshUser:y},children:t})},i=()=>{let e=(0,o.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[2258,8441,1684,7358],()=>t(658)),_N_E=e.O()}]);