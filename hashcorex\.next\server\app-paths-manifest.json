{"/_not-found/page": "app/_not-found/page.js", "/api/admin/binary-points/history/route": "app/api/admin/binary-points/history/route.js", "/api/admin/binary-points/reset-all/route": "app/api/admin/binary-points/reset-all/route.js", "/api/admin/binary-matching/manual/route": "app/api/admin/binary-matching/manual/route.js", "/api/admin/binary-points/route": "app/api/admin/binary-points/route.js", "/api/admin/binary-points/test-limit/route": "app/api/admin/binary-points/test-limit/route.js", "/api/admin/check/route": "app/api/admin/check/route.js", "/api/admin/binary-points/update-limit/route": "app/api/admin/binary-points/update-limit/route.js", "/api/admin/deposit-settings/route": "app/api/admin/deposit-settings/route.js", "/api/admin/binary-points/user-history/[userId]/route": "app/api/admin/binary-points/user-history/[userId]/route.js", "/api/admin/deposits/route": "app/api/admin/deposits/route.js", "/api/admin/email/smtp/route": "app/api/admin/email/smtp/route.js", "/api/admin/email/stats/route": "app/api/admin/email/stats/route.js", "/api/admin/kyc/all/route": "app/api/admin/kyc/all/route.js", "/api/admin/email/test/route": "app/api/admin/email/test/route.js", "/api/admin/kyc/bulk/route": "app/api/admin/kyc/bulk/route.js", "/api/admin/kyc/pending/route": "app/api/admin/kyc/pending/route.js", "/api/admin/kyc/search/route": "app/api/admin/kyc/search/route.js", "/api/admin/logs/export/route": "app/api/admin/logs/export/route.js", "/api/admin/kyc/review/route": "app/api/admin/kyc/review/route.js", "/api/admin/referral-commissions/route": "app/api/admin/referral-commissions/route.js", "/api/admin/referral-commissions/stats/route": "app/api/admin/referral-commissions/stats/route.js", "/api/admin/scheduler/route": "app/api/admin/scheduler/route.js", "/api/admin/logs/route": "app/api/admin/logs/route.js", "/api/admin/settings/pricing/route": "app/api/admin/settings/pricing/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/admin/refresh-tree-cache/route": "app/api/admin/refresh-tree-cache/route.js", "/api/admin/support/tickets/[ticketId]/responses/route": "app/api/admin/support/tickets/[ticketId]/responses/route.js", "/api/admin/stats/route": "app/api/admin/stats/route.js", "/api/admin/support/tickets/route": "app/api/admin/support/tickets/route.js", "/api/admin/update-mining-units-roi/route": "app/api/admin/update-mining-units-roi/route.js", "/api/admin/update-user-status/route": "app/api/admin/update-user-status/route.js", "/api/admin/support/tickets/[ticketId]/route": "app/api/admin/support/tickets/[ticketId]/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/admin/users/[userId]/details/route": "app/api/admin/users/[userId]/details/route.js", "/api/admin/withdrawals/action/route": "app/api/admin/withdrawals/action/route.js", "/api/admin/wallet/adjust/route": "app/api/admin/wallet/adjust/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/admin/withdrawals/route": "app/api/admin/withdrawals/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/binary-points/info/route": "app/api/binary-points/info/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/cron/binary-matching/route": "app/api/cron/binary-matching/route.js", "/api/admin/users/action/route": "app/api/admin/users/action/route.js", "/api/cron/weekly-payout/route": "app/api/cron/weekly-payout/route.js", "/api/cron/daily-roi/route": "app/api/cron/daily-roi/route.js", "/api/earnings/route": "app/api/earnings/route.js", "/api/errors/log/route": "app/api/errors/log/route.js", "/api/cron/process-deposits/route": "app/api/cron/process-deposits/route.js", "/api/init/route": "app/api/init/route.js", "/api/kyc/upload/route": "app/api/kyc/upload/route.js", "/api/kyc/submit/route": "app/api/kyc/submit/route.js", "/api/kyc/status/route": "app/api/kyc/status/route.js", "/api/kyc/documents/route": "app/api/kyc/documents/route.js", "/api/mining-units/[id]/earnings/route": "app/api/mining-units/[id]/earnings/route.js", "/api/referrals/stats/route": "app/api/referrals/stats/route.js", "/api/referrals/search/route": "app/api/referrals/search/route.js", "/api/mining-units/route": "app/api/mining-units/route.js", "/api/support/tickets/[ticketId]/responses/route": "app/api/support/tickets/[ticketId]/responses/route.js", "/api/support/tickets/route": "app/api/support/tickets/route.js", "/api/user/notification-settings/route": "app/api/user/notification-settings/route.js", "/api/user/withdrawal-address/route": "app/api/user/withdrawal-address/route.js", "/api/wallet/balance/route": "app/api/wallet/balance/route.js", "/api/test/binary-settings/route": "app/api/test/binary-settings/route.js", "/api/referrals/tree/route": "app/api/referrals/tree/route.js", "/api/wallet/deposit/info/route": "app/api/wallet/deposit/info/route.js", "/api/wallet/deposit-address/route": "app/api/wallet/deposit-address/route.js", "/api/wallet/deposit/verify/route": "app/api/wallet/deposit/verify/route.js", "/api/wallet/transactions/[id]/route": "app/api/wallet/transactions/[id]/route.js", "/api/wallet/transactions/route": "app/api/wallet/transactions/route.js", "/api/wallet/withdraw/route": "app/api/wallet/withdraw/route.js", "/api/wallet/withdrawal-settings/route": "app/api/wallet/withdrawal-settings/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/(public)/privacy/page": "app/(public)/privacy/page.js", "/(public)/terms/page": "app/(public)/terms/page.js", "/(public)/about/page": "app/(public)/about/page.js", "/(public)/contact/page": "app/(public)/contact/page.js", "/(public)/how-it-works/page": "app/(public)/how-it-works/page.js", "/admin/page": "app/admin/page.js", "/page": "app/page.js"}