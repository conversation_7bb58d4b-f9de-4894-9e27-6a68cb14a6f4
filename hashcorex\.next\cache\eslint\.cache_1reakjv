[{"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\login\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\register\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(dashboard)\\dashboard\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\about\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\contact\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\how-it-works\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\privacy\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\terms\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\admin\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-matching\\manual\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\history\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\reset-all\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\test-limit\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\update-limit\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\user-history\\[userId]\\route.ts": "16", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\check\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposit-settings\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposits\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\all\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\bulk\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\pending\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\review\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\search\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\export\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\referral-commissions\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\referral-commissions\\stats\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\refresh-tree-cache\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\pricing\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\stats\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\[ticketId]\\responses\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\[ticketId]\\route.ts": "35", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\update-mining-units-roi\\route.ts": "36", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\update-user-status\\route.ts": "37", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\action\\route.ts": "38", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\route.ts": "39", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\[userId]\\details\\route.ts": "40", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\wallet\\adjust\\route.ts": "41", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\withdrawals\\action\\route.ts": "42", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\withdrawals\\route.ts": "43", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\login\\route.ts": "44", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\logout\\route.ts": "45", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\me\\route.ts": "46", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\register\\route.ts": "47", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\binary-points\\info\\route.ts": "48", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\binary-matching\\route.ts": "49", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\daily-roi\\route.ts": "50", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\process-deposits\\route.ts": "51", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\weekly-payout\\route.ts": "52", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\earnings\\route.ts": "53", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\errors\\log\\route.ts": "54", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\init\\route.ts": "55", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\documents\\route.ts": "56", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\status\\route.ts": "57", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\submit\\route.ts": "58", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\upload\\route.ts": "59", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\route.ts": "60", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\[id]\\earnings\\route.ts": "61", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\search\\route.ts": "62", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\stats\\route.ts": "63", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\tree\\route.ts": "64", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\support\\tickets\\route.ts": "65", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\support\\tickets\\[ticketId]\\responses\\route.ts": "66", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\test\\binary-settings\\route.ts": "67", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\notification-settings\\route.ts": "68", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\withdrawal-address\\route.ts": "69", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\balance\\route.ts": "70", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\info\\route.ts": "71", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\verify\\route.ts": "72", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit-address\\route.ts": "73", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\transactions\\route.ts": "74", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\transactions\\[id]\\route.ts": "75", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\withdraw\\route.ts": "76", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\withdrawal-settings\\route.ts": "77", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx": "78", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\page.tsx": "79", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\AdminDashboard.tsx": "80", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\AdminLayout.tsx": "81", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\BinaryPointsManagement.tsx": "82", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\DepositManagement.tsx": "83", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\ErrorLogsViewer.tsx": "84", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\KYCReview.tsx": "85", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\ReferralCommissionTracking.tsx": "86", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\SupportTicketManagement.tsx": "87", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\SystemLogs.tsx": "88", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\SystemSettings.tsx": "89", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\UserManagement.tsx": "90", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\WithdrawalManagement.tsx": "91", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\AdvancedBinaryTreeVisualizer.tsx": "92", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\BinaryPointsInfoPanel.tsx": "93", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\BinaryTreeVisualizer.tsx": "94", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\D3BinaryTree.tsx": "95", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\DashboardLayout.tsx": "96", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\DashboardOverview.tsx": "97", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\EarningsTracker.tsx": "98", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\KYCPortal.tsx": "99", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\MiningUnitsTable.tsx": "100", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\PurchaseMiningUnit.tsx": "101", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\ReactFlowBinaryTree.tsx": "102", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\SupportCenter.tsx": "103", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\TreeNavigationControls.tsx": "104", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\UserProfileSettings.tsx": "105", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\WalletDashboard.tsx": "106", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\Cryptocurrency.tsx": "107", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\EcoFriendly.tsx": "108", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\index.ts": "109", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\MiningRig.tsx": "110", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\SolarPanel.tsx": "111", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\Container.tsx": "112", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\Flex.tsx": "113", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\Grid.tsx": "114", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\index.ts": "115", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\PublicLayout.tsx": "116", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\pages\\HomePage.tsx": "117", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\pages\\index.ts": "118", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Button.tsx": "119", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Card.tsx": "120", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\ConfirmDialog.tsx": "121", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\index.ts": "122", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Input.tsx": "123", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Loading.tsx": "124", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\MessageBox.tsx": "125", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Modal.tsx": "126", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\wallet\\DepositPage.tsx": "127", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx": "128", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useClientOnly.ts": "129", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useTreeInteractions.ts": "130", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\auth.ts": "131", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\database.ts": "132", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\depositVerificationService.ts": "133", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\errorLogger.ts": "134", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\initServices.ts": "135", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\mining.ts": "136", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\miningUnitEarnings.ts": "137", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\prisma.ts": "138", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\referral.ts": "139", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\referralValidation.ts": "140", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\trongrid.ts": "141", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\utils.ts": "142", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\scripts\\validateImplementation.ts": "143", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\tests\\integration.test.ts": "144", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\tests\\referral.test.ts": "145", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\types\\index.ts": "146", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\smtp\\route.ts": "147", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\stats\\route.ts": "148", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\test\\route.ts": "149", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\scheduler\\route.ts": "150", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\EmailSettings.tsx": "151", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\auth\\AuthGuard.tsx": "152", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\emailService.ts": "153", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\emailTemplates.ts": "154", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\emailTriggers.ts": "155", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\initializeApp.ts": "156", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\scheduler.ts": "157", "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\clientErrorLogger.tsx": "158"}, {"size": 8335, "mtime": 1751136814761, "results": "159", "hashOfConfig": "160"}, {"size": 13015, "mtime": 1751138516860, "results": "161", "hashOfConfig": "160"}, {"size": 1989, "mtime": 1751135349728, "results": "162", "hashOfConfig": "160"}, {"size": 10066, "mtime": 1751136946031, "results": "163", "hashOfConfig": "160"}, {"size": 11974, "mtime": 1751137194738, "results": "164", "hashOfConfig": "160"}, {"size": 12196, "mtime": 1751137456286, "results": "165", "hashOfConfig": "160"}, {"size": 12871, "mtime": 1750529506295, "results": "166", "hashOfConfig": "160"}, {"size": 15572, "mtime": 1751137475831, "results": "167", "hashOfConfig": "160"}, {"size": 2191, "mtime": 1751136259739, "results": "168", "hashOfConfig": "160"}, {"size": 2273, "mtime": 1750527955445, "results": "169", "hashOfConfig": "160"}, {"size": 3679, "mtime": 1750531799190, "results": "170", "hashOfConfig": "160"}, {"size": 2504, "mtime": 1751138569832, "results": "171", "hashOfConfig": "160"}, {"size": 4292, "mtime": 1751138616775, "results": "172", "hashOfConfig": "160"}, {"size": 4278, "mtime": 1751031707387, "results": "173", "hashOfConfig": "160"}, {"size": 1839, "mtime": 1751031831928, "results": "174", "hashOfConfig": "160"}, {"size": 3087, "mtime": 1751051937909, "results": "175", "hashOfConfig": "160"}, {"size": 882, "mtime": 1750340634498, "results": "176", "hashOfConfig": "160"}, {"size": 7779, "mtime": 1750435845806, "results": "177", "hashOfConfig": "160"}, {"size": 4837, "mtime": 1751138663931, "results": "178", "hashOfConfig": "160"}, {"size": 2517, "mtime": 1750446519845, "results": "179", "hashOfConfig": "160"}, {"size": 4197, "mtime": 1750446722531, "results": "180", "hashOfConfig": "160"}, {"size": 2111, "mtime": 1750445545322, "results": "181", "hashOfConfig": "160"}, {"size": 3158, "mtime": 1751134476973, "results": "182", "hashOfConfig": "160"}, {"size": 3786, "mtime": 1750446700169, "results": "183", "hashOfConfig": "160"}, {"size": 3841, "mtime": 1751037608949, "results": "184", "hashOfConfig": "160"}, {"size": 3722, "mtime": 1751037561394, "results": "185", "hashOfConfig": "160"}, {"size": 6170, "mtime": 1751039799069, "results": "186", "hashOfConfig": "160"}, {"size": 3292, "mtime": 1751011098084, "results": "187", "hashOfConfig": "160"}, {"size": 1101, "mtime": 1750415608749, "results": "188", "hashOfConfig": "160"}, {"size": 1867, "mtime": 1750528201977, "results": "189", "hashOfConfig": "160"}, {"size": 8308, "mtime": 1751039926284, "results": "190", "hashOfConfig": "160"}, {"size": 3546, "mtime": 1751120584795, "results": "191", "hashOfConfig": "160"}, {"size": 1609, "mtime": 1750450792669, "results": "192", "hashOfConfig": "160"}, {"size": 2264, "mtime": 1751136671146, "results": "193", "hashOfConfig": "160"}, {"size": 2977, "mtime": 1751136744215, "results": "194", "hashOfConfig": "160"}, {"size": 2297, "mtime": 1750529082103, "results": "195", "hashOfConfig": "160"}, {"size": 1335, "mtime": 1750496961449, "results": "196", "hashOfConfig": "160"}, {"size": 3419, "mtime": 1750358422646, "results": "197", "hashOfConfig": "160"}, {"size": 4930, "mtime": 1751138979982, "results": "198", "hashOfConfig": "160"}, {"size": 5528, "mtime": 1751004469372, "results": "199", "hashOfConfig": "160"}, {"size": 7048, "mtime": 1751139050701, "results": "200", "hashOfConfig": "160"}, {"size": 5807, "mtime": 1751134476973, "results": "201", "hashOfConfig": "160"}, {"size": 2714, "mtime": 1750488391066, "results": "202", "hashOfConfig": "160"}, {"size": 2277, "mtime": 1751139114155, "results": "203", "hashOfConfig": "160"}, {"size": 1231, "mtime": 1750338778106, "results": "204", "hashOfConfig": "160"}, {"size": 973, "mtime": 1750356858666, "results": "205", "hashOfConfig": "160"}, {"size": 2606, "mtime": 1751139189371, "results": "206", "hashOfConfig": "160"}, {"size": 4944, "mtime": 1751032133683, "results": "207", "hashOfConfig": "160"}, {"size": 2029, "mtime": 1751182085653, "results": "208", "hashOfConfig": "160"}, {"size": 2248, "mtime": 1750339740831, "results": "209", "hashOfConfig": "160"}, {"size": 6782, "mtime": 1751047628936, "results": "210", "hashOfConfig": "160"}, {"size": 2007, "mtime": 1751135136911, "results": "211", "hashOfConfig": "160"}, {"size": 3027, "mtime": 1750339781020, "results": "212", "hashOfConfig": "160"}, {"size": 1505, "mtime": 1750529154540, "results": "213", "hashOfConfig": "160"}, {"size": 1574, "mtime": 1751023406401, "results": "214", "hashOfConfig": "160"}, {"size": 1148, "mtime": 1750340525201, "results": "215", "hashOfConfig": "160"}, {"size": 3358, "mtime": 1750446678533, "results": "216", "hashOfConfig": "160"}, {"size": 3928, "mtime": 1750446658056, "results": "217", "hashOfConfig": "160"}, {"size": 6876, "mtime": 1750446406761, "results": "218", "hashOfConfig": "160"}, {"size": 7292, "mtime": 1751134476973, "results": "219", "hashOfConfig": "160"}, {"size": 3578, "mtime": 1751137735606, "results": "220", "hashOfConfig": "160"}, {"size": 1305, "mtime": 1750408020364, "results": "221", "hashOfConfig": "160"}, {"size": 2346, "mtime": 1750408038080, "results": "222", "hashOfConfig": "160"}, {"size": 4278, "mtime": 1751008618189, "results": "223", "hashOfConfig": "160"}, {"size": 2371, "mtime": 1750450759346, "results": "224", "hashOfConfig": "160"}, {"size": 2212, "mtime": 1751137781957, "results": "225", "hashOfConfig": "160"}, {"size": 3060, "mtime": 1751032979504, "results": "226", "hashOfConfig": "160"}, {"size": 2266, "mtime": 1750451060897, "results": "227", "hashOfConfig": "160"}, {"size": 3263, "mtime": 1751124917790, "results": "228", "hashOfConfig": "160"}, {"size": 3866, "mtime": 1751026846053, "results": "229", "hashOfConfig": "160"}, {"size": 4740, "mtime": 1751027626110, "results": "230", "hashOfConfig": "160"}, {"size": 8276, "mtime": 1751047918344, "results": "231", "hashOfConfig": "160"}, {"size": 1432, "mtime": 1751045034719, "results": "232", "hashOfConfig": "160"}, {"size": 5787, "mtime": 1751049161583, "results": "233", "hashOfConfig": "160"}, {"size": 3175, "mtime": 1751137827321, "results": "234", "hashOfConfig": "160"}, {"size": 6478, "mtime": 1750488288104, "results": "235", "hashOfConfig": "160"}, {"size": 1375, "mtime": 1750448502130, "results": "236", "hashOfConfig": "160"}, {"size": 744, "mtime": 1750342564076, "results": "237", "hashOfConfig": "160"}, {"size": 115, "mtime": 1750339335321, "results": "238", "hashOfConfig": "160"}, {"size": 16199, "mtime": 1751120528480, "results": "239", "hashOfConfig": "160"}, {"size": 11863, "mtime": 1751136215781, "results": "240", "hashOfConfig": "160"}, {"size": 28378, "mtime": 1751126835573, "results": "241", "hashOfConfig": "160"}, {"size": 18594, "mtime": 1751038616979, "results": "242", "hashOfConfig": "160"}, {"size": 12194, "mtime": 1751029458960, "results": "243", "hashOfConfig": "160"}, {"size": 17525, "mtime": 1751038582015, "results": "244", "hashOfConfig": "160"}, {"size": 17729, "mtime": 1751039783663, "results": "245", "hashOfConfig": "160"}, {"size": 14884, "mtime": 1751038636140, "results": "246", "hashOfConfig": "160"}, {"size": 19589, "mtime": 1751038709810, "results": "247", "hashOfConfig": "160"}, {"size": 46806, "mtime": 1751137601658, "results": "248", "hashOfConfig": "160"}, {"size": 43284, "mtime": 1751039567343, "results": "249", "hashOfConfig": "160"}, {"size": 16258, "mtime": 1751038596476, "results": "250", "hashOfConfig": "160"}, {"size": 49734, "mtime": 1751135892136, "results": "251", "hashOfConfig": "160"}, {"size": 14794, "mtime": 1751033554317, "results": "252", "hashOfConfig": "160"}, {"size": 16890, "mtime": 1751033535722, "results": "253", "hashOfConfig": "160"}, {"size": 22607, "mtime": 1751134476946, "results": "254", "hashOfConfig": "160"}, {"size": 14411, "mtime": 1751123883147, "results": "255", "hashOfConfig": "160"}, {"size": 22032, "mtime": 1751043083851, "results": "256", "hashOfConfig": "160"}, {"size": 13953, "mtime": 1751043129691, "results": "257", "hashOfConfig": "160"}, {"size": 19499, "mtime": 1751120647401, "results": "258", "hashOfConfig": "160"}, {"size": 19373, "mtime": 1751137642901, "results": "259", "hashOfConfig": "160"}, {"size": 16121, "mtime": 1751127977838, "results": "260", "hashOfConfig": "160"}, {"size": 19626, "mtime": 1751008638893, "results": "261", "hashOfConfig": "160"}, {"size": 12908, "mtime": 1751137681833, "results": "262", "hashOfConfig": "160"}, {"size": 7355, "mtime": 1751008711353, "results": "263", "hashOfConfig": "160"}, {"size": 20420, "mtime": 1751127569864, "results": "264", "hashOfConfig": "160"}, {"size": 43570, "mtime": 1751127952060, "results": "265", "hashOfConfig": "160"}, {"size": 1749, "mtime": 1750339188473, "results": "266", "hashOfConfig": "160"}, {"size": 2492, "mtime": 1750339215807, "results": "267", "hashOfConfig": "160"}, {"size": 204, "mtime": 1750339225163, "results": "268", "hashOfConfig": "160"}, {"size": 1489, "mtime": 1750339170058, "results": "269", "hashOfConfig": "160"}, {"size": 1180, "mtime": 1750339154802, "results": "270", "hashOfConfig": "160"}, {"size": 639, "mtime": 1750529366025, "results": "271", "hashOfConfig": "160"}, {"size": 1528, "mtime": 1750339280091, "results": "272", "hashOfConfig": "160"}, {"size": 1977, "mtime": 1750339263019, "results": "273", "hashOfConfig": "160"}, {"size": 160, "mtime": 1750359340808, "results": "274", "hashOfConfig": "160"}, {"size": 7170, "mtime": 1751125322875, "results": "275", "hashOfConfig": "160"}, {"size": 12870, "mtime": 1751125612245, "results": "276", "hashOfConfig": "160"}, {"size": 39, "mtime": 1750339601163, "results": "277", "hashOfConfig": "160"}, {"size": 2480, "mtime": 1751030530992, "results": "278", "hashOfConfig": "160"}, {"size": 2988, "mtime": 1751030507186, "results": "279", "hashOfConfig": "160"}, {"size": 6052, "mtime": 1751127912431, "results": "280", "hashOfConfig": "160"}, {"size": 509, "mtime": 1751134476987, "results": "281", "hashOfConfig": "160"}, {"size": 1897, "mtime": 1750345471133, "results": "282", "hashOfConfig": "160"}, {"size": 1235, "mtime": 1750339122385, "results": "283", "hashOfConfig": "160"}, {"size": 4861, "mtime": 1751126227077, "results": "284", "hashOfConfig": "160"}, {"size": 2631, "mtime": 1750444589227, "results": "285", "hashOfConfig": "160"}, {"size": 23710, "mtime": 1751123626588, "results": "286", "hashOfConfig": "160"}, {"size": 4769, "mtime": 1751138474326, "results": "287", "hashOfConfig": "160"}, {"size": 1211, "mtime": 1751033283393, "results": "288", "hashOfConfig": "160"}, {"size": 6071, "mtime": 1750408153054, "results": "289", "hashOfConfig": "160"}, {"size": 5733, "mtime": 1751134476997, "results": "290", "hashOfConfig": "160"}, {"size": 22474, "mtime": 1751136525316, "results": "291", "hashOfConfig": "160"}, {"size": 18517, "mtime": 1751047596027, "results": "292", "hashOfConfig": "160"}, {"size": 7694, "mtime": 1750529141105, "results": "293", "hashOfConfig": "160"}, {"size": 1606, "mtime": 1751136330420, "results": "294", "hashOfConfig": "160"}, {"size": 19032, "mtime": 1751136643119, "results": "295", "hashOfConfig": "160"}, {"size": 6527, "mtime": 1751042468780, "results": "296", "hashOfConfig": "160"}, {"size": 284, "mtime": 1750341618459, "results": "297", "hashOfConfig": "160"}, {"size": 56878, "mtime": 1751046375950, "results": "298", "hashOfConfig": "160"}, {"size": 5828, "mtime": 1750408243860, "results": "299", "hashOfConfig": "160"}, {"size": 18618, "mtime": 1751044385260, "results": "300", "hashOfConfig": "160"}, {"size": 6057, "mtime": 1751033267888, "results": "301", "hashOfConfig": "160"}, {"size": 10715, "mtime": 1750408468672, "results": "302", "hashOfConfig": "160"}, {"size": 8950, "mtime": 1750408342615, "results": "303", "hashOfConfig": "160"}, {"size": 19645, "mtime": 1751137250866, "results": "304", "hashOfConfig": "160"}, {"size": 5750, "mtime": 1751137214421, "results": "305", "hashOfConfig": "160"}, {"size": 4682, "mtime": 1751138793469, "results": "306", "hashOfConfig": "160"}, {"size": 1067, "mtime": 1751136124180, "results": "307", "hashOfConfig": "160"}, {"size": 4174, "mtime": 1751138828951, "results": "308", "hashOfConfig": "160"}, {"size": 2306, "mtime": 1751135118682, "results": "309", "hashOfConfig": "160"}, {"size": 13371, "mtime": 1751136061641, "results": "310", "hashOfConfig": "160"}, {"size": 2921, "mtime": 1751135317700, "results": "311", "hashOfConfig": "160"}, {"size": 7487, "mtime": 1751135953964, "results": "312", "hashOfConfig": "160"}, {"size": 17723, "mtime": 1751136372274, "results": "313", "hashOfConfig": "160"}, {"size": 11255, "mtime": 1751135999440, "results": "314", "hashOfConfig": "160"}, {"size": 1071, "mtime": 1751135050830, "results": "315", "hashOfConfig": "160"}, {"size": 9423, "mtime": 1751135040473, "results": "316", "hashOfConfig": "160"}, {"size": 9421, "mtime": 1750529200692, "results": "317", "hashOfConfig": "160"}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1n6quwe", {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(auth)\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\how-it-works\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(public)\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-matching\\manual\\route.ts", ["792"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\history\\route.ts", ["793", "794"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\reset-all\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\route.ts", ["795", "796"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\test-limit\\route.ts", ["797"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\update-limit\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-points\\user-history\\[userId]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\check\\route.ts", ["798"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposit-settings\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\deposits\\route.ts", ["799"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\all\\route.ts", ["800"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\bulk\\route.ts", ["801"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\pending\\route.ts", ["802"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\review\\route.ts", ["803"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\search\\route.ts", ["804", "805"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\export\\route.ts", ["806"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\route.ts", ["807"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\referral-commissions\\route.ts", ["808", "809", "810", "811"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\referral-commissions\\stats\\route.ts", ["812", "813", "814"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\refresh-tree-cache\\route.ts", ["815"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\pricing\\route.ts", ["816", "817", "818"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\route.ts", ["819"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\stats\\route.ts", ["820"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\route.ts", ["821"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\[ticketId]\\responses\\route.ts", ["822"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\[ticketId]\\route.ts", ["823", "824", "825"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\update-mining-units-roi\\route.ts", ["826", "827"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\update-user-status\\route.ts", ["828"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\action\\route.ts", ["829"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\route.ts", ["830", "831", "832", "833", "834"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\users\\[userId]\\details\\route.ts", ["835"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\wallet\\adjust\\route.ts", ["836", "837", "838", "839"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\withdrawals\\action\\route.ts", ["840", "841", "842"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\withdrawals\\route.ts", ["843"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\login\\route.ts", ["844"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\logout\\route.ts", ["845"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\me\\route.ts", ["846"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\register\\route.ts", ["847"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\binary-points\\info\\route.ts", ["848"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\binary-matching\\route.ts", ["849"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\daily-roi\\route.ts", ["850"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\process-deposits\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\weekly-payout\\route.ts", ["851"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\earnings\\route.ts", ["852"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\errors\\log\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\init\\route.ts", ["853"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\documents\\route.ts", ["854"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\status\\route.ts", ["855"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\submit\\route.ts", ["856"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\upload\\route.ts", ["857", "858", "859", "860"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\route.ts", ["861", "862"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\[id]\\earnings\\route.ts", ["863", "864"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\search\\route.ts", ["865"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\stats\\route.ts", ["866", "867", "868"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\tree\\route.ts", ["869", "870", "871"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\support\\tickets\\route.ts", ["872", "873"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\support\\tickets\\[ticketId]\\responses\\route.ts", ["874"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\test\\binary-settings\\route.ts", ["875", "876"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\notification-settings\\route.ts", ["877", "878"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\withdrawal-address\\route.ts", ["879", "880", "881"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\balance\\route.ts", ["882"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\info\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit\\verify\\route.ts", ["883", "884", "885"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\deposit-address\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\transactions\\route.ts", ["886", "887", "888", "889", "890"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\transactions\\[id]\\route.ts", ["891", "892"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\withdraw\\route.ts", ["893", "894"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\withdrawal-settings\\route.ts", ["895"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\AdminDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\AdminLayout.tsx", ["896", "897"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\BinaryPointsManagement.tsx", ["898", "899", "900", "901", "902", "903"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\DepositManagement.tsx", ["904", "905", "906", "907", "908"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\ErrorLogsViewer.tsx", ["909"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\KYCReview.tsx", ["910", "911", "912", "913", "914", "915", "916", "917"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\ReferralCommissionTracking.tsx", ["918", "919", "920", "921", "922", "923", "924", "925"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\SupportTicketManagement.tsx", ["926", "927", "928", "929"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\SystemLogs.tsx", ["930", "931", "932", "933", "934", "935", "936"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\SystemSettings.tsx", ["937", "938", "939", "940", "941"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\UserManagement.tsx", ["942", "943", "944", "945", "946", "947", "948", "949", "950", "951"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\WithdrawalManagement.tsx", ["952", "953", "954", "955", "956", "957", "958"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\AdvancedBinaryTreeVisualizer.tsx", ["959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\BinaryPointsInfoPanel.tsx", ["972", "973", "974"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\BinaryTreeVisualizer.tsx", ["975"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\D3BinaryTree.tsx", ["976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\DashboardLayout.tsx", ["1002", "1003"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\DashboardOverview.tsx", ["1004", "1005", "1006", "1007"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\EarningsTracker.tsx", ["1008"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\KYCPortal.tsx", ["1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\MiningUnitsTable.tsx", ["1018", "1019", "1020", "1021"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\PurchaseMiningUnit.tsx", ["1022", "1023", "1024", "1025"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\ReactFlowBinaryTree.tsx", ["1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\SupportCenter.tsx", ["1039", "1040", "1041", "1042", "1043", "1044"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\TreeNavigationControls.tsx", ["1045"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\UserProfileSettings.tsx", ["1046"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\dashboard\\WalletDashboard.tsx", ["1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\Cryptocurrency.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\EcoFriendly.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\MiningRig.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\icons\\SolarPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\Container.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\Flex.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\Grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\layout\\PublicLayout.tsx", ["1056"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\pages\\HomePage.tsx", ["1057", "1058", "1059", "1060"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\pages\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\ConfirmDialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\MessageBox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\ui\\Modal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\wallet\\DepositPage.tsx", ["1061", "1062", "1063"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useClientOnly.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useTreeInteractions.ts", ["1064", "1065", "1066", "1067"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\auth.ts", ["1068"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\database.ts", ["1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\depositVerificationService.ts", ["1087", "1088", "1089", "1090"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\errorLogger.ts", ["1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\initServices.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\mining.ts", ["1104", "1105", "1106", "1107", "1108", "1109"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\miningUnitEarnings.ts", ["1110", "1111"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\referral.ts", ["1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\referralValidation.ts", ["1121"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\trongrid.ts", ["1122", "1123"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\utils.ts", ["1124", "1125", "1126", "1127"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\scripts\\validateImplementation.ts", ["1128", "1129", "1130", "1131", "1132", "1133", "1134"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\tests\\integration.test.ts", ["1135", "1136"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\tests\\referral.test.ts", ["1137", "1138"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\smtp\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\scheduler\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\admin\\EmailSettings.tsx", ["1139", "1140", "1141", "1142"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\components\\auth\\AuthGuard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\emailService.ts", ["1143", "1144", "1145", "1146", "1147"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\emailTriggers.ts", ["1148", "1149"], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\initializeApp.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\scheduler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\lib\\clientErrorLogger.tsx", ["1150", "1151", "1152", "1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168"], [], {"ruleId": "1169", "severity": 1, "message": "1170", "line": 53, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 53, "endColumn": 22, "suggestions": "1173"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 31, "column": 24, "nodeType": "1171", "messageId": "1172", "endLine": 31, "endColumn": 27, "suggestions": "1174"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 114, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 114, "endColumn": 22, "suggestions": "1175"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 49, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 49, "endColumn": 22, "suggestions": "1176"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 160, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 160, "endColumn": 22, "suggestions": "1177"}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 3, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 16}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 27, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 27, "endColumn": 22, "suggestions": "1181"}, {"ruleId": "1178", "severity": 1, "message": "1182", "line": 128, "column": 36, "nodeType": null, "messageId": "1180", "endLine": 128, "endColumn": 42}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 85, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 85, "endColumn": 22, "suggestions": "1183"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 143, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 143, "endColumn": 22, "suggestions": "1184"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 67, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 67, "endColumn": 22, "suggestions": "1185"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 109, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 109, "endColumn": 22, "suggestions": "1186"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 35, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 35, "endColumn": 25, "suggestions": "1187"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 128, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 128, "endColumn": 22, "suggestions": "1188"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 26, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 26, "endColumn": 21, "suggestions": "1189"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 30, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 30, "endColumn": 21, "suggestions": "1190"}, {"ruleId": "1178", "severity": 1, "message": "1191", "line": 27, "column": 11, "nodeType": null, "messageId": "1180", "endLine": 27, "endColumn": 15}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 34, "column": 21, "nodeType": "1171", "messageId": "1172", "endLine": 34, "endColumn": 24, "suggestions": "1192"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 42, "column": 24, "nodeType": "1171", "messageId": "1172", "endLine": 42, "endColumn": 27, "suggestions": "1193"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 186, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 186, "endColumn": 22, "suggestions": "1194"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 29, "column": 21, "nodeType": "1171", "messageId": "1172", "endLine": 29, "endColumn": 24, "suggestions": "1195"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 37, "column": 24, "nodeType": "1171", "messageId": "1172", "endLine": 37, "endColumn": 27, "suggestions": "1196"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 113, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 113, "endColumn": 22, "suggestions": "1197"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 33, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 33, "endColumn": 22, "suggestions": "1198"}, {"ruleId": "1178", "severity": 1, "message": "1199", "line": 5, "column": 27, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 34}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 11, "column": 27, "nodeType": "1171", "messageId": "1172", "endLine": 11, "endColumn": 30, "suggestions": "1200"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 47, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 47, "endColumn": 22, "suggestions": "1201"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 22, "column": 27, "nodeType": "1171", "messageId": "1172", "endLine": 22, "endColumn": 30, "suggestions": "1202"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 121, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 121, "endColumn": 22, "suggestions": "1203"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 51, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 51, "endColumn": 22, "suggestions": "1204"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 76, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 76, "endColumn": 22, "suggestions": "1205"}, {"ruleId": "1178", "severity": 1, "message": "1206", "line": 3, "column": 27, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 43}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 43, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 43, "endColumn": 22, "suggestions": "1207"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 107, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 107, "endColumn": 22, "suggestions": "1208"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 53, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 53, "endColumn": 22, "suggestions": "1209"}, {"ruleId": "1178", "severity": 1, "message": "1210", "line": 58, "column": 15, "nodeType": null, "messageId": "1180", "endLine": 58, "endColumn": 28}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 38, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 38, "endColumn": 22, "suggestions": "1211"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 37, "column": 21, "nodeType": "1171", "messageId": "1172", "endLine": 37, "endColumn": 24, "suggestions": "1212"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 45, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 45, "endColumn": 21, "suggestions": "1213"}, {"ruleId": "1214", "severity": 1, "message": "1215", "line": 109, "column": 9, "nodeType": "1216", "messageId": "1217", "endLine": 109, "endColumn": 29, "fix": "1218"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 120, "column": 47, "nodeType": "1171", "messageId": "1172", "endLine": 120, "endColumn": 50, "suggestions": "1219"}, {"ruleId": "1178", "severity": 1, "message": "1220", "line": 124, "column": 15, "nodeType": null, "messageId": "1180", "endLine": 124, "endColumn": 26}, {"ruleId": "1178", "severity": 1, "message": "1221", "line": 124, "column": 28, "nodeType": null, "messageId": "1180", "endLine": 124, "endColumn": 40}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 185, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 185, "endColumn": 22, "suggestions": "1222"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 215, "column": 24, "nodeType": "1171", "messageId": "1172", "endLine": 215, "endColumn": 27, "suggestions": "1223"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 216, "column": 27, "nodeType": "1171", "messageId": "1172", "endLine": 216, "endColumn": 30, "suggestions": "1224"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 217, "column": 31, "nodeType": "1171", "messageId": "1172", "endLine": 217, "endColumn": 34, "suggestions": "1225"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 218, "column": 30, "nodeType": "1171", "messageId": "1172", "endLine": 218, "endColumn": 33, "suggestions": "1226"}, {"ruleId": "1178", "severity": 1, "message": "1227", "line": 4, "column": 57, "nodeType": null, "messageId": "1180", "endLine": 4, "endColumn": 70}, {"ruleId": "1214", "severity": 1, "message": "1228", "line": 41, "column": 9, "nodeType": "1216", "messageId": "1217", "endLine": 41, "endColumn": 24, "fix": "1229"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 41, "column": 21, "nodeType": "1171", "messageId": "1172", "endLine": 41, "endColumn": 24, "suggestions": "1230"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 23, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 23, "endColumn": 21, "suggestions": "1231"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 61, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 61, "endColumn": 22, "suggestions": "1232"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 38, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 38, "endColumn": 22, "suggestions": "1233"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 30, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 30, "endColumn": 22, "suggestions": "1234"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 86, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 86, "endColumn": 22, "suggestions": "1235"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 142, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 142, "endColumn": 22, "suggestions": "1236"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 49, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 49, "endColumn": 22, "suggestions": "1237"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 55, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 55, "endColumn": 22, "suggestions": "1238"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 48, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 48, "endColumn": 22, "suggestions": "1239"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 83, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 83, "endColumn": 22, "suggestions": "1240"}, {"ruleId": "1178", "severity": 1, "message": "1199", "line": 10, "column": 27, "nodeType": null, "messageId": "1180", "endLine": 10, "endColumn": 34}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 37, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 37, "endColumn": 22, "suggestions": "1241"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 92, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 92, "endColumn": 22, "suggestions": "1242"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 127, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 127, "endColumn": 22, "suggestions": "1243"}, {"ruleId": "1178", "severity": 1, "message": "1244", "line": 79, "column": 14, "nodeType": null, "messageId": "1180", "endLine": 79, "endColumn": 19}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 99, "column": 24, "nodeType": "1171", "messageId": "1172", "endLine": 99, "endColumn": 27, "suggestions": "1245"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 130, "column": 25, "nodeType": "1171", "messageId": "1172", "endLine": 130, "endColumn": 28, "suggestions": "1246"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 209, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 209, "endColumn": 22, "suggestions": "1247"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 60, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 60, "endColumn": 22, "suggestions": "1248"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 196, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 196, "endColumn": 22, "suggestions": "1249"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 65, "column": 29, "nodeType": "1171", "messageId": "1172", "endLine": 65, "endColumn": 32, "suggestions": "1250"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 105, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 105, "endColumn": 22, "suggestions": "1251"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 36, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 36, "endColumn": 22, "suggestions": "1252"}, {"ruleId": "1214", "severity": 1, "message": "1253", "line": 26, "column": 9, "nodeType": "1216", "messageId": "1217", "endLine": 26, "endColumn": 26, "fix": "1254"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 26, "column": 23, "nodeType": "1171", "messageId": "1172", "endLine": 26, "endColumn": 26, "suggestions": "1255"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 73, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 73, "endColumn": 22, "suggestions": "1256"}, {"ruleId": "1214", "severity": 1, "message": "1253", "line": 76, "column": 9, "nodeType": "1216", "messageId": "1217", "endLine": 76, "endColumn": 26, "fix": "1257"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 76, "column": 23, "nodeType": "1171", "messageId": "1172", "endLine": 76, "endColumn": 26, "suggestions": "1258"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 111, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 111, "endColumn": 22, "suggestions": "1259"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 24, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 24, "endColumn": 22, "suggestions": "1260"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 81, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 81, "endColumn": 22, "suggestions": "1261"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 75, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 75, "endColumn": 22, "suggestions": "1262"}, {"ruleId": "1178", "severity": 1, "message": "1199", "line": 5, "column": 27, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 34}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 19, "column": 27, "nodeType": "1171", "messageId": "1172", "endLine": 19, "endColumn": 30, "suggestions": "1263"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 30, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 30, "endColumn": 22, "suggestions": "1264"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 70, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 70, "endColumn": 22, "suggestions": "1265"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 43, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 43, "endColumn": 22, "suggestions": "1266"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 93, "column": 23, "nodeType": "1171", "messageId": "1172", "endLine": 93, "endColumn": 26, "suggestions": "1267"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 105, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 105, "endColumn": 22, "suggestions": "1268"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 106, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 106, "endColumn": 22, "suggestions": "1269"}, {"ruleId": "1178", "severity": 1, "message": "1270", "line": 5, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 18}, {"ruleId": "1178", "severity": 1, "message": "1271", "line": 10, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 10, "endColumn": 31}, {"ruleId": "1178", "severity": 1, "message": "1272", "line": 161, "column": 11, "nodeType": null, "messageId": "1180", "endLine": 161, "endColumn": 24}, {"ruleId": "1178", "severity": 1, "message": "1273", "line": 3, "column": 25, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 45}, {"ruleId": "1178", "severity": 1, "message": "1179", "line": 4, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 4, "endColumn": 16}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 29, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 29, "endColumn": 21, "suggestions": "1274"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 51, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 51, "endColumn": 25, "suggestions": "1275"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 184, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 184, "endColumn": 22, "suggestions": "1276"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 37, "column": 28, "nodeType": "1171", "messageId": "1172", "endLine": 37, "endColumn": 31, "suggestions": "1277"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 94, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 94, "endColumn": 22, "suggestions": "1278"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 145, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 145, "endColumn": 22, "suggestions": "1279"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 190, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 190, "endColumn": 22, "suggestions": "1280"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 33, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 33, "endColumn": 22, "suggestions": "1281"}, {"ruleId": "1178", "severity": 1, "message": "1282", "line": 6, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 19}, {"ruleId": "1178", "severity": 1, "message": "1283", "line": 7, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 7, "endColumn": 16}, {"ruleId": "1178", "severity": 1, "message": "1284", "line": 8, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1285", "line": 19, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 19, "endColumn": 4}, {"ruleId": "1178", "severity": 1, "message": "1286", "line": 63, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 63, "endColumn": 22}, {"ruleId": "1178", "severity": 1, "message": "1287", "line": 67, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 67, "endColumn": 22}, {"ruleId": "1288", "severity": 1, "message": "1289", "line": 90, "column": 6, "nodeType": "1290", "endLine": 90, "endColumn": 8, "suggestions": "1291"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 442, "column": 68, "nodeType": "1171", "messageId": "1172", "endLine": 442, "endColumn": 71, "suggestions": "1292"}, {"ruleId": "1178", "severity": 1, "message": "1293", "line": 13, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 13, "endColumn": 11}, {"ruleId": "1294", "severity": 1, "message": "1295", "line": 26, "column": 11, "nodeType": "1216", "messageId": "1296", "endLine": 26, "endColumn": 33, "suggestions": "1297"}, {"ruleId": "1178", "severity": 1, "message": "1298", "line": 39, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 39, "endColumn": 23}, {"ruleId": "1178", "severity": 1, "message": "1299", "line": 72, "column": 9, "nodeType": null, "messageId": "1180", "endLine": 72, "endColumn": 28}, {"ruleId": "1288", "severity": 1, "message": "1300", "line": 110, "column": 6, "nodeType": "1290", "endLine": 110, "endColumn": 22, "suggestions": "1301"}, {"ruleId": "1288", "severity": 1, "message": "1302", "line": 45, "column": 6, "nodeType": "1290", "endLine": 45, "endColumn": 28, "suggestions": "1303"}, {"ruleId": "1178", "severity": 1, "message": "1304", "line": 4, "column": 16, "nodeType": null, "messageId": "1180", "endLine": 4, "endColumn": 26}, {"ruleId": "1178", "severity": 1, "message": "1305", "line": 4, "column": 28, "nodeType": null, "messageId": "1180", "endLine": 4, "endColumn": 37}, {"ruleId": "1178", "severity": 1, "message": "1306", "line": 7, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 7, "endColumn": 6}, {"ruleId": "1178", "severity": 1, "message": "1293", "line": 10, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 10, "endColumn": 11}, {"ruleId": "1178", "severity": 1, "message": "1284", "line": 16, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 16, "endColumn": 9}, {"ruleId": "1288", "severity": 1, "message": "1307", "line": 73, "column": 6, "nodeType": "1290", "endLine": 73, "endColumn": 39, "suggestions": "1308"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 212, "column": 68, "nodeType": "1171", "messageId": "1172", "endLine": 212, "endColumn": 71, "suggestions": "1309"}, {"ruleId": "1310", "severity": 1, "message": "1311", "line": 382, "column": 15, "nodeType": "1312", "endLine": 386, "endColumn": 17}, {"ruleId": "1178", "severity": 1, "message": "1284", "line": 8, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1306", "line": 10, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 10, "endColumn": 6}, {"ruleId": "1178", "severity": 1, "message": "1287", "line": 70, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 70, "endColumn": 22}, {"ruleId": "1178", "severity": 1, "message": "1313", "line": 70, "column": 24, "nodeType": null, "messageId": "1180", "endLine": 70, "endColumn": 39}, {"ruleId": "1288", "severity": 1, "message": "1314", "line": 81, "column": 6, "nodeType": "1290", "endLine": 81, "endColumn": 56, "suggestions": "1315"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 287, "column": 65, "nodeType": "1171", "messageId": "1172", "endLine": 287, "endColumn": 68, "suggestions": "1316"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 298, "column": 66, "nodeType": "1171", "messageId": "1172", "endLine": 298, "endColumn": 69, "suggestions": "1317"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 308, "column": 68, "nodeType": "1171", "messageId": "1172", "endLine": 308, "endColumn": 71, "suggestions": "1318"}, {"ruleId": "1178", "severity": 1, "message": "1319", "line": 5, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 14}, {"ruleId": "1178", "severity": 1, "message": "1284", "line": 12, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 12, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1320", "line": 16, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 16, "endColumn": 6}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 98, "column": 68, "nodeType": "1171", "messageId": "1172", "endLine": 98, "endColumn": 71, "suggestions": "1321"}, {"ruleId": "1178", "severity": 1, "message": "1284", "line": 8, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1322", "line": 10, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 10, "endColumn": 11}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 29, "column": 12, "nodeType": "1171", "messageId": "1172", "endLine": 29, "endColumn": 15, "suggestions": "1323"}, {"ruleId": "1288", "severity": 1, "message": "1302", "line": 46, "column": 6, "nodeType": "1290", "endLine": 46, "endColumn": 56, "suggestions": "1324"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 190, "column": 37, "nodeType": "1171", "messageId": "1172", "endLine": 190, "endColumn": 40, "suggestions": "1325"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 209, "column": 38, "nodeType": "1171", "messageId": "1172", "endLine": 209, "endColumn": 41, "suggestions": "1326"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 362, "column": 65, "nodeType": "1171", "messageId": "1172", "endLine": 362, "endColumn": 68, "suggestions": "1327"}, {"ruleId": "1178", "severity": 1, "message": "1328", "line": 6, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 11}, {"ruleId": "1178", "severity": 1, "message": "1329", "line": 11, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 11, "endColumn": 8}, {"ruleId": "1178", "severity": 1, "message": "1330", "line": 12, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 12, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1331", "line": 20, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 20, "endColumn": 24}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 145, "column": 60, "nodeType": "1171", "messageId": "1172", "endLine": 145, "endColumn": 63, "suggestions": "1332"}, {"ruleId": "1178", "severity": 1, "message": "1333", "line": 3, "column": 38, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 49}, {"ruleId": "1178", "severity": 1, "message": "1319", "line": 5, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 14}, {"ruleId": "1178", "severity": 1, "message": "1284", "line": 9, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 9, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1334", "line": 17, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 17, "endColumn": 7}, {"ruleId": "1178", "severity": 1, "message": "1335", "line": 18, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 18, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1336", "line": 27, "column": 42, "nodeType": null, "messageId": "1180", "endLine": 27, "endColumn": 50}, {"ruleId": "1288", "severity": 1, "message": "1337", "line": 148, "column": 6, "nodeType": "1290", "endLine": 148, "endColumn": 27, "suggestions": "1338"}, {"ruleId": "1288", "severity": 1, "message": "1339", "line": 152, "column": 6, "nodeType": "1290", "endLine": 152, "endColumn": 73, "suggestions": "1340"}, {"ruleId": "1178", "severity": 1, "message": "1244", "line": 291, "column": 14, "nodeType": null, "messageId": "1180", "endLine": 291, "endColumn": 19}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 389, "column": 68, "nodeType": "1171", "messageId": "1172", "endLine": 389, "endColumn": 71, "suggestions": "1341"}, {"ruleId": "1178", "severity": 1, "message": "1304", "line": 4, "column": 16, "nodeType": null, "messageId": "1180", "endLine": 4, "endColumn": 26}, {"ruleId": "1178", "severity": 1, "message": "1305", "line": 4, "column": 28, "nodeType": null, "messageId": "1180", "endLine": 4, "endColumn": 37}, {"ruleId": "1178", "severity": 1, "message": "1284", "line": 8, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1342", "line": 15, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 15, "endColumn": 16}, {"ruleId": "1288", "severity": 1, "message": "1343", "line": 52, "column": 6, "nodeType": "1290", "endLine": 52, "endColumn": 32, "suggestions": "1344"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 179, "column": 68, "nodeType": "1171", "messageId": "1172", "endLine": 179, "endColumn": 71, "suggestions": "1345"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 382, "column": 31, "nodeType": "1171", "messageId": "1172", "endLine": 382, "endColumn": 34, "suggestions": "1346"}, {"ruleId": "1178", "severity": 1, "message": "1347", "line": 8, "column": 41, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 45}, {"ruleId": "1178", "severity": 1, "message": "1322", "line": 8, "column": 47, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 55}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 122, "column": 54, "nodeType": "1171", "messageId": "1172", "endLine": 122, "endColumn": 57, "suggestions": "1348"}, {"ruleId": "1288", "severity": 1, "message": "1349", "line": 248, "column": 6, "nodeType": "1290", "endLine": 248, "endColumn": 8, "suggestions": "1350"}, {"ruleId": "1178", "severity": 1, "message": "1351", "line": 963, "column": 11, "nodeType": null, "messageId": "1180", "endLine": 963, "endColumn": 27}, {"ruleId": "1178", "severity": 1, "message": "1352", "line": 1104, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 1104, "endColumn": 20}, {"ruleId": "1178", "severity": 1, "message": "1353", "line": 1105, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 1105, "endColumn": 18}, {"ruleId": "1178", "severity": 1, "message": "1354", "line": 1106, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 1106, "endColumn": 17}, {"ruleId": "1178", "severity": 1, "message": "1355", "line": 1226, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 1226, "endColumn": 12}, {"ruleId": "1178", "severity": 1, "message": "1352", "line": 1234, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 1234, "endColumn": 20}, {"ruleId": "1178", "severity": 1, "message": "1353", "line": 1235, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 1235, "endColumn": 18}, {"ruleId": "1178", "severity": 1, "message": "1354", "line": 1236, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 1236, "endColumn": 17}, {"ruleId": "1178", "severity": 1, "message": "1356", "line": 1237, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 1237, "endColumn": 11}, {"ruleId": "1178", "severity": 1, "message": "1357", "line": 11, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 11, "endColumn": 8}, {"ruleId": "1178", "severity": 1, "message": "1358", "line": 14, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 14, "endColumn": 6}, {"ruleId": "1178", "severity": 1, "message": "1322", "line": 15, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 15, "endColumn": 11}, {"ruleId": "1178", "severity": 1, "message": "1359", "line": 7, "column": 26, "nodeType": null, "messageId": "1180", "endLine": 7, "endColumn": 40}, {"ruleId": "1178", "severity": 1, "message": "1319", "line": 5, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 14}, {"ruleId": "1178", "severity": 1, "message": "1360", "line": 8, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1361", "line": 8, "column": 11, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 18}, {"ruleId": "1178", "severity": 1, "message": "1362", "line": 8, "column": 20, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 29}, {"ruleId": "1178", "severity": 1, "message": "1331", "line": 10, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 10, "endColumn": 24}, {"ruleId": "1178", "severity": 1, "message": "1359", "line": 10, "column": 26, "nodeType": null, "messageId": "1180", "endLine": 10, "endColumn": 40}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 206, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 206, "endColumn": 25, "suggestions": "1363"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 229, "column": 30, "nodeType": "1171", "messageId": "1172", "endLine": 229, "endColumn": 33, "suggestions": "1364"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 240, "column": 28, "nodeType": "1171", "messageId": "1172", "endLine": 240, "endColumn": 31, "suggestions": "1365"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 249, "column": 34, "nodeType": "1171", "messageId": "1172", "endLine": 249, "endColumn": 37, "suggestions": "1366"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 261, "column": 26, "nodeType": "1171", "messageId": "1172", "endLine": 261, "endColumn": 29, "suggestions": "1367"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 273, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 273, "endColumn": 20, "suggestions": "1368"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 288, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 288, "endColumn": 20, "suggestions": "1369"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 298, "column": 26, "nodeType": "1171", "messageId": "1172", "endLine": 298, "endColumn": 29, "suggestions": "1370"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 299, "column": 28, "nodeType": "1171", "messageId": "1172", "endLine": 299, "endColumn": 31, "suggestions": "1371"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 310, "column": 26, "nodeType": "1171", "messageId": "1172", "endLine": 310, "endColumn": 29, "suggestions": "1372"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 311, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 311, "endColumn": 20, "suggestions": "1373"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 315, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 315, "endColumn": 22, "suggestions": "1374"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 322, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 322, "endColumn": 20, "suggestions": "1375"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 333, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 333, "endColumn": 20, "suggestions": "1376"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 336, "column": 50, "nodeType": "1171", "messageId": "1172", "endLine": 336, "endColumn": 53, "suggestions": "1377"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 344, "column": 26, "nodeType": "1171", "messageId": "1172", "endLine": 344, "endColumn": 29, "suggestions": "1378"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 350, "column": 39, "nodeType": "1171", "messageId": "1172", "endLine": 350, "endColumn": 42, "suggestions": "1379"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 368, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 368, "endColumn": 20, "suggestions": "1380"}, {"ruleId": "1288", "severity": 1, "message": "1381", "line": 459, "column": 6, "nodeType": "1290", "endLine": 459, "endColumn": 67, "suggestions": "1382"}, {"ruleId": "1288", "severity": 1, "message": "1349", "line": 483, "column": 6, "nodeType": "1290", "endLine": 483, "endColumn": 21, "suggestions": "1383"}, {"ruleId": "1178", "severity": 1, "message": "1282", "line": 7, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 7, "endColumn": 19}, {"ruleId": "1178", "severity": 1, "message": "1384", "line": 25, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 25, "endColumn": 13}, {"ruleId": "1178", "severity": 1, "message": "1385", "line": 5, "column": 16, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 24}, {"ruleId": "1178", "severity": 1, "message": "1386", "line": 6, "column": 37, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 47}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 79, "column": 69, "nodeType": "1171", "messageId": "1172", "endLine": 79, "endColumn": 72, "suggestions": "1387"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 82, "column": 59, "nodeType": "1171", "messageId": "1172", "endLine": 82, "endColumn": 62, "suggestions": "1388"}, {"ruleId": "1178", "severity": 1, "message": "1385", "line": 5, "column": 16, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 24}, {"ruleId": "1178", "severity": 1, "message": "1389", "line": 5, "column": 60, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 65}, {"ruleId": "1178", "severity": 1, "message": "1390", "line": 7, "column": 78, "nodeType": null, "messageId": "1180", "endLine": 7, "endColumn": 84}, {"ruleId": "1178", "severity": 1, "message": "1391", "line": 22, "column": 11, "nodeType": null, "messageId": "1180", "endLine": 22, "endColumn": 24}, {"ruleId": "1178", "severity": 1, "message": "1392", "line": 34, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 34, "endColumn": 19}, {"ruleId": "1178", "severity": 1, "message": "1393", "line": 34, "column": 21, "nodeType": null, "messageId": "1180", "endLine": 34, "endColumn": 33}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 152, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 152, "endColumn": 22, "suggestions": "1394"}, {"ruleId": "1178", "severity": 1, "message": "1395", "line": 185, "column": 9, "nodeType": null, "messageId": "1180", "endLine": 185, "endColumn": 26}, {"ruleId": "1310", "severity": 1, "message": "1311", "line": 276, "column": 15, "nodeType": "1312", "endLine": 280, "endColumn": 17}, {"ruleId": "1310", "severity": 1, "message": "1311", "line": 432, "column": 23, "nodeType": "1312", "endLine": 436, "endColumn": 25}, {"ruleId": "1178", "severity": 1, "message": "1322", "line": 6, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 18}, {"ruleId": "1178", "severity": 1, "message": "1396", "line": 6, "column": 20, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 30}, {"ruleId": "1178", "severity": 1, "message": "1397", "line": 6, "column": 32, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 42}, {"ruleId": "1178", "severity": 1, "message": "1398", "line": 78, "column": 9, "nodeType": null, "messageId": "1180", "endLine": 78, "endColumn": 25}, {"ruleId": "1178", "severity": 1, "message": "1399", "line": 31, "column": 24, "nodeType": null, "messageId": "1180", "endLine": 31, "endColumn": 35}, {"ruleId": "1178", "severity": 1, "message": "1400", "line": 32, "column": 24, "nodeType": null, "messageId": "1180", "endLine": 32, "endColumn": 35}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 153, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 153, "endColumn": 22, "suggestions": "1401"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 199, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 199, "endColumn": 22, "suggestions": "1402"}, {"ruleId": "1178", "severity": 1, "message": "1403", "line": 3, "column": 51, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 58}, {"ruleId": "1178", "severity": 1, "message": "1404", "line": 12, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 12, "endColumn": 10}, {"ruleId": "1178", "severity": 1, "message": "1405", "line": 13, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 13, "endColumn": 13}, {"ruleId": "1178", "severity": 1, "message": "1406", "line": 15, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 15, "endColumn": 8}, {"ruleId": "1178", "severity": 1, "message": "1360", "line": 29, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 29, "endColumn": 9}, {"ruleId": "1178", "severity": 1, "message": "1361", "line": 30, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 30, "endColumn": 10}, {"ruleId": "1178", "severity": 1, "message": "1407", "line": 31, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 31, "endColumn": 11}, {"ruleId": "1178", "severity": 1, "message": "1331", "line": 35, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 35, "endColumn": 24}, {"ruleId": "1178", "severity": 1, "message": "1408", "line": 35, "column": 26, "nodeType": null, "messageId": "1180", "endLine": 35, "endColumn": 36}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 91, "column": 52, "nodeType": "1171", "messageId": "1172", "endLine": 91, "endColumn": 55, "suggestions": "1409"}, {"ruleId": "1178", "severity": 1, "message": "1410", "line": 251, "column": 7, "nodeType": null, "messageId": "1180", "endLine": 251, "endColumn": 21}, {"ruleId": "1288", "severity": 1, "message": "1411", "line": 366, "column": 6, "nodeType": "1290", "endLine": 366, "endColumn": 58, "suggestions": "1412"}, {"ruleId": "1288", "severity": 1, "message": "1349", "line": 397, "column": 6, "nodeType": "1290", "endLine": 397, "endColumn": 21, "suggestions": "1413"}, {"ruleId": "1178", "severity": 1, "message": "1319", "line": 5, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 5, "endColumn": 14}, {"ruleId": "1178", "severity": 1, "message": "1414", "line": 13, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 13, "endColumn": 7}, {"ruleId": "1178", "severity": 1, "message": "1328", "line": 14, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 14, "endColumn": 11}, {"ruleId": "1178", "severity": 1, "message": "1415", "line": 15, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 15, "endColumn": 7}, {"ruleId": "1178", "severity": 1, "message": "1416", "line": 16, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 16, "endColumn": 13}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 281, "column": 97, "nodeType": "1171", "messageId": "1172", "endLine": 281, "endColumn": 100, "suggestions": "1417"}, {"ruleId": "1178", "severity": 1, "message": "1284", "line": 7, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 7, "endColumn": 9}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 490, "column": 21, "nodeType": "1171", "messageId": "1172", "endLine": 490, "endColumn": 24, "suggestions": "1418"}, {"ruleId": "1178", "severity": 1, "message": "1397", "line": 6, "column": 82, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 92}, {"ruleId": "1178", "severity": 1, "message": "1419", "line": 56, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 56, "endColumn": 27}, {"ruleId": "1178", "severity": 1, "message": "1420", "line": 56, "column": 29, "nodeType": null, "messageId": "1180", "endLine": 56, "endColumn": 49}, {"ruleId": "1178", "severity": 1, "message": "1399", "line": 66, "column": 24, "nodeType": null, "messageId": "1180", "endLine": 66, "endColumn": 35}, {"ruleId": "1178", "severity": 1, "message": "1400", "line": 67, "column": 24, "nodeType": null, "messageId": "1180", "endLine": 67, "endColumn": 35}, {"ruleId": "1288", "severity": 1, "message": "1421", "line": 99, "column": 6, "nodeType": "1290", "endLine": 99, "endColumn": 8, "suggestions": "1422"}, {"ruleId": "1288", "severity": 1, "message": "1423", "line": 104, "column": 6, "nodeType": "1290", "endLine": 104, "endColumn": 44, "suggestions": "1424"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 308, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 308, "endColumn": 22, "suggestions": "1425"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 351, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 351, "endColumn": 22, "suggestions": "1426"}, {"ruleId": "1178", "severity": 1, "message": "1427", "line": 7, "column": 22, "nodeType": null, "messageId": "1180", "endLine": 7, "endColumn": 26}, {"ruleId": "1178", "severity": 1, "message": "1385", "line": 6, "column": 27, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 35}, {"ruleId": "1178", "severity": 1, "message": "1428", "line": 6, "column": 37, "nodeType": null, "messageId": "1180", "endLine": 6, "endColumn": 41}, {"ruleId": "1178", "severity": 1, "message": "1329", "line": 8, "column": 42, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 47}, {"ruleId": "1178", "severity": 1, "message": "1358", "line": 8, "column": 49, "nodeType": null, "messageId": "1180", "endLine": 8, "endColumn": 52}, {"ruleId": "1178", "severity": 1, "message": "1429", "line": 4, "column": 8, "nodeType": null, "messageId": "1180", "endLine": 4, "endColumn": 13}, {"ruleId": "1178", "severity": 1, "message": "1430", "line": 12, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 12, "endColumn": 15}, {"ruleId": "1288", "severity": 1, "message": "1431", "line": 265, "column": 6, "nodeType": "1290", "endLine": 265, "endColumn": 23, "suggestions": "1432"}, {"ruleId": "1178", "severity": 1, "message": "1433", "line": 37, "column": 9, "nodeType": null, "messageId": "1180", "endLine": 37, "endColumn": 19}, {"ruleId": "1178", "severity": 1, "message": "1434", "line": 38, "column": 9, "nodeType": null, "messageId": "1180", "endLine": 38, "endColumn": 21}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 136, "column": 54, "nodeType": "1171", "messageId": "1172", "endLine": 136, "endColumn": 57, "suggestions": "1435"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 182, "column": 44, "nodeType": "1171", "messageId": "1172", "endLine": 182, "endColumn": 47, "suggestions": "1436"}, {"ruleId": "1178", "severity": 1, "message": "1244", "line": 27, "column": 12, "nodeType": null, "messageId": "1180", "endLine": 27, "endColumn": 17}, {"ruleId": "1178", "severity": 1, "message": "1347", "line": 2, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 2, "endColumn": 14}, {"ruleId": "1178", "severity": 1, "message": "1437", "line": 2, "column": 16, "nodeType": null, "messageId": "1180", "endLine": 2, "endColumn": 26}, {"ruleId": "1178", "severity": 1, "message": "1438", "line": 2, "column": 28, "nodeType": null, "messageId": "1180", "endLine": 2, "endColumn": 39}, {"ruleId": "1178", "severity": 1, "message": "1439", "line": 2, "column": 41, "nodeType": null, "messageId": "1180", "endLine": 2, "endColumn": 49}, {"ruleId": "1178", "severity": 1, "message": "1440", "line": 2, "column": 51, "nodeType": null, "messageId": "1180", "endLine": 2, "endColumn": 63}, {"ruleId": "1178", "severity": 1, "message": "1441", "line": 2, "column": 80, "nodeType": null, "messageId": "1180", "endLine": 2, "endColumn": 98}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 148, "column": 23, "nodeType": "1171", "messageId": "1172", "endLine": 148, "endColumn": 26, "suggestions": "1442"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 199, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 199, "endColumn": 21, "suggestions": "1443"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 245, "column": 23, "nodeType": "1171", "messageId": "1172", "endLine": 245, "endColumn": 26, "suggestions": "1444"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 287, "column": 23, "nodeType": "1171", "messageId": "1172", "endLine": 287, "endColumn": 26, "suggestions": "1445"}, {"ruleId": "1178", "severity": 1, "message": "1446", "line": 444, "column": 41, "nodeType": null, "messageId": "1180", "endLine": 444, "endColumn": 50}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 463, "column": 15, "nodeType": "1171", "messageId": "1172", "endLine": 463, "endColumn": 18, "suggestions": "1447"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 614, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 614, "endColumn": 21, "suggestions": "1448"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 643, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 643, "endColumn": 21, "suggestions": "1449"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 677, "column": 23, "nodeType": "1171", "messageId": "1172", "endLine": 677, "endColumn": 26, "suggestions": "1450"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 770, "column": 24, "nodeType": "1171", "messageId": "1172", "endLine": 770, "endColumn": 27, "suggestions": "1451"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 887, "column": 44, "nodeType": "1171", "messageId": "1172", "endLine": 887, "endColumn": 47, "suggestions": "1452"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 920, "column": 24, "nodeType": "1171", "messageId": "1172", "endLine": 920, "endColumn": 27, "suggestions": "1453"}, {"ruleId": "1178", "severity": 1, "message": "1270", "line": 3, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 18}, {"ruleId": "1178", "severity": 1, "message": "1227", "line": 4, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 4, "endColumn": 16}, {"ruleId": "1178", "severity": 1, "message": "1454", "line": 10, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 10, "endColumn": 23}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 169, "column": 95, "nodeType": "1171", "messageId": "1172", "endLine": 169, "endColumn": 98, "suggestions": "1455"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 12, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 12, "endColumn": 20, "suggestions": "1456"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 13, "column": 20, "nodeType": "1171", "messageId": "1172", "endLine": 13, "endColumn": 23, "suggestions": "1457"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 23, "column": 20, "nodeType": "1171", "messageId": "1172", "endLine": 23, "endColumn": 23, "suggestions": "1458"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 78, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 78, "endColumn": 25, "suggestions": "1459"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 81, "column": 24, "nodeType": "1171", "messageId": "1172", "endLine": 81, "endColumn": 27, "suggestions": "1460"}, {"ruleId": "1178", "severity": 1, "message": "1461", "line": 94, "column": 16, "nodeType": null, "messageId": "1180", "endLine": 94, "endColumn": 25}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 147, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 147, "endColumn": 25, "suggestions": "1462"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 167, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 167, "endColumn": 25, "suggestions": "1463"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 196, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 196, "endColumn": 25, "suggestions": "1464"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 215, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 215, "endColumn": 25, "suggestions": "1465"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 231, "column": 13, "nodeType": "1171", "messageId": "1172", "endLine": 231, "endColumn": 16, "suggestions": "1466"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 233, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 233, "endColumn": 25, "suggestions": "1467"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 247, "column": 44, "nodeType": "1171", "messageId": "1172", "endLine": 247, "endColumn": 47, "suggestions": "1468"}, {"ruleId": "1178", "severity": 1, "message": "1469", "line": 3, "column": 35, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 59}, {"ruleId": "1178", "severity": 1, "message": "1470", "line": 3, "column": 61, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 77}, {"ruleId": "1178", "severity": 1, "message": "1471", "line": 3, "column": 79, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 95}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 35, "column": 57, "nodeType": "1171", "messageId": "1172", "endLine": 35, "endColumn": 60, "suggestions": "1472"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 92, "column": 57, "nodeType": "1171", "messageId": "1172", "endLine": 92, "endColumn": 60, "suggestions": "1473"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 145, "column": 57, "nodeType": "1171", "messageId": "1172", "endLine": 145, "endColumn": 60, "suggestions": "1474"}, {"ruleId": "1178", "severity": 1, "message": "1227", "line": 2, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 2, "endColumn": 23}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 98, "column": 25, "nodeType": "1171", "messageId": "1172", "endLine": 98, "endColumn": 28, "suggestions": "1475"}, {"ruleId": "1178", "severity": 1, "message": "1469", "line": 3, "column": 35, "nodeType": null, "messageId": "1180", "endLine": 3, "endColumn": 59}, {"ruleId": "1214", "severity": 1, "message": "1476", "line": 687, "column": 13, "nodeType": "1216", "messageId": "1217", "endLine": 687, "endColumn": 43, "fix": "1477"}, {"ruleId": "1178", "severity": 1, "message": "1478", "line": 687, "column": 13, "nodeType": null, "messageId": "1180", "endLine": 687, "endColumn": 25}, {"ruleId": "1178", "severity": 1, "message": "1479", "line": 763, "column": 16, "nodeType": null, "messageId": "1180", "endLine": 763, "endColumn": 36}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 1291, "column": 103, "nodeType": "1171", "messageId": "1172", "endLine": 1291, "endColumn": 106, "suggestions": "1480"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 1385, "column": 14, "nodeType": "1171", "messageId": "1172", "endLine": 1385, "endColumn": 17, "suggestions": "1481"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 1386, "column": 15, "nodeType": "1171", "messageId": "1172", "endLine": 1386, "endColumn": 18, "suggestions": "1482"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 1414, "column": 45, "nodeType": "1171", "messageId": "1172", "endLine": 1414, "endColumn": 48, "suggestions": "1483"}, {"ruleId": "1178", "severity": 1, "message": "1484", "line": 1486, "column": 11, "nodeType": null, "messageId": "1180", "endLine": 1486, "endColumn": 24}, {"ruleId": "1178", "severity": 1, "message": "1244", "line": 57, "column": 12, "nodeType": null, "messageId": "1180", "endLine": 57, "endColumn": 17}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 140, "column": 93, "nodeType": "1171", "messageId": "1172", "endLine": 140, "endColumn": 96, "suggestions": "1485"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 340, "column": 16, "nodeType": "1171", "messageId": "1172", "endLine": 340, "endColumn": 19, "suggestions": "1486"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 65, "column": 46, "nodeType": "1171", "messageId": "1172", "endLine": 65, "endColumn": 49, "suggestions": "1487"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 65, "column": 56, "nodeType": "1171", "messageId": "1172", "endLine": 65, "endColumn": 59, "suggestions": "1488"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 76, "column": 46, "nodeType": "1171", "messageId": "1172", "endLine": 76, "endColumn": 49, "suggestions": "1489"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 76, "column": 56, "nodeType": "1171", "messageId": "1172", "endLine": 76, "endColumn": 59, "suggestions": "1490"}, {"ruleId": "1178", "severity": 1, "message": "1491", "line": 15, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 15, "endColumn": 20}, {"ruleId": "1178", "severity": 1, "message": "1492", "line": 16, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 16, "endColumn": 29}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 24, "column": 13, "nodeType": "1171", "messageId": "1172", "endLine": 24, "endColumn": 16, "suggestions": "1493"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 30, "column": 79, "nodeType": "1171", "messageId": "1172", "endLine": 30, "endColumn": 82, "suggestions": "1494"}, {"ruleId": "1178", "severity": 1, "message": "1495", "line": 43, "column": 13, "nodeType": null, "messageId": "1180", "endLine": 43, "endColumn": 23}, {"ruleId": "1178", "severity": 1, "message": "1496", "line": 68, "column": 13, "nodeType": null, "messageId": "1180", "endLine": 68, "endColumn": 27}, {"ruleId": "1178", "severity": 1, "message": "1497", "line": 90, "column": 13, "nodeType": null, "messageId": "1180", "endLine": 90, "endColumn": 31}, {"ruleId": "1178", "severity": 1, "message": "1498", "line": 118, "column": 13, "nodeType": null, "messageId": "1180", "endLine": 118, "endColumn": 18}, {"ruleId": "1178", "severity": 1, "message": "1499", "line": 126, "column": 13, "nodeType": null, "messageId": "1180", "endLine": 126, "endColumn": 18}, {"ruleId": "1178", "severity": 1, "message": "1500", "line": 12, "column": 3, "nodeType": null, "messageId": "1180", "endLine": 12, "endColumn": 25}, {"ruleId": "1178", "severity": 1, "message": "1501", "line": 101, "column": 13, "nodeType": null, "messageId": "1180", "endLine": 101, "endColumn": 18}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 23, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 23, "endColumn": 20, "suggestions": "1502"}, {"ruleId": "1178", "severity": 1, "message": "1244", "line": 113, "column": 14, "nodeType": null, "messageId": "1180", "endLine": 113, "endColumn": 19}, {"ruleId": "1178", "severity": 1, "message": "1244", "line": 139, "column": 14, "nodeType": null, "messageId": "1180", "endLine": 139, "endColumn": 19}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 146, "column": 62, "nodeType": "1171", "messageId": "1172", "endLine": 146, "endColumn": 65, "suggestions": "1503"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 10, "column": 30, "nodeType": "1171", "messageId": "1172", "endLine": 10, "endColumn": 33, "suggestions": "1504"}, {"ruleId": "1178", "severity": 1, "message": "1505", "line": 93, "column": 13, "nodeType": null, "messageId": "1180", "endLine": 93, "endColumn": 19}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 127, "column": 31, "nodeType": "1171", "messageId": "1172", "endLine": 127, "endColumn": 34, "suggestions": "1506"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 156, "column": 71, "nodeType": "1171", "messageId": "1172", "endLine": 156, "endColumn": 74, "suggestions": "1507"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 222, "column": 19, "nodeType": "1171", "messageId": "1172", "endLine": 222, "endColumn": 22, "suggestions": "1508"}, {"ruleId": "1178", "severity": 1, "message": "1509", "line": 2, "column": 10, "nodeType": null, "messageId": "1180", "endLine": 2, "endColumn": 16}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 14, "column": 18, "nodeType": "1171", "messageId": "1172", "endLine": 14, "endColumn": 21, "suggestions": "1510"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 14, "column": 17, "nodeType": "1171", "messageId": "1172", "endLine": 14, "endColumn": 20, "suggestions": "1511"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 15, "column": 20, "nodeType": "1171", "messageId": "1172", "endLine": 15, "endColumn": 23, "suggestions": "1512"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 47, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 47, "endColumn": 25, "suggestions": "1513"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 75, "column": 16, "nodeType": "1171", "messageId": "1172", "endLine": 75, "endColumn": 19, "suggestions": "1514"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 109, "column": 16, "nodeType": "1171", "messageId": "1172", "endLine": 109, "endColumn": 19, "suggestions": "1515"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 143, "column": 15, "nodeType": "1171", "messageId": "1172", "endLine": 143, "endColumn": 18, "suggestions": "1516"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 219, "column": 29, "nodeType": "1171", "messageId": "1172", "endLine": 219, "endColumn": 32, "suggestions": "1517"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 219, "column": 52, "nodeType": "1171", "messageId": "1172", "endLine": 219, "endColumn": 55, "suggestions": "1518"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 231, "column": 29, "nodeType": "1171", "messageId": "1172", "endLine": 231, "endColumn": 32, "suggestions": "1519"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 256, "column": 29, "nodeType": "1171", "messageId": "1172", "endLine": 256, "endColumn": 32, "suggestions": "1520"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 257, "column": 38, "nodeType": "1171", "messageId": "1172", "endLine": 257, "endColumn": 41, "suggestions": "1521"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 257, "column": 71, "nodeType": "1171", "messageId": "1172", "endLine": 257, "endColumn": 74, "suggestions": "1522"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 257, "column": 107, "nodeType": "1171", "messageId": "1172", "endLine": 257, "endColumn": 110, "suggestions": "1523"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 277, "column": 32, "nodeType": "1171", "messageId": "1172", "endLine": 277, "endColumn": 35, "suggestions": "1524"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 300, "column": 22, "nodeType": "1171", "messageId": "1172", "endLine": 300, "endColumn": 25, "suggestions": "1525"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 309, "column": 46, "nodeType": "1171", "messageId": "1172", "endLine": 309, "endColumn": 49, "suggestions": "1526"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 340, "column": 66, "nodeType": "1171", "messageId": "1172", "endLine": 340, "endColumn": 69, "suggestions": "1527"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 344, "column": 90, "nodeType": "1171", "messageId": "1172", "endLine": 344, "endColumn": 93, "suggestions": "1528"}, {"ruleId": "1169", "severity": 1, "message": "1170", "line": 348, "column": 85, "nodeType": "1171", "messageId": "1172", "endLine": 348, "endColumn": 88, "suggestions": "1529"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1530", "1531"], ["1532", "1533"], ["1534", "1535"], ["1536", "1537"], ["1538", "1539"], "@typescript-eslint/no-unused-vars", "'prisma' is defined but never used.", "unusedVar", ["1540", "1541"], "'reason' is assigned a value but never used.", ["1542", "1543"], ["1544", "1545"], ["1546", "1547"], ["1548", "1549"], ["1550", "1551"], ["1552", "1553"], ["1554", "1555"], ["1556", "1557"], "'type' is assigned a value but never used.", ["1558", "1559"], ["1560", "1561"], ["1562", "1563"], ["1564", "1565"], ["1566", "1567"], ["1568", "1569"], ["1570", "1571"], "'request' is defined but never used.", ["1572", "1573"], ["1574", "1575"], ["1576", "1577"], ["1578", "1579"], ["1580", "1581"], ["1582", "1583"], "'ticketResponseDb' is defined but never used.", ["1584", "1585"], ["1586", "1587"], ["1588", "1589"], "'authenticated' is assigned a value but never used.", ["1590", "1591"], ["1592", "1593"], ["1594", "1595"], "prefer-const", "'usersWithMiningPower' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "1596", "text": "1597"}, ["1598", "1599"], "'miningUnits' is assigned a value but never used.", "'kycDocuments' is assigned a value but never used.", ["1600", "1601"], ["1602", "1603"], ["1604", "1605"], ["1606", "1607"], ["1608", "1609"], "'transactionDb' is defined but never used.", "'logDetails' is never reassigned. Use 'const' instead.", {"range": "1610", "text": "1611"}, ["1612", "1613"], ["1614", "1615"], ["1616", "1617"], ["1618", "1619"], ["1620", "1621"], ["1622", "1623"], ["1624", "1625"], ["1626", "1627"], ["1628", "1629"], ["1630", "1631"], ["1632", "1633"], ["1634", "1635"], ["1636", "1637"], ["1638", "1639"], "'error' is defined but never used.", ["1640", "1641"], ["1642", "1643"], ["1644", "1645"], ["1646", "1647"], ["1648", "1649"], ["1650", "1651"], ["1652", "1653"], ["1654", "1655"], "'responseData' is never reassigned. Use 'const' instead.", {"range": "1656", "text": "1657"}, ["1658", "1659"], ["1660", "1661"], {"range": "1662", "text": "1663"}, ["1664", "1665"], ["1666", "1667"], ["1668", "1669"], ["1670", "1671"], ["1672", "1673"], ["1674", "1675"], ["1676", "1677"], ["1678", "1679"], ["1680", "1681"], ["1682", "1683"], ["1684", "1685"], ["1686", "1687"], "'walletBalanceDb' is defined but never used.", "'verifyUSDTTransaction' is defined but never used.", "'depositRecord' is assigned a value but never used.", "'depositTransactionDb' is defined but never used.", ["1688", "1689"], ["1690", "1691"], ["1692", "1693"], ["1694", "1695"], ["1696", "1697"], ["1698", "1699"], ["1700", "1701"], ["1702", "1703"], "'Container' is defined but never used.", "'Button' is defined but never used.", "'Filter' is defined but never used.", "'X' is defined but never used.", "'matchHistory' is assigned a value but never used.", "'selectedUser' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchBinaryPointsData'. Either include it or remove the dependency array.", "ArrayExpression", ["1704"], ["1705", "1706"], "'Download' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An empty interface declaration allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowInterfaces' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "noEmptyInterface", ["1707", "1708"], "'actionLoading' is assigned a value but never used.", "'handleDepositAction' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDeposits'. Either include it or remove the dependency array.", ["1709"], "React Hook useEffect has a missing dependency: 'fetchLogs'. Either include it or remove the dependency array.", ["1710"], "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Eye' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterUsers'. Either include it or remove the dependency array.", ["1711"], ["1712", "1713"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'setSelectedUser' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchCommissions' and 'fetchStats'. Either include them or remove the dependency array.", ["1714"], ["1715", "1716"], ["1717", "1718"], ["1719", "1720"], "'Grid' is defined but never used.", "'Tag' is defined but never used.", ["1721", "1722"], "'Calendar' is defined but never used.", ["1723", "1724"], ["1725"], ["1726", "1727"], ["1728", "1729"], ["1730", "1731"], "'Settings' is defined but never used.", "'Users' is defined but never used.", "'Shield' is defined but never used.", "'formatCurrency' is defined but never used.", ["1732", "1733"], "'useCallback' is defined but never used.", "'Edit' is defined but never used.", "'Trash2' is defined but never used.", "'debounce' is defined but never used.", "React Hook useEffect has a missing dependency: 'searchTerm'. Either include it or remove the dependency array.", ["1734"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1735"], ["1736", "1737"], "'AlertTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchWithdrawals'. Either include it or remove the dependency array.", ["1738"], ["1739", "1740"], ["1741", "1742"], "'User' is defined but never used.", ["1743", "1744"], "React Hook useEffect has a missing dependency: 'fetchTreeData'. Either include it or remove the dependency array.", ["1745"], "'NodePositionInfo' is defined but never used.", "'horizontalSpacing' is defined but never used.", "'verticalSpacing' is defined but never used.", "'containerWidth' is defined but never used.", "'nodeIndex' is defined but never used.", "'isMobile' is defined but never used.", "'Award' is defined but never used.", "'Zap' is defined but never used.", "'formatDateTime' is defined but never used.", "'ZoomIn' is defined but never used.", "'ZoomOut' is defined but never used.", "'Maximize2' is defined but never used.", ["1746", "1747"], ["1748", "1749"], ["1750", "1751"], ["1752", "1753"], ["1754", "1755"], ["1756", "1757"], ["1758", "1759"], ["1760", "1761"], ["1762", "1763"], ["1764", "1765"], ["1766", "1767"], ["1768", "1769"], ["1770", "1771"], ["1772", "1773"], ["1774", "1775"], ["1776", "1777"], ["1778", "1779"], ["1780", "1781"], "React Hook useCallback has missing dependencies: 'handleNodeToggle', 'height', 'margin.bottom', 'margin.left', 'margin.right', 'margin.top', and 'width'. Either include them or remove the dependency array.", ["1782"], ["1783"], "'CreditCard' is defined but never used.", "'GridItem' is defined but never used.", "'SolarPanel' is defined but never used.", ["1784", "1785"], ["1786", "1787"], "'Input' is defined but never used.", "'Camera' is defined but never used.", "'KYCSubmission' is defined but never used.", "'uploading' is assigned a value but never used.", "'setUploading' is assigned a value but never used.", ["1788", "1789"], "'getDocumentByType' is assigned a value but never used.", "'TrendingUp' is defined but never used.", "'DollarSign' is defined but never used.", "'getDaysRemaining' is assigned a value but never used.", "'hideConfirm' is assigned a value but never used.", "'hideMessage' is assigned a value but never used.", ["1790", "1791"], ["1792", "1793"], "'useMemo' is defined but never used.", "'addEdge' is defined but never used.", "'Connection' is defined but never used.", "'Panel' is defined but never used.", "'Maximize' is defined but never used.", "'formatDate' is defined but never used.", ["1794", "1795"], "'availableWidth' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'handleNodeExpand', 'setEdges', and 'setNodes'. Either include them or remove the dependency array.", ["1796"], ["1797"], "'Bell' is defined but never used.", "'Mail' is defined but never used.", "'Smartphone' is defined but never used.", ["1798", "1799"], ["1800", "1801"], "'showWithdrawModal' is assigned a value but never used.", "'setShowWithdrawModal' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAllTransactions', 'fetchAllTransactionsSilent', and 'fetchUserWithdrawalAddress'. Either include them or remove the dependency array.", ["1802"], "React Hook useEffect has a missing dependency: 'fetchAllTransactions'. Either include it or remove the dependency array.", ["1803"], ["1804", "1805"], ["1806", "1807"], "'Leaf' is defined but never used.", "'Flex' is defined but never used.", "'Image' is defined but never used.", "'ExternalLink' is defined but never used.", "React Hook useEffect has missing dependencies: 'deposits' and 'pollingInterval'. Either include them or remove the dependency array.", ["1808"], "'isDragging' is assigned a value but never used.", "'lastPanPoint' is assigned a value but never used.", ["1809", "1810"], ["1811", "1812"], "'MiningUnit' is defined but never used.", "'Transaction' is defined but never used.", "'Referral' is defined but never used.", "'BinaryPoints' is defined but never used.", "'DepositTransaction' is defined but never used.", ["1813", "1814"], ["1815", "1816"], ["1817", "1818"], ["1819", "1820"], "'updatedBy' is defined but never used.", ["1821", "1822"], ["1823", "1824"], ["1825", "1826"], ["1827", "1828"], ["1829", "1830"], ["1831", "1832"], ["1833", "1834"], "'DepositStatus' is defined but never used.", ["1835", "1836"], ["1837", "1838"], ["1839", "1840"], ["1841", "1842"], ["1843", "1844"], ["1845", "1846"], "'bodyError' is defined but never used.", ["1847", "1848"], ["1849", "1850"], ["1851", "1852"], ["1853", "1854"], ["1855", "1856"], ["1857", "1858"], ["1859", "1860"], "'getActiveMiningUnitsFIFO' is defined but never used.", "'shouldExpireUnit' is defined but never used.", "'expireMiningUnit' is defined but never used.", ["1861", "1862"], ["1863", "1864"], ["1865", "1866"], ["1867", "1868"], "'sideToUpdate' is never reassigned. Use 'const' instead.", {"range": "1869", "text": "1870"}, "'sideToUpdate' is assigned a value but never used.", "'getActiveUplineUsers' is defined but never used.", ["1871", "1872"], ["1873", "1874"], ["1875", "1876"], ["1877", "1878"], "'searchPattern' is assigned a value but never used.", ["1879", "1880"], ["1881", "1882"], ["1883", "1884"], ["1885", "1886"], ["1887", "1888"], ["1889", "1890"], "'searchUsersInTree' is defined but never used.", "'updateCachedDownlineCounts' is defined but never used.", ["1891", "1892"], ["1893", "1894"], "'userSample' is assigned a value but never used.", "'referralSample' is assigned a value but never used.", "'binaryPointsSample' is assigned a value but never used.", "'user1' is assigned a value but never used.", "'user2' is assigned a value but never used.", "'getBinaryTreeStructure' is defined but never used.", "'user4' is assigned a value but never used.", ["1895", "1896"], ["1897", "1898"], ["1899", "1900"], "'result' is assigned a value but never used.", ["1901", "1902"], ["1903", "1904"], ["1905", "1906"], "'userDb' is defined but never used.", ["1907", "1908"], ["1909", "1910"], ["1911", "1912"], ["1913", "1914"], ["1915", "1916"], ["1917", "1918"], ["1919", "1920"], ["1921", "1922"], ["1923", "1924"], ["1925", "1926"], ["1927", "1928"], ["1929", "1930"], ["1931", "1932"], ["1933", "1934"], ["1935", "1936"], ["1937", "1938"], ["1939", "1940"], ["1941", "1942"], ["1943", "1944"], ["1945", "1946"], {"messageId": "1947", "fix": "1948", "desc": "1949"}, {"messageId": "1950", "fix": "1951", "desc": "1952"}, {"messageId": "1947", "fix": "1953", "desc": "1949"}, {"messageId": "1950", "fix": "1954", "desc": "1952"}, {"messageId": "1947", "fix": "1955", "desc": "1949"}, {"messageId": "1950", "fix": "1956", "desc": "1952"}, {"messageId": "1947", "fix": "1957", "desc": "1949"}, {"messageId": "1950", "fix": "1958", "desc": "1952"}, {"messageId": "1947", "fix": "1959", "desc": "1949"}, {"messageId": "1950", "fix": "1960", "desc": "1952"}, {"messageId": "1947", "fix": "1961", "desc": "1949"}, {"messageId": "1950", "fix": "1962", "desc": "1952"}, {"messageId": "1947", "fix": "1963", "desc": "1949"}, {"messageId": "1950", "fix": "1964", "desc": "1952"}, {"messageId": "1947", "fix": "1965", "desc": "1949"}, {"messageId": "1950", "fix": "1966", "desc": "1952"}, {"messageId": "1947", "fix": "1967", "desc": "1949"}, {"messageId": "1950", "fix": "1968", "desc": "1952"}, {"messageId": "1947", "fix": "1969", "desc": "1949"}, {"messageId": "1950", "fix": "1970", "desc": "1952"}, {"messageId": "1947", "fix": "1971", "desc": "1949"}, {"messageId": "1950", "fix": "1972", "desc": "1952"}, {"messageId": "1947", "fix": "1973", "desc": "1949"}, {"messageId": "1950", "fix": "1974", "desc": "1952"}, {"messageId": "1947", "fix": "1975", "desc": "1949"}, {"messageId": "1950", "fix": "1976", "desc": "1952"}, {"messageId": "1947", "fix": "1977", "desc": "1949"}, {"messageId": "1950", "fix": "1978", "desc": "1952"}, {"messageId": "1947", "fix": "1979", "desc": "1949"}, {"messageId": "1950", "fix": "1980", "desc": "1952"}, {"messageId": "1947", "fix": "1981", "desc": "1949"}, {"messageId": "1950", "fix": "1982", "desc": "1952"}, {"messageId": "1947", "fix": "1983", "desc": "1949"}, {"messageId": "1950", "fix": "1984", "desc": "1952"}, {"messageId": "1947", "fix": "1985", "desc": "1949"}, {"messageId": "1950", "fix": "1986", "desc": "1952"}, {"messageId": "1947", "fix": "1987", "desc": "1949"}, {"messageId": "1950", "fix": "1988", "desc": "1952"}, {"messageId": "1947", "fix": "1989", "desc": "1949"}, {"messageId": "1950", "fix": "1990", "desc": "1952"}, {"messageId": "1947", "fix": "1991", "desc": "1949"}, {"messageId": "1950", "fix": "1992", "desc": "1952"}, {"messageId": "1947", "fix": "1993", "desc": "1949"}, {"messageId": "1950", "fix": "1994", "desc": "1952"}, {"messageId": "1947", "fix": "1995", "desc": "1949"}, {"messageId": "1950", "fix": "1996", "desc": "1952"}, {"messageId": "1947", "fix": "1997", "desc": "1949"}, {"messageId": "1950", "fix": "1998", "desc": "1952"}, {"messageId": "1947", "fix": "1999", "desc": "1949"}, {"messageId": "1950", "fix": "2000", "desc": "1952"}, {"messageId": "1947", "fix": "2001", "desc": "1949"}, {"messageId": "1950", "fix": "2002", "desc": "1952"}, {"messageId": "1947", "fix": "2003", "desc": "1949"}, {"messageId": "1950", "fix": "2004", "desc": "1952"}, {"messageId": "1947", "fix": "2005", "desc": "1949"}, {"messageId": "1950", "fix": "2006", "desc": "1952"}, {"messageId": "1947", "fix": "2007", "desc": "1949"}, {"messageId": "1950", "fix": "2008", "desc": "1952"}, {"messageId": "1947", "fix": "2009", "desc": "1949"}, {"messageId": "1950", "fix": "2010", "desc": "1952"}, {"messageId": "1947", "fix": "2011", "desc": "1949"}, {"messageId": "1950", "fix": "2012", "desc": "1952"}, {"messageId": "1947", "fix": "2013", "desc": "1949"}, {"messageId": "1950", "fix": "2014", "desc": "1952"}, {"messageId": "1947", "fix": "2015", "desc": "1949"}, {"messageId": "1950", "fix": "2016", "desc": "1952"}, [3250, 4153], "const usersWithMiningPower = users.map(user => {\n      const activeTHS = user.miningUnits.reduce((total, unit) => {\n        const isActive = unit.status === 'ACTIVE' && unit.expiryDate > new Date();\n        return total + (isActive ? unit.thsAmount : 0);\n      }, 0);\n\n      // Determine display KYC status based on document submission\n      let displayKycStatus = user.kycStatus;\n\n      // If user has PENDING status but no KYC documents, show as \"Not Submitted\"\n      if (user.kycStatus === 'PENDING' && user.kycDocuments.length === 0) {\n        displayKycStatus = 'NOT_SUBMITTED' as any; // For display purposes only\n      }\n\n      // Remove miningUnits and kycDocuments from response to keep it clean\n      const { miningUnits, kycDocuments, ...userWithoutMiningUnits } = user;\n\n      return {\n        ...userWithoutMiningUnits,\n        kycStatus: displayKycStatus,\n        activeTHS,\n      };\n    });", {"messageId": "1947", "fix": "2017", "desc": "1949"}, {"messageId": "1950", "fix": "2018", "desc": "1952"}, {"messageId": "1947", "fix": "2019", "desc": "1949"}, {"messageId": "1950", "fix": "2020", "desc": "1952"}, {"messageId": "1947", "fix": "2021", "desc": "1949"}, {"messageId": "1950", "fix": "2022", "desc": "1952"}, {"messageId": "1947", "fix": "2023", "desc": "1949"}, {"messageId": "1950", "fix": "2024", "desc": "1952"}, {"messageId": "1947", "fix": "2025", "desc": "1949"}, {"messageId": "1950", "fix": "2026", "desc": "1952"}, {"messageId": "1947", "fix": "2027", "desc": "1949"}, {"messageId": "1950", "fix": "2028", "desc": "1952"}, [1354, 1510], "const logDetails: any = {\n      withdrawalId,\n      userId: withdrawal.userId,\n      amount: withdrawal.amount,\n      userEmail: withdrawal.user.email,\n    };", {"messageId": "1947", "fix": "2029", "desc": "1949"}, {"messageId": "1950", "fix": "2030", "desc": "1952"}, {"messageId": "1947", "fix": "2031", "desc": "1949"}, {"messageId": "1950", "fix": "2032", "desc": "1952"}, {"messageId": "1947", "fix": "2033", "desc": "1949"}, {"messageId": "1950", "fix": "2034", "desc": "1952"}, {"messageId": "1947", "fix": "2035", "desc": "1949"}, {"messageId": "1950", "fix": "2036", "desc": "1952"}, {"messageId": "1947", "fix": "2037", "desc": "1949"}, {"messageId": "1950", "fix": "2038", "desc": "1952"}, {"messageId": "1947", "fix": "2039", "desc": "1949"}, {"messageId": "1950", "fix": "2040", "desc": "1952"}, {"messageId": "1947", "fix": "2041", "desc": "1949"}, {"messageId": "1950", "fix": "2042", "desc": "1952"}, {"messageId": "1947", "fix": "2043", "desc": "1949"}, {"messageId": "1950", "fix": "2044", "desc": "1952"}, {"messageId": "1947", "fix": "2045", "desc": "1949"}, {"messageId": "1950", "fix": "2046", "desc": "1952"}, {"messageId": "1947", "fix": "2047", "desc": "1949"}, {"messageId": "1950", "fix": "2048", "desc": "1952"}, {"messageId": "1947", "fix": "2049", "desc": "1949"}, {"messageId": "1950", "fix": "2050", "desc": "1952"}, {"messageId": "1947", "fix": "2051", "desc": "1949"}, {"messageId": "1950", "fix": "2052", "desc": "1952"}, {"messageId": "1947", "fix": "2053", "desc": "1949"}, {"messageId": "1950", "fix": "2054", "desc": "1952"}, {"messageId": "1947", "fix": "2055", "desc": "1949"}, {"messageId": "1950", "fix": "2056", "desc": "1952"}, {"messageId": "1947", "fix": "2057", "desc": "1949"}, {"messageId": "1950", "fix": "2058", "desc": "1952"}, {"messageId": "1947", "fix": "2059", "desc": "1949"}, {"messageId": "1950", "fix": "2060", "desc": "1952"}, {"messageId": "1947", "fix": "2061", "desc": "1949"}, {"messageId": "1950", "fix": "2062", "desc": "1952"}, {"messageId": "1947", "fix": "2063", "desc": "1949"}, {"messageId": "1950", "fix": "2064", "desc": "1952"}, {"messageId": "1947", "fix": "2065", "desc": "1949"}, {"messageId": "1950", "fix": "2066", "desc": "1952"}, {"messageId": "1947", "fix": "2067", "desc": "1949"}, {"messageId": "1950", "fix": "2068", "desc": "1952"}, {"messageId": "1947", "fix": "2069", "desc": "1949"}, {"messageId": "1950", "fix": "2070", "desc": "1952"}, {"messageId": "1947", "fix": "2071", "desc": "1949"}, {"messageId": "1950", "fix": "2072", "desc": "1952"}, [740, 767], "const responseData: any = {};", {"messageId": "1947", "fix": "2073", "desc": "1949"}, {"messageId": "1950", "fix": "2074", "desc": "1952"}, {"messageId": "1947", "fix": "2075", "desc": "1949"}, {"messageId": "1950", "fix": "2076", "desc": "1952"}, [2800, 3667], "const responseData: any = {\n      treeStructure,\n      statistics: {\n        totalDirectReferrals: directReferralsFromUserTable.length,\n        leftPlacements,\n        rightPlacements,\n        leftReferrals: teamCounts.left, // Use team counts for left/right referrals display\n        rightReferrals: teamCounts.right,\n        leftTeam: teamCounts.left,\n        rightTeam: teamCounts.right,\n        totalTeam: teamCounts.total,\n        totalCommissions,\n        binaryPoints: binaryPoints || { leftPoints: 0, rightPoints: 0, matchedPoints: 0 },\n      },\n      referralLinks: {\n        left: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}&side=left`,\n        right: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}&side=right`,\n        general: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}`,\n      },\n    };", {"messageId": "1947", "fix": "2077", "desc": "1949"}, {"messageId": "1950", "fix": "2078", "desc": "1952"}, {"messageId": "1947", "fix": "2079", "desc": "1949"}, {"messageId": "1950", "fix": "2080", "desc": "1952"}, {"messageId": "1947", "fix": "2081", "desc": "1949"}, {"messageId": "1950", "fix": "2082", "desc": "1952"}, {"messageId": "1947", "fix": "2083", "desc": "1949"}, {"messageId": "1950", "fix": "2084", "desc": "1952"}, {"messageId": "1947", "fix": "2085", "desc": "1949"}, {"messageId": "1950", "fix": "2086", "desc": "1952"}, {"messageId": "1947", "fix": "2087", "desc": "1949"}, {"messageId": "1950", "fix": "2088", "desc": "1952"}, {"messageId": "1947", "fix": "2089", "desc": "1949"}, {"messageId": "1950", "fix": "2090", "desc": "1952"}, {"messageId": "1947", "fix": "2091", "desc": "1949"}, {"messageId": "1950", "fix": "2092", "desc": "1952"}, {"messageId": "1947", "fix": "2093", "desc": "1949"}, {"messageId": "1950", "fix": "2094", "desc": "1952"}, {"messageId": "1947", "fix": "2095", "desc": "1949"}, {"messageId": "1950", "fix": "2096", "desc": "1952"}, {"messageId": "1947", "fix": "2097", "desc": "1949"}, {"messageId": "1950", "fix": "2098", "desc": "1952"}, {"messageId": "1947", "fix": "2099", "desc": "1949"}, {"messageId": "1950", "fix": "2100", "desc": "1952"}, {"messageId": "1947", "fix": "2101", "desc": "1949"}, {"messageId": "1950", "fix": "2102", "desc": "1952"}, {"messageId": "1947", "fix": "2103", "desc": "1949"}, {"messageId": "1950", "fix": "2104", "desc": "1952"}, {"messageId": "1947", "fix": "2105", "desc": "1949"}, {"messageId": "1950", "fix": "2106", "desc": "1952"}, {"messageId": "1947", "fix": "2107", "desc": "1949"}, {"messageId": "1950", "fix": "2108", "desc": "1952"}, {"messageId": "1947", "fix": "2109", "desc": "1949"}, {"messageId": "1950", "fix": "2110", "desc": "1952"}, {"messageId": "1947", "fix": "2111", "desc": "1949"}, {"messageId": "1950", "fix": "2112", "desc": "1952"}, {"messageId": "1947", "fix": "2113", "desc": "1949"}, {"messageId": "1950", "fix": "2114", "desc": "1952"}, {"messageId": "1947", "fix": "2115", "desc": "1949"}, {"messageId": "1950", "fix": "2116", "desc": "1952"}, {"desc": "2117", "fix": "2118"}, {"messageId": "1947", "fix": "2119", "desc": "1949"}, {"messageId": "1950", "fix": "2120", "desc": "1952"}, {"messageId": "2121", "data": "2122", "fix": "2123", "desc": "2124"}, {"messageId": "2121", "data": "2125", "fix": "2126", "desc": "2127"}, {"desc": "2128", "fix": "2129"}, {"desc": "2130", "fix": "2131"}, {"desc": "2132", "fix": "2133"}, {"messageId": "1947", "fix": "2134", "desc": "1949"}, {"messageId": "1950", "fix": "2135", "desc": "1952"}, {"desc": "2136", "fix": "2137"}, {"messageId": "1947", "fix": "2138", "desc": "1949"}, {"messageId": "1950", "fix": "2139", "desc": "1952"}, {"messageId": "1947", "fix": "2140", "desc": "1949"}, {"messageId": "1950", "fix": "2141", "desc": "1952"}, {"messageId": "1947", "fix": "2142", "desc": "1949"}, {"messageId": "1950", "fix": "2143", "desc": "1952"}, {"messageId": "1947", "fix": "2144", "desc": "1949"}, {"messageId": "1950", "fix": "2145", "desc": "1952"}, {"messageId": "1947", "fix": "2146", "desc": "1949"}, {"messageId": "1950", "fix": "2147", "desc": "1952"}, {"desc": "2148", "fix": "2149"}, {"messageId": "1947", "fix": "2150", "desc": "1949"}, {"messageId": "1950", "fix": "2151", "desc": "1952"}, {"messageId": "1947", "fix": "2152", "desc": "1949"}, {"messageId": "1950", "fix": "2153", "desc": "1952"}, {"messageId": "1947", "fix": "2154", "desc": "1949"}, {"messageId": "1950", "fix": "2155", "desc": "1952"}, {"messageId": "1947", "fix": "2156", "desc": "1949"}, {"messageId": "1950", "fix": "2157", "desc": "1952"}, {"desc": "2158", "fix": "2159"}, {"desc": "2160", "fix": "2161"}, {"messageId": "1947", "fix": "2162", "desc": "1949"}, {"messageId": "1950", "fix": "2163", "desc": "1952"}, {"desc": "2164", "fix": "2165"}, {"messageId": "1947", "fix": "2166", "desc": "1949"}, {"messageId": "1950", "fix": "2167", "desc": "1952"}, {"messageId": "1947", "fix": "2168", "desc": "1949"}, {"messageId": "1950", "fix": "2169", "desc": "1952"}, {"messageId": "1947", "fix": "2170", "desc": "1949"}, {"messageId": "1950", "fix": "2171", "desc": "1952"}, {"desc": "2172", "fix": "2173"}, {"messageId": "1947", "fix": "2174", "desc": "1949"}, {"messageId": "1950", "fix": "2175", "desc": "1952"}, {"messageId": "1947", "fix": "2176", "desc": "1949"}, {"messageId": "1950", "fix": "2177", "desc": "1952"}, {"messageId": "1947", "fix": "2178", "desc": "1949"}, {"messageId": "1950", "fix": "2179", "desc": "1952"}, {"messageId": "1947", "fix": "2180", "desc": "1949"}, {"messageId": "1950", "fix": "2181", "desc": "1952"}, {"messageId": "1947", "fix": "2182", "desc": "1949"}, {"messageId": "1950", "fix": "2183", "desc": "1952"}, {"messageId": "1947", "fix": "2184", "desc": "1949"}, {"messageId": "1950", "fix": "2185", "desc": "1952"}, {"messageId": "1947", "fix": "2186", "desc": "1949"}, {"messageId": "1950", "fix": "2187", "desc": "1952"}, {"messageId": "1947", "fix": "2188", "desc": "1949"}, {"messageId": "1950", "fix": "2189", "desc": "1952"}, {"messageId": "1947", "fix": "2190", "desc": "1949"}, {"messageId": "1950", "fix": "2191", "desc": "1952"}, {"messageId": "1947", "fix": "2192", "desc": "1949"}, {"messageId": "1950", "fix": "2193", "desc": "1952"}, {"messageId": "1947", "fix": "2194", "desc": "1949"}, {"messageId": "1950", "fix": "2195", "desc": "1952"}, {"messageId": "1947", "fix": "2196", "desc": "1949"}, {"messageId": "1950", "fix": "2197", "desc": "1952"}, {"messageId": "1947", "fix": "2198", "desc": "1949"}, {"messageId": "1950", "fix": "2199", "desc": "1952"}, {"messageId": "1947", "fix": "2200", "desc": "1949"}, {"messageId": "1950", "fix": "2201", "desc": "1952"}, {"messageId": "1947", "fix": "2202", "desc": "1949"}, {"messageId": "1950", "fix": "2203", "desc": "1952"}, {"messageId": "1947", "fix": "2204", "desc": "1949"}, {"messageId": "1950", "fix": "2205", "desc": "1952"}, {"messageId": "1947", "fix": "2206", "desc": "1949"}, {"messageId": "1950", "fix": "2207", "desc": "1952"}, {"messageId": "1947", "fix": "2208", "desc": "1949"}, {"messageId": "1950", "fix": "2209", "desc": "1952"}, {"desc": "2210", "fix": "2211"}, {"desc": "2212", "fix": "2213"}, {"messageId": "1947", "fix": "2214", "desc": "1949"}, {"messageId": "1950", "fix": "2215", "desc": "1952"}, {"messageId": "1947", "fix": "2216", "desc": "1949"}, {"messageId": "1950", "fix": "2217", "desc": "1952"}, {"messageId": "1947", "fix": "2218", "desc": "1949"}, {"messageId": "1950", "fix": "2219", "desc": "1952"}, {"messageId": "1947", "fix": "2220", "desc": "1949"}, {"messageId": "1950", "fix": "2221", "desc": "1952"}, {"messageId": "1947", "fix": "2222", "desc": "1949"}, {"messageId": "1950", "fix": "2223", "desc": "1952"}, {"messageId": "1947", "fix": "2224", "desc": "1949"}, {"messageId": "1950", "fix": "2225", "desc": "1952"}, {"desc": "2226", "fix": "2227"}, {"desc": "2212", "fix": "2228"}, {"messageId": "1947", "fix": "2229", "desc": "1949"}, {"messageId": "1950", "fix": "2230", "desc": "1952"}, {"messageId": "1947", "fix": "2231", "desc": "1949"}, {"messageId": "1950", "fix": "2232", "desc": "1952"}, {"desc": "2233", "fix": "2234"}, {"desc": "2235", "fix": "2236"}, {"messageId": "1947", "fix": "2237", "desc": "1949"}, {"messageId": "1950", "fix": "2238", "desc": "1952"}, {"messageId": "1947", "fix": "2239", "desc": "1949"}, {"messageId": "1950", "fix": "2240", "desc": "1952"}, {"desc": "2241", "fix": "2242"}, {"messageId": "1947", "fix": "2243", "desc": "1949"}, {"messageId": "1950", "fix": "2244", "desc": "1952"}, {"messageId": "1947", "fix": "2245", "desc": "1949"}, {"messageId": "1950", "fix": "2246", "desc": "1952"}, {"messageId": "1947", "fix": "2247", "desc": "1949"}, {"messageId": "1950", "fix": "2248", "desc": "1952"}, {"messageId": "1947", "fix": "2249", "desc": "1949"}, {"messageId": "1950", "fix": "2250", "desc": "1952"}, {"messageId": "1947", "fix": "2251", "desc": "1949"}, {"messageId": "1950", "fix": "2252", "desc": "1952"}, {"messageId": "1947", "fix": "2253", "desc": "1949"}, {"messageId": "1950", "fix": "2254", "desc": "1952"}, {"messageId": "1947", "fix": "2255", "desc": "1949"}, {"messageId": "1950", "fix": "2256", "desc": "1952"}, {"messageId": "1947", "fix": "2257", "desc": "1949"}, {"messageId": "1950", "fix": "2258", "desc": "1952"}, {"messageId": "1947", "fix": "2259", "desc": "1949"}, {"messageId": "1950", "fix": "2260", "desc": "1952"}, {"messageId": "1947", "fix": "2261", "desc": "1949"}, {"messageId": "1950", "fix": "2262", "desc": "1952"}, {"messageId": "1947", "fix": "2263", "desc": "1949"}, {"messageId": "1950", "fix": "2264", "desc": "1952"}, {"messageId": "1947", "fix": "2265", "desc": "1949"}, {"messageId": "1950", "fix": "2266", "desc": "1952"}, {"messageId": "1947", "fix": "2267", "desc": "1949"}, {"messageId": "1950", "fix": "2268", "desc": "1952"}, {"messageId": "1947", "fix": "2269", "desc": "1949"}, {"messageId": "1950", "fix": "2270", "desc": "1952"}, {"messageId": "1947", "fix": "2271", "desc": "1949"}, {"messageId": "1950", "fix": "2272", "desc": "1952"}, {"messageId": "1947", "fix": "2273", "desc": "1949"}, {"messageId": "1950", "fix": "2274", "desc": "1952"}, {"messageId": "1947", "fix": "2275", "desc": "1949"}, {"messageId": "1950", "fix": "2276", "desc": "1952"}, {"messageId": "1947", "fix": "2277", "desc": "1949"}, {"messageId": "1950", "fix": "2278", "desc": "1952"}, {"messageId": "1947", "fix": "2279", "desc": "1949"}, {"messageId": "1950", "fix": "2280", "desc": "1952"}, {"messageId": "1947", "fix": "2281", "desc": "1949"}, {"messageId": "1950", "fix": "2282", "desc": "1952"}, {"messageId": "1947", "fix": "2283", "desc": "1949"}, {"messageId": "1950", "fix": "2284", "desc": "1952"}, {"messageId": "1947", "fix": "2285", "desc": "1949"}, {"messageId": "1950", "fix": "2286", "desc": "1952"}, {"messageId": "1947", "fix": "2287", "desc": "1949"}, {"messageId": "1950", "fix": "2288", "desc": "1952"}, {"messageId": "1947", "fix": "2289", "desc": "1949"}, {"messageId": "1950", "fix": "2290", "desc": "1952"}, {"messageId": "1947", "fix": "2291", "desc": "1949"}, {"messageId": "1950", "fix": "2292", "desc": "1952"}, {"messageId": "1947", "fix": "2293", "desc": "1949"}, {"messageId": "1950", "fix": "2294", "desc": "1952"}, {"messageId": "1947", "fix": "2295", "desc": "1949"}, {"messageId": "1950", "fix": "2296", "desc": "1952"}, {"messageId": "1947", "fix": "2297", "desc": "1949"}, {"messageId": "1950", "fix": "2298", "desc": "1952"}, {"messageId": "1947", "fix": "2299", "desc": "1949"}, {"messageId": "1950", "fix": "2300", "desc": "1952"}, {"messageId": "1947", "fix": "2301", "desc": "1949"}, {"messageId": "1950", "fix": "2302", "desc": "1952"}, [24668, 24719], "const sideToUpdate: 'LEFT' | 'RIGHT' = placementSide;", {"messageId": "1947", "fix": "2303", "desc": "1949"}, {"messageId": "1950", "fix": "2304", "desc": "1952"}, {"messageId": "1947", "fix": "2305", "desc": "1949"}, {"messageId": "1950", "fix": "2306", "desc": "1952"}, {"messageId": "1947", "fix": "2307", "desc": "1949"}, {"messageId": "1950", "fix": "2308", "desc": "1952"}, {"messageId": "1947", "fix": "2309", "desc": "1949"}, {"messageId": "1950", "fix": "2310", "desc": "1952"}, {"messageId": "1947", "fix": "2311", "desc": "1949"}, {"messageId": "1950", "fix": "2312", "desc": "1952"}, {"messageId": "1947", "fix": "2313", "desc": "1949"}, {"messageId": "1950", "fix": "2314", "desc": "1952"}, {"messageId": "1947", "fix": "2315", "desc": "1949"}, {"messageId": "1950", "fix": "2316", "desc": "1952"}, {"messageId": "1947", "fix": "2317", "desc": "1949"}, {"messageId": "1950", "fix": "2318", "desc": "1952"}, {"messageId": "1947", "fix": "2319", "desc": "1949"}, {"messageId": "1950", "fix": "2320", "desc": "1952"}, {"messageId": "1947", "fix": "2321", "desc": "1949"}, {"messageId": "1950", "fix": "2322", "desc": "1952"}, {"messageId": "1947", "fix": "2323", "desc": "1949"}, {"messageId": "1950", "fix": "2324", "desc": "1952"}, {"messageId": "1947", "fix": "2325", "desc": "1949"}, {"messageId": "1950", "fix": "2326", "desc": "1952"}, {"messageId": "1947", "fix": "2327", "desc": "1949"}, {"messageId": "1950", "fix": "2328", "desc": "1952"}, {"messageId": "1947", "fix": "2329", "desc": "1949"}, {"messageId": "1950", "fix": "2330", "desc": "1952"}, {"messageId": "1947", "fix": "2331", "desc": "1949"}, {"messageId": "1950", "fix": "2332", "desc": "1952"}, {"messageId": "1947", "fix": "2333", "desc": "1949"}, {"messageId": "1950", "fix": "2334", "desc": "1952"}, {"messageId": "1947", "fix": "2335", "desc": "1949"}, {"messageId": "1950", "fix": "2336", "desc": "1952"}, {"messageId": "1947", "fix": "2337", "desc": "1949"}, {"messageId": "1950", "fix": "2338", "desc": "1952"}, {"messageId": "1947", "fix": "2339", "desc": "1949"}, {"messageId": "1950", "fix": "2340", "desc": "1952"}, {"messageId": "1947", "fix": "2341", "desc": "1949"}, {"messageId": "1950", "fix": "2342", "desc": "1952"}, {"messageId": "1947", "fix": "2343", "desc": "1949"}, {"messageId": "1950", "fix": "2344", "desc": "1952"}, {"messageId": "1947", "fix": "2345", "desc": "1949"}, {"messageId": "1950", "fix": "2346", "desc": "1952"}, {"messageId": "1947", "fix": "2347", "desc": "1949"}, {"messageId": "1950", "fix": "2348", "desc": "1952"}, {"messageId": "1947", "fix": "2349", "desc": "1949"}, {"messageId": "1950", "fix": "2350", "desc": "1952"}, {"messageId": "1947", "fix": "2351", "desc": "1949"}, {"messageId": "1950", "fix": "2352", "desc": "1952"}, {"messageId": "1947", "fix": "2353", "desc": "1949"}, {"messageId": "1950", "fix": "2354", "desc": "1952"}, {"messageId": "1947", "fix": "2355", "desc": "1949"}, {"messageId": "1950", "fix": "2356", "desc": "1952"}, {"messageId": "1947", "fix": "2357", "desc": "1949"}, {"messageId": "1950", "fix": "2358", "desc": "1952"}, {"messageId": "1947", "fix": "2359", "desc": "1949"}, {"messageId": "1950", "fix": "2360", "desc": "1952"}, {"messageId": "1947", "fix": "2361", "desc": "1949"}, {"messageId": "1950", "fix": "2362", "desc": "1952"}, {"messageId": "1947", "fix": "2363", "desc": "1949"}, {"messageId": "1950", "fix": "2364", "desc": "1952"}, {"messageId": "1947", "fix": "2365", "desc": "1949"}, {"messageId": "1950", "fix": "2366", "desc": "1952"}, {"messageId": "1947", "fix": "2367", "desc": "1949"}, {"messageId": "1950", "fix": "2368", "desc": "1952"}, {"messageId": "1947", "fix": "2369", "desc": "1949"}, {"messageId": "1950", "fix": "2370", "desc": "1952"}, {"messageId": "1947", "fix": "2371", "desc": "1949"}, {"messageId": "1950", "fix": "2372", "desc": "1952"}, {"messageId": "1947", "fix": "2373", "desc": "1949"}, {"messageId": "1950", "fix": "2374", "desc": "1952"}, {"messageId": "1947", "fix": "2375", "desc": "1949"}, {"messageId": "1950", "fix": "2376", "desc": "1952"}, {"messageId": "1947", "fix": "2377", "desc": "1949"}, {"messageId": "1950", "fix": "2378", "desc": "1952"}, "suggestUnknown", {"range": "2379", "text": "2380"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2381", "text": "2382"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2383", "text": "2380"}, {"range": "2384", "text": "2382"}, {"range": "2385", "text": "2380"}, {"range": "2386", "text": "2382"}, {"range": "2387", "text": "2380"}, {"range": "2388", "text": "2382"}, {"range": "2389", "text": "2380"}, {"range": "2390", "text": "2382"}, {"range": "2391", "text": "2380"}, {"range": "2392", "text": "2382"}, {"range": "2393", "text": "2380"}, {"range": "2394", "text": "2382"}, {"range": "2395", "text": "2380"}, {"range": "2396", "text": "2382"}, {"range": "2397", "text": "2380"}, {"range": "2398", "text": "2382"}, {"range": "2399", "text": "2380"}, {"range": "2400", "text": "2382"}, {"range": "2401", "text": "2380"}, {"range": "2402", "text": "2382"}, {"range": "2403", "text": "2380"}, {"range": "2404", "text": "2382"}, {"range": "2405", "text": "2380"}, {"range": "2406", "text": "2382"}, {"range": "2407", "text": "2380"}, {"range": "2408", "text": "2382"}, {"range": "2409", "text": "2380"}, {"range": "2410", "text": "2382"}, {"range": "2411", "text": "2380"}, {"range": "2412", "text": "2382"}, {"range": "2413", "text": "2380"}, {"range": "2414", "text": "2382"}, {"range": "2415", "text": "2380"}, {"range": "2416", "text": "2382"}, {"range": "2417", "text": "2380"}, {"range": "2418", "text": "2382"}, {"range": "2419", "text": "2380"}, {"range": "2420", "text": "2382"}, {"range": "2421", "text": "2380"}, {"range": "2422", "text": "2382"}, {"range": "2423", "text": "2380"}, {"range": "2424", "text": "2382"}, {"range": "2425", "text": "2380"}, {"range": "2426", "text": "2382"}, {"range": "2427", "text": "2380"}, {"range": "2428", "text": "2382"}, {"range": "2429", "text": "2380"}, {"range": "2430", "text": "2382"}, {"range": "2431", "text": "2380"}, {"range": "2432", "text": "2382"}, {"range": "2433", "text": "2380"}, {"range": "2434", "text": "2382"}, {"range": "2435", "text": "2380"}, {"range": "2436", "text": "2382"}, {"range": "2437", "text": "2380"}, {"range": "2438", "text": "2382"}, {"range": "2439", "text": "2380"}, {"range": "2440", "text": "2382"}, {"range": "2441", "text": "2380"}, {"range": "2442", "text": "2382"}, {"range": "2443", "text": "2380"}, {"range": "2444", "text": "2382"}, {"range": "2445", "text": "2380"}, {"range": "2446", "text": "2382"}, {"range": "2447", "text": "2380"}, {"range": "2448", "text": "2382"}, {"range": "2449", "text": "2380"}, {"range": "2450", "text": "2382"}, {"range": "2451", "text": "2380"}, {"range": "2452", "text": "2382"}, {"range": "2453", "text": "2380"}, {"range": "2454", "text": "2382"}, {"range": "2455", "text": "2380"}, {"range": "2456", "text": "2382"}, {"range": "2457", "text": "2380"}, {"range": "2458", "text": "2382"}, {"range": "2459", "text": "2380"}, {"range": "2460", "text": "2382"}, {"range": "2461", "text": "2380"}, {"range": "2462", "text": "2382"}, {"range": "2463", "text": "2380"}, {"range": "2464", "text": "2382"}, {"range": "2465", "text": "2380"}, {"range": "2466", "text": "2382"}, {"range": "2467", "text": "2380"}, {"range": "2468", "text": "2382"}, {"range": "2469", "text": "2380"}, {"range": "2470", "text": "2382"}, {"range": "2471", "text": "2380"}, {"range": "2472", "text": "2382"}, {"range": "2473", "text": "2380"}, {"range": "2474", "text": "2382"}, {"range": "2475", "text": "2380"}, {"range": "2476", "text": "2382"}, {"range": "2477", "text": "2380"}, {"range": "2478", "text": "2382"}, {"range": "2479", "text": "2380"}, {"range": "2480", "text": "2382"}, {"range": "2481", "text": "2380"}, {"range": "2482", "text": "2382"}, {"range": "2483", "text": "2380"}, {"range": "2484", "text": "2382"}, {"range": "2485", "text": "2380"}, {"range": "2486", "text": "2382"}, {"range": "2487", "text": "2380"}, {"range": "2488", "text": "2382"}, {"range": "2489", "text": "2380"}, {"range": "2490", "text": "2382"}, {"range": "2491", "text": "2380"}, {"range": "2492", "text": "2382"}, {"range": "2493", "text": "2380"}, {"range": "2494", "text": "2382"}, {"range": "2495", "text": "2380"}, {"range": "2496", "text": "2382"}, {"range": "2497", "text": "2380"}, {"range": "2498", "text": "2382"}, {"range": "2499", "text": "2380"}, {"range": "2500", "text": "2382"}, {"range": "2501", "text": "2380"}, {"range": "2502", "text": "2382"}, {"range": "2503", "text": "2380"}, {"range": "2504", "text": "2382"}, {"range": "2505", "text": "2380"}, {"range": "2506", "text": "2382"}, {"range": "2507", "text": "2380"}, {"range": "2508", "text": "2382"}, {"range": "2509", "text": "2380"}, {"range": "2510", "text": "2382"}, {"range": "2511", "text": "2380"}, {"range": "2512", "text": "2382"}, {"range": "2513", "text": "2380"}, {"range": "2514", "text": "2382"}, {"range": "2515", "text": "2380"}, {"range": "2516", "text": "2382"}, {"range": "2517", "text": "2380"}, {"range": "2518", "text": "2382"}, {"range": "2519", "text": "2380"}, {"range": "2520", "text": "2382"}, {"range": "2521", "text": "2380"}, {"range": "2522", "text": "2382"}, {"range": "2523", "text": "2380"}, {"range": "2524", "text": "2382"}, {"range": "2525", "text": "2380"}, {"range": "2526", "text": "2382"}, {"range": "2527", "text": "2380"}, {"range": "2528", "text": "2382"}, {"range": "2529", "text": "2380"}, {"range": "2530", "text": "2382"}, {"range": "2531", "text": "2380"}, {"range": "2532", "text": "2382"}, {"range": "2533", "text": "2380"}, {"range": "2534", "text": "2382"}, {"range": "2535", "text": "2380"}, {"range": "2536", "text": "2382"}, {"range": "2537", "text": "2380"}, {"range": "2538", "text": "2382"}, {"range": "2539", "text": "2380"}, {"range": "2540", "text": "2382"}, {"range": "2541", "text": "2380"}, {"range": "2542", "text": "2382"}, {"range": "2543", "text": "2380"}, {"range": "2544", "text": "2382"}, {"range": "2545", "text": "2380"}, {"range": "2546", "text": "2382"}, "Update the dependencies array to be: [fetchBinaryPointsData]", {"range": "2547", "text": "2548"}, {"range": "2549", "text": "2380"}, {"range": "2550", "text": "2382"}, "replaceEmptyInterface", {"replacement": "2551"}, {"range": "2552", "text": "2553"}, "Replace empty interface with `object`.", {"replacement": "2380"}, {"range": "2554", "text": "2555"}, "Replace empty interface with `unknown`.", "Update the dependencies array to be: [fetchDeposits, selectedStatus]", {"range": "2556", "text": "2557"}, "Update the dependencies array to be: [currentPage, fetchLogs, filters]", {"range": "2558", "text": "2559"}, "Update the dependencies array to be: [users, searchTerm, statusFilter, filterUsers]", {"range": "2560", "text": "2561"}, {"range": "2562", "text": "2380"}, {"range": "2563", "text": "2382"}, "Update the dependencies array to be: [dateRange, filterType, filterStatus, currentPage, fetchCommissions, fetchStats]", {"range": "2564", "text": "2565"}, {"range": "2566", "text": "2380"}, {"range": "2567", "text": "2382"}, {"range": "2568", "text": "2380"}, {"range": "2569", "text": "2382"}, {"range": "2570", "text": "2380"}, {"range": "2571", "text": "2382"}, {"range": "2572", "text": "2380"}, {"range": "2573", "text": "2382"}, {"range": "2574", "text": "2380"}, {"range": "2575", "text": "2382"}, "Update the dependencies array to be: [currentPage, searchTerm, filterAction, dateRange, fetchLogs]", {"range": "2576", "text": "2577"}, {"range": "2578", "text": "2380"}, {"range": "2579", "text": "2382"}, {"range": "2580", "text": "2380"}, {"range": "2581", "text": "2382"}, {"range": "2582", "text": "2380"}, {"range": "2583", "text": "2382"}, {"range": "2584", "text": "2380"}, {"range": "2585", "text": "2382"}, "Update the dependencies array to be: [debouncedSearchTerm, searchTerm]", {"range": "2586", "text": "2587"}, "Update the dependencies array to be: [currentPage, debouncedSearchTerm, fetchUsers, filterStatus, sortBy, sortOrder]", {"range": "2588", "text": "2589"}, {"range": "2590", "text": "2380"}, {"range": "2591", "text": "2382"}, "Update the dependencies array to be: [searchTerm, filterStatus, fetchWithdrawals]", {"range": "2592", "text": "2593"}, {"range": "2594", "text": "2380"}, {"range": "2595", "text": "2382"}, {"range": "2596", "text": "2380"}, {"range": "2597", "text": "2382"}, {"range": "2598", "text": "2380"}, {"range": "2599", "text": "2382"}, "Update the dependencies array to be: [fetchTreeData]", {"range": "2600", "text": "2601"}, {"range": "2602", "text": "2380"}, {"range": "2603", "text": "2382"}, {"range": "2604", "text": "2380"}, {"range": "2605", "text": "2382"}, {"range": "2606", "text": "2380"}, {"range": "2607", "text": "2382"}, {"range": "2608", "text": "2380"}, {"range": "2609", "text": "2382"}, {"range": "2610", "text": "2380"}, {"range": "2611", "text": "2382"}, {"range": "2612", "text": "2380"}, {"range": "2613", "text": "2382"}, {"range": "2614", "text": "2380"}, {"range": "2615", "text": "2382"}, {"range": "2616", "text": "2380"}, {"range": "2617", "text": "2382"}, {"range": "2618", "text": "2380"}, {"range": "2619", "text": "2382"}, {"range": "2620", "text": "2380"}, {"range": "2621", "text": "2382"}, {"range": "2622", "text": "2380"}, {"range": "2623", "text": "2382"}, {"range": "2624", "text": "2380"}, {"range": "2625", "text": "2382"}, {"range": "2626", "text": "2380"}, {"range": "2627", "text": "2382"}, {"range": "2628", "text": "2380"}, {"range": "2629", "text": "2382"}, {"range": "2630", "text": "2380"}, {"range": "2631", "text": "2382"}, {"range": "2632", "text": "2380"}, {"range": "2633", "text": "2382"}, {"range": "2634", "text": "2380"}, {"range": "2635", "text": "2382"}, {"range": "2636", "text": "2380"}, {"range": "2637", "text": "2382"}, "Update the dependencies array to be: [treeData, width, margin.left, margin.right, margin.top, margin.bottom, height, convertToD3Hierarchy, expandedNodes, handleNodeToggle]", {"range": "2638", "text": "2639"}, "Update the dependencies array to be: [expandedNodes, fetchTreeData]", {"range": "2640", "text": "2641"}, {"range": "2642", "text": "2380"}, {"range": "2643", "text": "2382"}, {"range": "2644", "text": "2380"}, {"range": "2645", "text": "2382"}, {"range": "2646", "text": "2380"}, {"range": "2647", "text": "2382"}, {"range": "2648", "text": "2380"}, {"range": "2649", "text": "2382"}, {"range": "2650", "text": "2380"}, {"range": "2651", "text": "2382"}, {"range": "2652", "text": "2380"}, {"range": "2653", "text": "2382"}, "Update the dependencies array to be: [calculateSubtreeWidth, setNodes, setEdges, showInactive, expandedNodes, handleNodeExpand]", {"range": "2654", "text": "2655"}, {"range": "2656", "text": "2641"}, {"range": "2657", "text": "2380"}, {"range": "2658", "text": "2382"}, {"range": "2659", "text": "2380"}, {"range": "2660", "text": "2382"}, "Update the dependencies array to be: [fetchAllTransactions, fetchAllTransactionsSilent, fetchUserWithdrawalAddress]", {"range": "2661", "text": "2662"}, "Update the dependencies array to be: [searchTerm, filterType, filterStatus, fetchAllTransactions]", {"range": "2663", "text": "2664"}, {"range": "2665", "text": "2380"}, {"range": "2666", "text": "2382"}, {"range": "2667", "text": "2380"}, {"range": "2668", "text": "2382"}, "Update the dependencies array to be: [deposits, deposits.length, pollingInterval]", {"range": "2669", "text": "2670"}, {"range": "2671", "text": "2380"}, {"range": "2672", "text": "2382"}, {"range": "2673", "text": "2380"}, {"range": "2674", "text": "2382"}, {"range": "2675", "text": "2380"}, {"range": "2676", "text": "2382"}, {"range": "2677", "text": "2380"}, {"range": "2678", "text": "2382"}, {"range": "2679", "text": "2380"}, {"range": "2680", "text": "2382"}, {"range": "2681", "text": "2380"}, {"range": "2682", "text": "2382"}, {"range": "2683", "text": "2380"}, {"range": "2684", "text": "2382"}, {"range": "2685", "text": "2380"}, {"range": "2686", "text": "2382"}, {"range": "2687", "text": "2380"}, {"range": "2688", "text": "2382"}, {"range": "2689", "text": "2380"}, {"range": "2690", "text": "2382"}, {"range": "2691", "text": "2380"}, {"range": "2692", "text": "2382"}, {"range": "2693", "text": "2380"}, {"range": "2694", "text": "2382"}, {"range": "2695", "text": "2380"}, {"range": "2696", "text": "2382"}, {"range": "2697", "text": "2380"}, {"range": "2698", "text": "2382"}, {"range": "2699", "text": "2380"}, {"range": "2700", "text": "2382"}, {"range": "2701", "text": "2380"}, {"range": "2702", "text": "2382"}, {"range": "2703", "text": "2380"}, {"range": "2704", "text": "2382"}, {"range": "2705", "text": "2380"}, {"range": "2706", "text": "2382"}, {"range": "2707", "text": "2380"}, {"range": "2708", "text": "2382"}, {"range": "2709", "text": "2380"}, {"range": "2710", "text": "2382"}, {"range": "2711", "text": "2380"}, {"range": "2712", "text": "2382"}, {"range": "2713", "text": "2380"}, {"range": "2714", "text": "2382"}, {"range": "2715", "text": "2380"}, {"range": "2716", "text": "2382"}, {"range": "2717", "text": "2380"}, {"range": "2718", "text": "2382"}, {"range": "2719", "text": "2380"}, {"range": "2720", "text": "2382"}, {"range": "2721", "text": "2380"}, {"range": "2722", "text": "2382"}, {"range": "2723", "text": "2380"}, {"range": "2724", "text": "2382"}, {"range": "2725", "text": "2380"}, {"range": "2726", "text": "2382"}, {"range": "2727", "text": "2380"}, {"range": "2728", "text": "2382"}, {"range": "2729", "text": "2380"}, {"range": "2730", "text": "2382"}, {"range": "2731", "text": "2380"}, {"range": "2732", "text": "2382"}, {"range": "2733", "text": "2380"}, {"range": "2734", "text": "2382"}, {"range": "2735", "text": "2380"}, {"range": "2736", "text": "2382"}, {"range": "2737", "text": "2380"}, {"range": "2738", "text": "2382"}, {"range": "2739", "text": "2380"}, {"range": "2740", "text": "2382"}, {"range": "2741", "text": "2380"}, {"range": "2742", "text": "2382"}, {"range": "2743", "text": "2380"}, {"range": "2744", "text": "2382"}, {"range": "2745", "text": "2380"}, {"range": "2746", "text": "2382"}, {"range": "2747", "text": "2380"}, {"range": "2748", "text": "2382"}, {"range": "2749", "text": "2380"}, {"range": "2750", "text": "2382"}, {"range": "2751", "text": "2380"}, {"range": "2752", "text": "2382"}, {"range": "2753", "text": "2380"}, {"range": "2754", "text": "2382"}, {"range": "2755", "text": "2380"}, {"range": "2756", "text": "2382"}, {"range": "2757", "text": "2380"}, {"range": "2758", "text": "2382"}, {"range": "2759", "text": "2380"}, {"range": "2760", "text": "2382"}, {"range": "2761", "text": "2380"}, {"range": "2762", "text": "2382"}, {"range": "2763", "text": "2380"}, {"range": "2764", "text": "2382"}, {"range": "2765", "text": "2380"}, {"range": "2766", "text": "2382"}, {"range": "2767", "text": "2380"}, {"range": "2768", "text": "2382"}, {"range": "2769", "text": "2380"}, {"range": "2770", "text": "2382"}, {"range": "2771", "text": "2380"}, {"range": "2772", "text": "2382"}, {"range": "2773", "text": "2380"}, {"range": "2774", "text": "2382"}, {"range": "2775", "text": "2380"}, {"range": "2776", "text": "2382"}, {"range": "2777", "text": "2380"}, {"range": "2778", "text": "2382"}, {"range": "2779", "text": "2380"}, {"range": "2780", "text": "2382"}, {"range": "2781", "text": "2380"}, {"range": "2782", "text": "2382"}, {"range": "2783", "text": "2380"}, {"range": "2784", "text": "2382"}, {"range": "2785", "text": "2380"}, {"range": "2786", "text": "2382"}, {"range": "2787", "text": "2380"}, {"range": "2788", "text": "2382"}, {"range": "2789", "text": "2380"}, {"range": "2790", "text": "2382"}, {"range": "2791", "text": "2380"}, {"range": "2792", "text": "2382"}, {"range": "2793", "text": "2380"}, {"range": "2794", "text": "2382"}, {"range": "2795", "text": "2380"}, {"range": "2796", "text": "2382"}, {"range": "2797", "text": "2380"}, {"range": "2798", "text": "2382"}, {"range": "2799", "text": "2380"}, {"range": "2800", "text": "2382"}, {"range": "2801", "text": "2380"}, {"range": "2802", "text": "2382"}, {"range": "2803", "text": "2380"}, {"range": "2804", "text": "2382"}, {"range": "2805", "text": "2380"}, {"range": "2806", "text": "2382"}, [1602, 1605], "unknown", [1602, 1605], "never", [970, 973], [970, 973], [3464, 3467], [3464, 3467], [1252, 1255], [1252, 1255], [4091, 4094], [4091, 4094], [686, 689], [686, 689], [2329, 2332], [2329, 2332], [3996, 3999], [3996, 3999], [1906, 1909], [1906, 1909], [2967, 2970], [2967, 2970], [1218, 1221], [1218, 1221], [3600, 3603], [3600, 3603], [942, 945], [942, 945], [1103, 1106], [1103, 1106], [1128, 1131], [1128, 1131], [1396, 1399], [1396, 1399], [5957, 5960], [5957, 5960], [864, 867], [864, 867], [1132, 1135], [1132, 1135], [3069, 3072], [3069, 3072], [908, 911], [908, 911], [408, 411], [408, 411], [1675, 1678], [1675, 1678], [742, 745], [742, 745], [3346, 3349], [3346, 3349], [1400, 1403], [1400, 1403], [2055, 2058], [2055, 2058], [1114, 1117], [1114, 1117], [2784, 2787], [2784, 2787], [1613, 1616], [1613, 1616], [1138, 1141], [1138, 1141], [1201, 1204], [1201, 1204], [1596, 1599], [1596, 1599], [3834, 3837], [3834, 3837], [5331, 5334], [5331, 5334], [6497, 6500], [6497, 6500], [6537, 6540], [6537, 6540], [6584, 6587], [6584, 6587], [6634, 6637], [6634, 6637], [1370, 1373], [1370, 1373], [782, 785], [782, 785], [1608, 1611], [1608, 1611], [1061, 1064], [1061, 1064], [785, 788], [785, 788], [2407, 2410], [2407, 2410], [4728, 4731], [4728, 4731], [1602, 1605], [1602, 1605], [1827, 1830], [1827, 1830], [1586, 1589], [1586, 1589], [2833, 2836], [2833, 2836], [949, 952], [949, 952], [3165, 3168], [3165, 3168], [3743, 3746], [3743, 3746], [3150, 3153], [3150, 3153], [4048, 4051], [4048, 4051], [6681, 6684], [6681, 6684], [2668, 2671], [2668, 2671], [7091, 7094], [7091, 7094], [2009, 2012], [2009, 2012], [3361, 3364], [3361, 3364], [1108, 1111], [1108, 1111], [758, 761], [758, 761], [2140, 2143], [2140, 2143], [2818, 2821], [2818, 2821], [4087, 4090], [4087, 4090], [656, 659], [656, 659], [2166, 2169], [2166, 2169], [2015, 2018], [2015, 2018], [762, 765], [762, 765], [840, 843], [840, 843], [2049, 2052], [2049, 2052], [1087, 1090], [1087, 1090], [2628, 2631], [2628, 2631], [3002, 3005], [3002, 3005], [3665, 3668], [3665, 3668], [1106, 1109], [1106, 1109], [1605, 1608], [1605, 1608], [5576, 5579], [5576, 5579], [1021, 1024], [1021, 1024], [2964, 2967], [2964, 2967], [5130, 5133], [5130, 5133], [6269, 6272], [6269, 6272], [1164, 1167], [1164, 1167], [2551, 2553], "[fetchBinaryPointsData]", [14374, 14377], [14374, 14377], "object", [517, 552], "type DepositManagementProps = object", [517, 552], "type DepositManagementProps = unknown", [3033, 3049], "[fetch<PERSON><PERSON><PERSON><PERSON>, selectedStatus]", [1081, 1103], "[currentPage, fetchLogs, filters]", [1869, 1902], "[users, searchTerm, statusFilter, filterUsers]", [6238, 6241], [6238, 6241], [2158, 2208], "[dateRange, filterType, filterStatus, currentPage, fetchCommissions, fetchStats]", [9857, 9860], [9857, 9860], [10357, 10360], [10357, 10360], [10836, 10839], [10836, 10839], [2435, 2438], [2435, 2438], [531, 534], [531, 534], [1104, 1154], "[currentPage, searchTerm, filterAction, dateRange, fetchLogs]", [5725, 5728], [5725, 5728], [6177, 6180], [6177, 6180], [13648, 13651], [13648, 13651], [3887, 3890], [3887, 3890], [3833, 3854], "[debouncedSearchTerm, searchTerm]", [3901, 3968], "[currentPage, debouncedSearchTerm, fetchUsers, filterStatus, sortBy, sortOrder]", [10982, 10985], [10982, 10985], [1539, 1565], "[searchTerm, filterStatus, fetchWithdrawals]", [5579, 5582], [5579, 5582], [15066, 15069], [15066, 15069], [2874, 2877], [2874, 2877], [6987, 6989], "[fetchTreeData]", [6158, 6161], [6158, 6161], [6903, 6906], [6903, 6906], [7241, 7244], [7241, 7244], [7681, 7684], [7681, 7684], [8102, 8105], [8102, 8105], [8463, 8466], [8463, 8466], [8931, 8934], [8931, 8934], [9208, 9211], [9208, 9211], [9289, 9292], [9289, 9292], [9626, 9629], [9626, 9629], [9696, 9699], [9696, 9699], [9821, 9824], [9821, 9824], [10043, 10046], [10043, 10046], [10396, 10399], [10396, 10399], [10615, 10618], [10615, 10618], [10861, 10864], [10861, 10864], [11151, 11154], [11151, 11154], [11672, 11675], [11672, 11675], [14076, 14137], "[treeData, width, margin.left, margin.right, margin.top, margin.bottom, height, convertToD3Hierarchy, expandedNodes, handleNodeToggle]", [14651, 14666], "[expandedNodes, fetchTreeData]", [2555, 2558], [2555, 2558], [2727, 2730], [2727, 2730], [4194, 4197], [4194, 4197], [6075, 6078], [6075, 6078], [7204, 7207], [7204, 7207], [1775, 1778], [1775, 1778], [10593, 10645], "[calculateSubtreeWidth, setNodes, setEdges, showInactive, expandedNodes, handleNodeExpand]", [11379, 11394], [8611, 8614], [8611, 8614], [16952, 16955], [16952, 16955], [3441, 3443], "[fetchAllTransactions, fetchAllTransactionsSilent, fetchUserWithdrawalAddress]", [3544, 3582], "[searchTerm, filterType, filterStatus, fetchAllTransactions]", [10253, 10256], [10253, 10256], [11396, 11399], [11396, 11399], [8098, 8115], "[deposits, deposits.length, pollingInterval]", [3919, 3922], [3919, 3922], [4976, 4979], [4976, 4979], [3433, 3436], [3433, 3436], [4790, 4793], [4790, 4793], [5886, 5889], [5886, 5889], [6804, 6807], [6804, 6807], [10899, 10902], [10899, 10902], [14781, 14784], [14781, 14784], [15364, 15367], [15364, 15367], [16056, 16059], [16056, 16059], [18508, 18511], [18508, 18511], [21024, 21027], [21024, 21027], [21755, 21758], [21755, 21758], [6026, 6029], [6026, 6029], [266, 269], [266, 269], [290, 293], [290, 293], [465, 468], [465, 468], [2012, 2015], [2012, 2015], [2070, 2073], [2070, 2073], [4015, 4018], [4015, 4018], [4379, 4382], [4379, 4382], [5127, 5130], [5127, 5130], [5548, 5551], [5548, 5551], [5871, 5874], [5871, 5874], [5918, 5921], [5918, 5921], [6216, 6219], [6216, 6219], [1674, 1677], [1674, 1677], [3807, 3810], [3807, 3810], [5916, 5919], [5916, 5919], [2868, 2871], [2868, 2871], [44572, 44575], [44572, 44575], [47804, 47807], [47804, 47807], [47830, 47833], [47830, 47833], [48554, 48557], [48554, 48557], [4049, 4052], [4049, 4052], [9533, 9536], [9533, 9536], [1933, 1936], [1933, 1936], [1943, 1946], [1943, 1946], [2216, 2219], [2216, 2219], [2226, 2229], [2226, 2229], [599, 602], [599, 602], [762, 765], [762, 765], [529, 532], [529, 532], [3743, 3746], [3743, 3746], [233, 236], [233, 236], [3273, 3276], [3273, 3276], [4211, 4214], [4211, 4214], [5889, 5892], [5889, 5892], [273, 276], [273, 276], [264, 267], [264, 267], [288, 291], [288, 291], [971, 974], [971, 974], [1718, 1721], [1718, 1721], [2607, 2610], [2607, 2610], [3538, 3541], [3538, 3541], [5722, 5725], [5722, 5725], [5745, 5748], [5745, 5748], [5947, 5950], [5947, 5950], [6580, 6583], [6580, 6583], [6623, 6626], [6623, 6626], [6656, 6659], [6656, 6659], [6692, 6695], [6692, 6695], [7142, 7145], [7142, 7145], [7732, 7735], [7732, 7735], [7951, 7954], [7951, 7954], [8935, 8938], [8935, 8938], [9131, 9134], [9131, 9134], [9337, 9340], [9337, 9340]]