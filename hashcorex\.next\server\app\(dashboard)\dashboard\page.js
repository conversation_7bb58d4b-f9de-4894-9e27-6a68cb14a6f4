(()=>{var e={};e.id=6337,e.ids=[6337],e.modules={1933:(e,t)=>{"use strict";function s(e){var t;let{config:s,src:a,width:r,quality:n}=e,i=n||(null==(t=s.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return s.path+"?url="+encodeURIComponent(a)+"&w="+r+"&q="+i+(a.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),s.__next_img_default=!0;let a=s},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8772:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,23259)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(dashboard)\\dashboard\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(dashboard)\\dashboard\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{VALID_LOADERS:function(){return s},imageConfigDefault:function(){return a}});let s=["default","imgix","cloudinary","akamai","custom"],a={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},14959:(e,t,s)=>{"use strict";e.exports=s(94041).vendored.contexts.AmpContext},17903:(e,t,s)=>{"use strict";e.exports=s(94041).vendored.contexts.ImageConfigContext},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20666:(e,t,s)=>{"use strict";s.d(t,{q_:()=>c});var a=s(60687),r=s(43210),n=s(16189),i=s(57445),l=s(76276);let c=({children:e,requireAuth:t=!0,requireAdmin:s=!1,redirectTo:c="/login",fallback:d})=>{let{user:o,loading:x,isInitialized:u}=(0,i.A)(),h=(0,n.useRouter)();return((0,r.useEffect)(()=>{if(u&&!x){if(t&&!o)return void h.push(c);if(s&&o&&"ADMIN"!==o.role)return void h.push("/dashboard")}},[o,x,u,t,s,c,h]),!u||x)?d||(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(l.Rh,{size:"lg",text:"Checking authentication..."})}):t&&!o?d||(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(l.Rh,{size:"lg",text:"Redirecting to login..."})}):s&&o&&"ADMIN"!==o.role?d||(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(l.Rh,{size:"lg",text:"Redirecting to dashboard..."})}):(0,a.jsx)(a.Fragment,{children:e})}},23259:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\(dashboard)\\dashboard\\page.tsx","default")},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30512:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return f},defaultHead:function(){return x}});let a=s(37366),r=s(40740),n=s(60687),i=r._(s(43210)),l=a._(s(47755)),c=s(14959),d=s(89513),o=s(34604);function x(e){void 0===e&&(e=!1);let t=[(0,n.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,n.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function u(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}s(50148);let h=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:s}=t;return e.reduce(u,[]).reverse().concat(x(s).reverse()).filter(function(){let e=new Set,t=new Set,s=new Set,a={};return r=>{let n=!0,i=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){i=!0;let t=r.key.slice(r.key.indexOf("$")+1);e.has(t)?n=!1:e.add(t)}switch(r.type){case"title":case"base":t.has(r.type)?n=!1:t.add(r.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(r.props.hasOwnProperty(t))if("charSet"===t)s.has(t)?n=!1:s.add(t);else{let e=r.props[t],s=a[t]||new Set;("name"!==t||!i)&&s.has(e)?n=!1:(s.add(e),a[t]=s)}}}return n}}()).reverse().map((e,t)=>{let a=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!s&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:a})})}let f=function(e){let{children:t}=e,s=(0,i.useContext)(c.AmpStateContext),a=(0,i.useContext)(d.HeadManagerContext);return(0,n.jsx)(l.default,{reduceComponentsToState:m,headManager:a,inAmpMode:(0,o.isInAmpMode)(s),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31261:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return c},getImageProps:function(){return l}});let a=s(37366),r=s(44953),n=s(46533),i=a._(s(1933));function l(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let c=n.Image},34604:(e,t)=>{"use strict";function s(e){let{ampFirst:t=!1,hybrid:s=!1,hasQuery:a=!1}=void 0===e?{}:e;return t||s&&a}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return s}})},39902:e=>{"use strict";e.exports=require("path")},41480:(e,t)=>{"use strict";function s(e){let{widthInt:t,heightInt:s,blurWidth:a,blurHeight:r,blurDataURL:n,objectFit:i}=e,l=a?40*a:t,c=r?40*r:s,d=l&&c?"viewBox='0 0 "+l+" "+c+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===i?"xMidYMid":"cover"===i?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return s}})},44953:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return c}}),s(50148);let a=s(41480),r=s(12756),n=["-moz-initial","fill","none","scale-down",void 0];function i(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function c(e,t){var s,c;let d,o,x,{src:u,sizes:h,unoptimized:m=!1,priority:f=!1,loading:p,className:g,quality:y,width:j,height:v,fill:N=!1,style:b,overrideSrc:w,onLoad:A,onLoadingComplete:_,placeholder:k="empty",blurDataURL:E,fetchPriority:S,decoding:C="async",layout:P,objectFit:R,objectPosition:T,lazyBoundary:I,lazyRoot:D,...M}=e,{imgConf:O,showAltText:$,blurComplete:F,defaultLoader:z}=t,U=O||r.imageConfigDefault;if("allSizes"in U)d=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),a=null==(s=U.qualities)?void 0:s.sort((e,t)=>e-t);d={...U,allSizes:e,deviceSizes:t,qualities:a}}if(void 0===z)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let W=M.loader||z;delete M.loader,delete M.srcSet;let B="__next_img_default"in W;if(B){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=W;W=t=>{let{config:s,...a}=t;return e(a)}}if(P){"fill"===P&&(N=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[P];e&&(b={...b,...e});let t={responsive:"100vw",fill:"100vw"}[P];t&&!h&&(h=t)}let Z="",L=l(j),V=l(v);if((c=u)&&"object"==typeof c&&(i(c)||void 0!==c.src)){let e=i(u)?u.default:u;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(o=e.blurWidth,x=e.blurHeight,E=E||e.blurDataURL,Z=e.src,!N)if(L||V){if(L&&!V){let t=L/e.width;V=Math.round(e.height*t)}else if(!L&&V){let t=V/e.height;L=Math.round(e.width*t)}}else L=e.width,V=e.height}let G=!f&&("lazy"===p||void 0===p);(!(u="string"==typeof u?u:Z)||u.startsWith("data:")||u.startsWith("blob:"))&&(m=!0,G=!1),d.unoptimized&&(m=!0),B&&!d.dangerouslyAllowSVG&&u.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=l(y),Y=Object.assign(N?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:R,objectPosition:T}:{},$?{}:{color:"transparent"},b),q=F||"empty"===k?null:"blur"===k?'url("data:image/svg+xml;charset=utf-8,'+(0,a.getImageBlurSvg)({widthInt:L,heightInt:V,blurWidth:o,blurHeight:x,blurDataURL:E||"",objectFit:Y.objectFit})+'")':'url("'+k+'")',X=n.includes(Y.objectFit)?"fill"===Y.objectFit?"100% 100%":"cover":Y.objectFit,K=q?{backgroundSize:X,backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:q}:{},J=function(e){let{config:t,src:s,unoptimized:a,width:r,quality:n,sizes:i,loader:l}=e;if(a)return{src:s,srcSet:void 0,sizes:void 0};let{widths:c,kind:d}=function(e,t,s){let{deviceSizes:a,allSizes:r}=e;if(s){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let a;a=e.exec(s);)t.push(parseInt(a[2]));if(t.length){let e=.01*Math.min(...t);return{widths:r.filter(t=>t>=a[0]*e),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof t?{widths:a,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>r.find(t=>t>=e)||r[r.length-1]))],kind:"x"}}(t,r,i),o=c.length-1;return{sizes:i||"w"!==d?i:"100vw",srcSet:c.map((e,a)=>l({config:t,src:s,quality:n,width:e})+" "+("w"===d?e:a+1)+d).join(", "),src:l({config:t,src:s,quality:n,width:c[o]})}}({config:d,src:u,unoptimized:m,width:L,quality:H,sizes:h,loader:W});return{props:{...M,loading:G?"lazy":p,fetchPriority:S,width:L,height:V,decoding:C,className:g,style:{...Y,...K},sizes:J.sizes,srcSet:J.srcSet,src:w||J.src},meta:{unoptimized:m,priority:f,placeholder:k,fill:N}}}},46533:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return v}});let a=s(37366),r=s(40740),n=s(60687),i=r._(s(43210)),l=a._(s(51215)),c=a._(s(30512)),d=s(44953),o=s(12756),x=s(17903);s(50148);let u=s(69148),h=a._(s(1933)),m=s(53038),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function p(e,t,s,a,r,n,i){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&r(!0),null==s?void 0:s.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let a=!1,r=!1;s.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>a,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{a=!0,t.preventDefault()},stopPropagation:()=>{r=!0,t.stopPropagation()}})}(null==a?void 0:a.current)&&a.current(e)}}))}function g(e){return i.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let y=(0,i.forwardRef)((e,t)=>{let{src:s,srcSet:a,sizes:r,height:l,width:c,decoding:d,className:o,style:x,fetchPriority:u,placeholder:h,loading:f,unoptimized:y,fill:j,onLoadRef:v,onLoadingCompleteRef:N,setBlurComplete:b,setShowAltText:w,sizesInput:A,onLoad:_,onError:k,...E}=e,S=(0,i.useCallback)(e=>{e&&(k&&(e.src=e.src),e.complete&&p(e,h,v,N,b,y,A))},[s,h,v,N,b,k,y,A]),C=(0,m.useMergedRef)(t,S);return(0,n.jsx)("img",{...E,...g(u),loading:f,width:c,height:l,decoding:d,"data-nimg":j?"fill":"1",className:o,style:x,sizes:r,srcSet:a,src:s,ref:C,onLoad:e=>{p(e.currentTarget,h,v,N,b,y,A)},onError:e=>{w(!0),"empty"!==h&&b(!0),k&&k(e)}})});function j(e){let{isAppRouter:t,imgAttributes:s}=e,a={as:"image",imageSrcSet:s.srcSet,imageSizes:s.sizes,crossOrigin:s.crossOrigin,referrerPolicy:s.referrerPolicy,...g(s.fetchPriority)};return t&&l.default.preload?(l.default.preload(s.src,a),null):(0,n.jsx)(c.default,{children:(0,n.jsx)("link",{rel:"preload",href:s.srcSet?void 0:s.src,...a},"__nimg-"+s.src+s.srcSet+s.sizes)})}let v=(0,i.forwardRef)((e,t)=>{let s=(0,i.useContext)(u.RouterContext),a=(0,i.useContext)(x.ImageConfigContext),r=(0,i.useMemo)(()=>{var e;let t=f||a||o.imageConfigDefault,s=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),r=t.deviceSizes.sort((e,t)=>e-t),n=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:s,deviceSizes:r,qualities:n}},[a]),{onLoad:l,onLoadingComplete:c}=e,m=(0,i.useRef)(l);(0,i.useEffect)(()=>{m.current=l},[l]);let p=(0,i.useRef)(c);(0,i.useEffect)(()=>{p.current=c},[c]);let[g,v]=(0,i.useState)(!1),[N,b]=(0,i.useState)(!1),{props:w,meta:A}=(0,d.getImgProps)(e,{defaultLoader:h.default,imgConf:r,blurComplete:g,showAltText:N});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(y,{...w,unoptimized:A.unoptimized,placeholder:A.placeholder,fill:A.fill,onLoadRef:m,onLoadingCompleteRef:p,setBlurComplete:v,setShowAltText:b,sizesInput:e.sizes,ref:t}),A.priority?(0,n.jsx)(j,{isAppRouter:!s,imgAttributes:w}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let a=s(43210),r=()=>{},n=()=>{};function i(e){var t;let{headManager:s,reduceComponentsToState:i}=e;function l(){if(s&&s.mountedInstances){let t=a.Children.toArray(Array.from(s.mountedInstances).filter(Boolean));s.updateHead(i(t,e))}}return null==s||null==(t=s.mountedInstances)||t.add(e.children),l(),r(()=>{var t;return null==s||null==(t=s.mountedInstances)||t.add(e.children),()=>{var t;null==s||null==(t=s.mountedInstances)||t.delete(e.children)}}),r(()=>(s&&(s._pendingUpdate=l),()=>{s&&(s._pendingUpdate=l)})),n(()=>(s&&s._pendingUpdate&&(s._pendingUpdate(),s._pendingUpdate=null),()=>{s&&s._pendingUpdate&&(s._pendingUpdate(),s._pendingUpdate=null)})),null}},57084:(e,t,s)=>{Promise.resolve().then(s.bind(s,76265))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69148:(e,t,s)=>{"use strict";e.exports=s(94041).vendored.contexts.RouterContext},70652:(e,t,s)=>{Promise.resolve().then(s.bind(s,23259))},76265:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>sX});var a,r=s(60687),n=s(43210),i=s(20666),l=s(85814),c=s.n(l),d=s(16189),o=s(57445),x=s(36679),u=s(76276),h=s(71180),m=s(49625),f=s(45583),p=s(25541),g=s(35583),y=s(41312),j=s(99891),v=s(33872),N=s(84027),b=s(11860),w=s(40083),A=s(12941),_=s(97051),k=s(78272),E=s(58869),S=s(93613);let C=({children:e,activeTab:t,onTabChange:s})=>{let{user:a,logout:i}=(0,o.A)(),l=(0,d.useRouter)(),[C,P]=(0,n.useState)(!1),[R,T]=(0,n.useState)(!1),[I,D]=(0,n.useState)(!1),M=(0,n.useRef)(null),O=[{id:"overview",label:"Overview",icon:m.A},{id:"mining",label:"Mining Units",icon:f.A},{id:"earnings",label:"Earnings",icon:p.A},{id:"wallet",label:"Wallet",icon:g.A},{id:"referrals",label:"Network",icon:y.A},{id:"kyc",label:"KYC Verification",icon:j.A},{id:"support",label:"Support",icon:v.A}],$=a?.role==="ADMIN"?[...O,{id:"admin",label:"Admin Panel",icon:N.A}]:O,F=async()=>{await i()},z=e=>{"admin"===e?l.push("/admin"):(s(e),P(!1))};return(0,n.useEffect)(()=>{let e=e=>{M.current&&!M.current.contains(e.target)&&D(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[C&&(0,r.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>P(!1)}),(0,r.jsx)("aside",{className:`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200
        transform transition-all duration-300 ease-in-out
        ${C?"translate-x-0":"-translate-x-full lg:translate-x-0"}
      `,children:(0,r.jsxs)("div",{className:"flex flex-col h-screen",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-14 px-5 border-b border-gray-200 bg-white flex-shrink-0",children:[(0,r.jsxs)(c(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)(h.MX,{className:"h-5 w-5 text-white"})}),(0,r.jsx)("span",{className:"text-lg font-bold text-gray-900",children:"HashCoreX"})]}),(0,r.jsx)("button",{onClick:()=>P(!1),className:"lg:hidden p-1.5 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("nav",{className:"flex-1 px-3 py-4 space-y-1 min-h-0",children:$.map(e=>{let s=e.icon,a=t===e.id;return(0,r.jsxs)("button",{onClick:()=>z(e.id),className:`
                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 group
                    ${a?"bg-yellow-500 text-white shadow-md":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}
                  `,children:[(0,r.jsx)(s,{className:`h-4 w-4 ${a?"text-white":"text-gray-500 group-hover:text-gray-700"}`}),(0,r.jsx)("span",{className:"font-medium text-sm",children:e.label})]},e.id)})}),(0,r.jsx)("div",{className:"px-3 py-3 border-t border-gray-200 bg-gray-50 flex-shrink-0",children:(0,r.jsxs)("button",{onClick:F,className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-gray-600 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 group-hover:text-red-600"}),(0,r.jsx)("span",{className:"font-medium text-sm",children:"Logout"})]})})]})}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col min-w-0 lg:ml-64",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12",children:(0,r.jsxs)(x.so,{justify:"between",align:"center",className:"h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:()=>P(!0),className:"lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors",children:(0,r.jsx)(A.A,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900 capitalize",children:$.find(e=>e.id===t)?.label||"Dashboard"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 hidden sm:block",children:(e=>{switch(e){case"overview":default:return"Manage your mining operations and earnings";case"mining":return"Purchase and manage your mining units";case"earnings":return"Track your mining rewards and commissions";case"wallet":return"Manage deposits, withdrawals, and balance";case"referrals":return"Build and manage your referral network";case"kyc":return"Complete your identity verification";case"support":return"Get help and manage support tickets";case"profile":return"Manage your account information and preferences";case"admin":return"Manage platform operations and users"}})(t)})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("button",{className:"relative p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,r.jsx)(_.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"})]}),a?.kycStatus!=="APPROVED"&&("kyc"===t||"wallet"===t)&&(0,r.jsxs)("div",{className:`
                    px-3 py-1.5 rounded-lg text-xs font-semibold border
                    ${a?.kycStatus==="PENDING"?"bg-solar-50 text-solar-700 border-solar-200":"bg-red-50 text-red-700 border-red-200"}
                  `,children:["KYC: ",a?.kycStatus]}),(0,r.jsxs)("div",{className:"relative",ref:M,children:[(0,r.jsxs)("button",{onClick:()=>D(!I),className:"flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-semibold text-sm",children:a?.firstName?.charAt(0).toUpperCase()||a?.email.charAt(0).toUpperCase()})}),(0,r.jsx)(k.A,{className:`h-4 w-4 text-gray-500 transition-transform ${I?"rotate-180":""}`})]}),I&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50",children:[(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold",children:a?.firstName?.charAt(0).toUpperCase()||a?.email.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-gray-900 truncate",children:a?.firstName&&a?.lastName?`${a.firstName} ${a.lastName}`:a?.email.split("@")[0]}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:["ID: ",a?.referralId]})]})]})}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("button",{onClick:()=>{D(!1),s("profile")},className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,r.jsx)(E.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Profile Settings"})]}),(0,r.jsx)("div",{className:"border-t border-gray-100 my-1"}),(0,r.jsxs)("button",{onClick:()=>{D(!1),F()},className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",children:[(0,r.jsx)(w.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Sign Out"})]})]})]})]})]})]})})}),a?.kycStatus!=="APPROVED"&&!R&&(0,r.jsx)("div",{className:"bg-yellow-50 border-b border-yellow-200",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12",children:(0,r.jsxs)("div",{className:"flex items-center justify-between py-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(S.A,{className:"h-5 w-5 text-solar-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-solar-800",children:a?.kycStatus==="PENDING"?"KYC verification in progress":"Complete your KYC verification"}),(0,r.jsx)("p",{className:"text-xs text-solar-600",children:a?.kycStatus==="PENDING"?"Your documents are being reviewed. This usually takes 1-3 business days.":"Verify your identity to enable withdrawals and unlock all features."})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[a?.kycStatus!=="PENDING"&&(0,r.jsx)(u.$n,{size:"sm",onClick:()=>s("kyc"),className:"bg-solar-600 hover:bg-solar-700 text-white",children:"Complete KYC"}),(0,r.jsx)("button",{onClick:()=>T(!0),className:"text-solar-500 hover:text-solar-700 p-1",children:(0,r.jsx)(b.A,{className:"h-4 w-4"})})]})]})})}),(0,r.jsx)("main",{className:"flex-1 bg-gray-50 overflow-y-auto",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12 py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:e})})})]})]})};var P=s(48730),R=s(62688);let T=(0,R.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var I=s(4780);let D=({onTabChange:e})=>{let[t,s]=(0,n.useState)(null),[a,i]=(0,n.useState)(!0),[l,c]=(0,n.useState)((0,I.ZU)());(0,n.useEffect)(()=>{d();let e=setInterval(()=>{c((0,I.ZU)())},1e3);return()=>clearInterval(e)},[]);let d=async()=>{try{let[e,t,a,r]=await Promise.all([fetch("/api/wallet/balance",{credentials:"include"}),fetch("/api/earnings",{credentials:"include"}),fetch("/api/mining-units",{credentials:"include"}),fetch("/api/referrals/tree?depth=1",{credentials:"include"})]),[n,i,l,c]=await Promise.all([e.json(),t.json(),a.json(),r.json()]);if(n.success&&i.success&&l.success&&c.success){let e=l.data.reduce((e,t)=>e+t.thsAmount,0),t=l.data.filter(e=>"ACTIVE"===e.status),a=t.length>0?{id:t[0].id,progressPercentage:t[0].progressPercentage||0,remainingCapacity:t[0].remainingCapacity||0,thsAmount:t[0].thsAmount}:null;s({totalTHS:e,estimatedEarnings:i.data.estimatedEarnings,walletBalance:n.data.balance,pendingEarnings:n.data.pendingEarnings,activeUnits:l.data.length,totalEarnings:i.data.totalEarnings,directReferrals:c.data.statistics.totalDirectReferrals,binaryPoints:c.data.statistics.binaryPoints,miningUnits:{totalUnits:l.summary?.totalUnits||l.data.length,expiredUnits:l.summary?.expiredUnits||0,nextToExpire:a,totalMiningEarnings:l.summary?.totalMiningEarnings||0,totalReferralEarnings:l.summary?.totalReferralEarnings||0,totalBinaryEarnings:l.summary?.totalBinaryEarnings||0}})}}catch(e){console.error("Failed to fetch dashboard stats:",e)}finally{i(!1)}};return a?(0,r.jsx)("div",{className:"space-y-6",children:Array.from({length:3}).map((e,t)=>(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-xl"})},t))}):t?(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-green-600 rounded-2xl p-8 text-white shadow-lg",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-3",children:"Welcome to HashCoreX"}),(0,r.jsx)("p",{className:"text-green-100 text-lg leading-relaxed",children:"Your sustainable mining dashboard. Track your earnings, manage your mining units, and grow your referral network."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Key Metrics"}),(0,r.jsxs)(x.xA,{cols:{default:1,sm:2,lg:4},gap:6,children:[(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Total Mining Power"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,I.jI)(t.totalTHS)})]}),(0,r.jsx)("div",{className:"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(f.A,{className:"h-7 w-7 text-solar-600"})})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Wallet Balance"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-eco-600",children:(0,I.vv)(t.walletBalance)})]}),(0,r.jsx)("div",{className:"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(g.A,{className:"h-7 w-7 text-eco-600"})})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Total Earnings"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,I.vv)(t.totalEarnings)})]}),(0,r.jsx)("div",{className:"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-7 w-7 text-blue-600"})})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Direct Referrals"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:t.directReferrals})]}),(0,r.jsx)("div",{className:"h-14 w-14 bg-purple-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"h-7 w-7 text-purple-600"})})]})})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Earnings Overview"}),(0,r.jsxs)(x.xA,{cols:{default:1,lg:2},gap:8,children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{className:"pb-4",children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-eco-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-eco-600"})}),(0,r.jsx)("span",{children:"Estimated Earnings"})]})}),(0,r.jsxs)(u.Wu,{className:"pt-0",children:[(0,r.jsxs)("div",{className:"space-y-5",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,r.jsx)("span",{className:"text-gray-600 font-medium",children:"Next 7 Days"}),(0,r.jsx)("span",{className:"font-bold text-eco-600 text-lg",children:(0,I.vv)(t.estimatedEarnings.next7Days)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,r.jsx)("span",{className:"text-gray-600 font-medium",children:"Next 30 Days"}),(0,r.jsx)("span",{className:"font-bold text-eco-600 text-lg",children:(0,I.vv)(t.estimatedEarnings.next30Days)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,r.jsx)("span",{className:"text-gray-600 font-medium",children:"Next 365 Days"}),(0,r.jsx)("span",{className:"font-bold text-eco-600 text-lg",children:(0,I.vv)(t.estimatedEarnings.next365Days)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,r.jsx)("span",{className:"text-gray-600 font-medium",children:"Next 2 Years"}),(0,r.jsx)("span",{className:"font-bold text-eco-600 text-lg",children:(0,I.vv)(t.estimatedEarnings.next2Years)})]})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-eco-50 rounded-xl",children:(0,r.jsx)("p",{className:"text-sm text-eco-700 font-medium",children:"* Based on current mining units and average ROI"})})]})]}),(0,r.jsxs)(u.Zp,{className:"hover:shadow-lg transition-shadow duration-200",children:[(0,r.jsx)(u.aR,{className:"pb-4",children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(P.A,{className:"h-5 w-5 text-solar-600"})}),(0,r.jsx)("span",{children:"Next Payout"})]})}),(0,r.jsx)(u.Wu,{className:"pt-0",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-6 font-medium",children:"Weekly payout every Saturday at 15:00 UTC"}),(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"bg-solar-50 rounded-xl p-3 mb-2",children:(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:l.days})}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Days"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"bg-solar-50 rounded-xl p-3 mb-2",children:(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:l.hours})}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Hours"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"bg-solar-50 rounded-xl p-3 mb-2",children:(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:l.minutes})}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Min"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"bg-solar-50 rounded-xl p-3 mb-2",children:(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:l.seconds})}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Sec"})]})]}),t.pendingEarnings>0&&(0,r.jsx)("div",{className:"mt-6 p-4 bg-solar-50 rounded-xl",children:(0,r.jsxs)("p",{className:"text-sm text-solar-700 font-semibold",children:[(0,r.jsx)("strong",{className:"text-lg",children:(0,I.vv)(t.pendingEarnings)})," pending"]})})]})})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Quick Actions"}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-8",children:(0,r.jsxs)(x.xA,{cols:{default:1,sm:2,lg:3},gap:6,children:[(0,r.jsxs)(u.$n,{className:"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl bg-green-600 text-white border-0",onClick:()=>e?.("mining"),children:[(0,r.jsx)(h.NC,{className:"h-7 w-7"}),(0,r.jsx)("span",{children:"Buy Mining Power"})]}),(0,r.jsxs)(u.$n,{variant:"outline",className:"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 border-green-500 text-green-600",onClick:()=>e?.("wallet"),children:[(0,r.jsx)(h.Lc,{className:"h-7 w-7"}),(0,r.jsx)("span",{children:"Withdraw USDT"})]}),(0,r.jsxs)(u.$n,{variant:"outline",className:"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 border-green-500 text-green-600",onClick:()=>e?.("referrals"),children:[(0,r.jsx)(y.A,{className:"h-7 w-7"}),(0,r.jsx)("span",{children:"Build Network"})]})]})})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Mining Units Overview"}),(0,r.jsxs)(x.xA,{cols:{default:1,lg:2},gap:8,children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{className:"pb-4",children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-eco-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-eco-600"})}),(0,r.jsx)("span",{children:"Earnings Breakdown"})]})}),(0,r.jsx)(u.Wu,{className:"pt-0",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-green-50 rounded-lg",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Mining Earnings"}),(0,r.jsx)("span",{className:"font-bold text-green-600",children:(0,I.vv)(t.miningUnits.totalMiningEarnings)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Referral Earnings"}),(0,r.jsx)("span",{className:"font-bold text-blue-600",children:(0,I.vv)(t.miningUnits.totalReferralEarnings)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Binary Earnings"}),(0,r.jsx)("span",{className:"font-bold text-purple-600",children:(0,I.vv)(t.miningUnits.totalBinaryEarnings)})]})]})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{className:"pb-4",children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(P.A,{className:"h-5 w-5 text-orange-600"})}),(0,r.jsx)("span",{children:"FIFO Expiration System"})]})}),(0,r.jsx)(u.Wu,{className:"pt-0",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"Units expire at 5x investment using FIFO order"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Units:"}),(0,r.jsx)("span",{className:"font-medium ml-2",children:t.miningUnits.totalUnits})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Expired:"}),(0,r.jsx)("span",{className:"font-medium ml-2 text-red-600",children:t.miningUnits.expiredUnits})]})]})]}),t.miningUnits.nextToExpire?(0,r.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"Next to Expire (FIFO #1)"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Mining Power:"}),(0,r.jsx)("span",{className:"font-medium",children:(0,I.jI)(t.miningUnits.nextToExpire.thsAmount)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Progress:"}),(0,r.jsxs)("span",{className:"font-medium text-orange-600",children:[t.miningUnits.nextToExpire.progressPercentage.toFixed(1),"%"]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Remaining:"}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:(0,I.vv)(t.miningUnits.nextToExpire.remainingCapacity)})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,r.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(t.miningUnits.nextToExpire.progressPercentage,100)}%`}})})]})]}):(0,r.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg text-center",children:(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"No active mining units"})})]})})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Binary Network Summary"}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{className:"pb-4",children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-3 text-lg",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(T,{className:"h-5 w-5 text-solar-600"})}),(0,r.jsx)("span",{children:"Network Performance"})]})}),(0,r.jsx)(u.Wu,{className:"pt-0",children:(0,r.jsxs)(x.xA,{cols:{default:1,sm:3},gap:8,children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"bg-solar-50 rounded-xl p-6 mb-3",children:(0,r.jsx)("div",{className:"text-3xl font-bold text-solar-600",children:t.binaryPoints.leftPoints})}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Left Points"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"bg-solar-50 rounded-xl p-6 mb-3",children:(0,r.jsx)("div",{className:"text-3xl font-bold text-solar-600",children:t.binaryPoints.rightPoints})}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Right Points"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"bg-eco-50 rounded-xl p-6 mb-3",children:(0,r.jsx)("div",{className:"text-3xl font-bold text-eco-600",children:Math.min(t.binaryPoints.leftPoints,t.binaryPoints.rightPoints)})}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Potential Match"})]})]})})]})]})]}):(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Failed to load dashboard data"})})})};var M=s(23928);let O=(0,R.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),$=({onPurchaseComplete:e})=>{let[t,s]=(0,n.useState)({thsAmount:"",investmentAmount:""}),[a,i]=(0,n.useState)(!1),[l,c]=(0,n.useState)(""),[d,o]=(0,n.useState)(50),[x,m]=(0,n.useState)([{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}]),[p,g]=(0,n.useState)(100),{showConfirm:y,hideConfirm:j,ConfirmDialog:v}=(0,u.G_)(),{showMessage:N,hideMessage:b,MessageBoxComponent:w}=(0,u.eC)();(0,n.useEffect)(()=>{A()},[]);let A=async()=>{try{let e=await fetch("/api/admin/settings/pricing");if(e.ok){let t=await e.json();t.success&&(o(t.data.thsPrice),m(t.data.earningsRanges),g(t.data.minPurchaseAmount))}}catch(e){console.error("Failed to fetch pricing:",e)}},_=e=>x.find(t=>e>=t.minTHS&&e<=t.maxTHS)||x[x.length-1],k=async e=>{e.preventDefault(),c("");try{let e=parseFloat(t.thsAmount),s=parseFloat(t.investmentAmount);if(!e||!s||e<=0||s<=0)throw Error("Please enter valid amounts");if(s<p)throw Error(`Minimum purchase amount is $${p}`);let{daily:a,weekly:n,monthly:i,range:l}=S();y({title:"Confirm Mining Unit Purchase",message:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"TH/s Amount:"}),(0,r.jsxs)("span",{className:"font-semibold",children:[e.toFixed(4)," TH/s"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Investment Amount:"}),(0,r.jsxs)("span",{className:"font-semibold",children:["$",s.toFixed(2)," USD"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Price per TH/s:"}),(0,r.jsxs)("span",{className:"font-semibold",children:["$",d.toFixed(2)," USD"]})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg space-y-2",children:[(0,r.jsxs)("h4",{className:"font-semibold text-green-800",children:["Estimated Earnings (Average ROI: ",l?`${l.dailyReturnMin}-${l.dailyReturnMax}%`:"0.4%",")"]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"font-semibold text-green-600",children:["$",a.toFixed(2)]}),(0,r.jsx)("div",{className:"text-green-700",children:"Daily"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"font-semibold text-green-600",children:["$",n.toFixed(2)]}),(0,r.jsx)("div",{className:"text-green-700",children:"Weekly"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"font-semibold text-green-600",children:["$",i.toFixed(2)]}),(0,r.jsx)("div",{className:"text-green-700",children:"Monthly"})]})]})]}),(0,r.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,r.jsx)("strong",{children:"Important:"})," Mining units are active for 24 months and expire when 5x investment is earned."]})})]}),confirmText:"Purchase Mining Unit",cancelText:"Cancel",variant:"default",onConfirm:()=>E(e,s)})}catch(e){c(e.message||"Invalid purchase details")}},E=async(t,a)=>{i(!0);try{let r=await fetch("/api/mining-units",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({thsAmount:t,investmentAmount:a})}),n=await r.json();if(!n.success)throw Error(n.error||"Purchase failed");N({title:"Mining Unit Purchased",message:"Your mining unit has been purchased successfully! You will start earning daily returns within 24 hours.",variant:"success",buttonText:"OK"}),s({thsAmount:"",investmentAmount:""}),e&&e()}catch(e){N({title:"Purchase Failed",message:e.message||"Failed to purchase mining unit. Please try again.",variant:"error",buttonText:"OK"})}finally{i(!1)}},S=()=>{let e=parseFloat(t.investmentAmount)||0,s=parseFloat(t.thsAmount)||0;if(e<=0||s<=0)return{daily:0,weekly:0,monthly:0,range:null};let a=_(s),r=e*((a.dailyReturnMin+a.dailyReturnMax)/2)/100;return{daily:r,weekly:7*r,monthly:30*r,range:a}},C=S();return(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{className:"pb-4",children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-3 text-xl",children:[(0,r.jsx)("div",{className:"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(h.NC,{className:"h-6 w-6 text-solar-600"})}),(0,r.jsx)("span",{children:"Purchase Mining Power"})]})}),(0,r.jsxs)(u.Wu,{className:"pt-0",children:[(0,r.jsxs)("form",{onSubmit:k,className:"space-y-8",children:[l&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-xl text-sm font-medium",children:l}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-solar-50 to-eco-50 rounded-xl p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"text-center sm:text-left",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600 block mb-1",children:"Current TH/s Price"}),(0,r.jsxs)("span",{className:"text-2xl font-bold text-solar-600",children:[(0,I.vv)(d)," / TH/s"]})]}),(0,r.jsxs)("div",{className:"text-center sm:text-right",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600 block mb-1",children:C.range?`Daily ROI Range (${C.range.minTHS}-${C.range.maxTHS} TH/s)`:"Daily ROI Range"}),(0,r.jsx)("span",{className:"text-xl font-bold text-eco-600",children:C.range?`${C.range.dailyReturnMin}% - ${C.range.dailyReturnMax}%`:"0.3% - 0.7%"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"TH/s Based Earnings Tiers"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:x.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg p-3 border border-gray-200",children:[(0,r.jsxs)("div",{className:"text-xs font-medium text-gray-600 mb-1",children:[e.minTHS," - ",999999===e.maxTHS?"∞":e.maxTHS," TH/s"]}),(0,r.jsxs)("div",{className:"text-sm font-bold text-eco-600",children:[e.dailyReturnMin,"% - ",e.dailyReturnMax,"%"]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Monthly: ",e.monthlyReturnMin,"% - ",e.monthlyReturnMax,"%"]})]},t))})]})]}),(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{children:(0,r.jsx)(u.pd,{label:"TH/s Amount",type:"number",step:"0.0001",min:"0",value:t.thsAmount,onChange:e=>{let t=(parseFloat(e.target.value)||0)*d;s({thsAmount:e.target.value,investmentAmount:t>0?t.toFixed(2):""})},placeholder:"Enter TH/s amount",leftIcon:(0,r.jsx)(f.A,{className:"h-4 w-4"}),className:"h-12"})}),(0,r.jsx)("div",{children:(0,r.jsx)(u.pd,{label:"Investment Amount (USD)",type:"number",step:"0.01",min:p,value:t.investmentAmount,onChange:e=>{let t=(parseFloat(e.target.value)||0)/d;s({thsAmount:t>0?t.toFixed(4):"",investmentAmount:e.target.value})},placeholder:"Enter investment amount",leftIcon:(0,r.jsx)(M.A,{className:"h-4 w-4"}),className:"h-12"})})]})}),C.daily>0&&(0,r.jsxs)("div",{className:"bg-gradient-to-r from-eco-50 to-green-50 rounded-xl p-6",children:[(0,r.jsxs)("h4",{className:"text-base font-semibold text-gray-800 mb-4 flex items-center",children:[(0,r.jsx)("div",{className:"h-8 w-8 bg-eco-100 rounded-lg flex items-center justify-center mr-3",children:(0,r.jsx)(O,{className:"h-4 w-4 text-eco-600"})}),"Estimated Earnings ",C.range?`(Average ROI: ${(0,I.ZV)((C.range.dailyReturnMin+C.range.dailyReturnMax)/2,1)}%)`:""]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl p-4 shadow-sm",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-eco-600 mb-1",children:(0,I.vv)(C.daily)}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Daily"})]})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl p-4 shadow-sm",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-eco-600 mb-1",children:(0,I.vv)(C.weekly)}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Weekly"})]})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl p-4 shadow-sm",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-eco-600 mb-1",children:(0,I.vv)(C.monthly)}),(0,r.jsx)("div",{className:"text-sm text-gray-600 font-medium",children:"Monthly"})]})})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6",children:[(0,r.jsx)("h4",{className:"text-base font-semibold text-gray-800 mb-4",children:"Important Notes:"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,r.jsxs)("span",{children:["Minimum purchase: $",p]})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,r.jsx)("span",{children:"Mining units are active for 24 months"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,r.jsx)("span",{children:"Units expire when 5x investment is earned"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,r.jsx)("span",{children:"Weekly payouts every Saturday at 15:00 UTC"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-solar-500 mr-2",children:"•"}),(0,r.jsx)("span",{children:"ROI varies daily based on mining performance"})]})]})]}),(0,r.jsxs)(u.$n,{type:"submit",size:"lg",className:"w-full h-14 text-lg font-semibold rounded-xl",loading:a,disabled:!t.thsAmount||!t.investmentAmount||parseFloat(t.investmentAmount)<p,children:[(0,r.jsx)(h.Lc,{className:"h-6 w-6 mr-3"}),"Purchase Mining Unit"]})]}),(0,r.jsx)(v,{}),(0,r.jsx)(w,{})]})]})};var F=s(40228);let z=()=>{let[e,t]=(0,n.useState)(null),[s,a]=(0,n.useState)(!0),[i,l]=(0,n.useState)((0,I.ZU)());(0,n.useEffect)(()=>{c();let e=setInterval(()=>{l((0,I.ZU)())},1e3);return()=>clearInterval(e)},[]);let c=async()=>{try{let e=await fetch("/api/earnings",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&t(s.data)}}catch(e){console.error("Failed to fetch earnings data:",e)}finally{a(!1)}};if(s)return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-xl"})})});if(!e)return(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Failed to load earnings data"})})});let d=e=>{switch(e){case"MINING_EARNINGS":return"text-solar-600";case"DIRECT_REFERRAL":return"text-eco-600";case"BINARY_BONUS":return"text-blue-600";default:return"text-gray-600"}},o=e=>{switch(e){case"MINING_EARNINGS":return"Mining";case"DIRECT_REFERRAL":return"Direct Referral";case"BINARY_BONUS":return"Binary Bonus";default:return e}};return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Earnings Overview"}),(0,r.jsxs)(x.xA,{cols:{default:1,sm:2,lg:4},gap:6,children:[(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Total Earnings"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,I.vv)(e.totalEarnings)})]}),(0,r.jsx)("div",{className:"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(M.A,{className:"h-7 w-7 text-eco-600"})})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Pending Earnings"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-solar-600",children:(0,I.vv)(e.pendingEarnings)})]}),(0,r.jsx)("div",{className:"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(P.A,{className:"h-7 w-7 text-solar-600"})})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Mining Earnings"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,I.vv)(e.miningEarnings)})]}),(0,r.jsx)("div",{className:"h-14 w-14 bg-gray-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-7 w-7 text-gray-600"})})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Referral Earnings"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-dark-900",children:(0,I.vv)(e.referralEarnings)})]}),(0,r.jsx)("div",{className:"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-7 w-7 text-blue-600"})})]})})})]})]}),(0,r.jsxs)(x.xA,{cols:{default:1,lg:2},gap:6,children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(F.A,{className:"h-5 w-5 text-solar-500"}),(0,r.jsx)("span",{children:"Next Payout"})]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Weekly payout every Saturday at 15:00 UTC"}),(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:i.days}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Days"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:i.hours}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Hours"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:i.minutes}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Minutes"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-solar-600",children:i.seconds}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Seconds"})]})]}),e.pendingEarnings>0&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-solar-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-solar-700",children:[(0,r.jsx)("strong",{children:(0,I.vv)(e.pendingEarnings)})," will be transferred to your wallet"]})})]})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-eco-500"}),(0,r.jsx)("span",{children:"Estimated Earnings"})]})}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Next 7 Days"}),(0,r.jsx)("span",{className:"font-semibold text-eco-600",children:(0,I.vv)(e.estimatedEarnings.next7Days)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Next 30 Days"}),(0,r.jsx)("span",{className:"font-semibold text-eco-600",children:(0,I.vv)(e.estimatedEarnings.next30Days)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Next 365 Days"}),(0,r.jsx)("span",{className:"font-semibold text-eco-600",children:(0,I.vv)(e.estimatedEarnings.next365Days)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Next 2 Years"}),(0,r.jsx)("span",{className:"font-semibold text-eco-600",children:(0,I.vv)(e.estimatedEarnings.next2Years)})]})]}),(0,r.jsx)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"* Estimates based on current mining units and average ROI"})})]})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"Recent Earnings"})}),(0,r.jsx)(u.Wu,{children:e.recentEarnings.length>0?(0,r.jsx)("div",{className:"space-y-3",children:e.recentEarnings.slice(0,10).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`text-sm font-medium ${d(e.type)}`,children:o(e.type)}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:(0,I.r6)(e.createdAt)})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("span",{className:"font-semibold text-eco-600",children:["+",(0,I.vv)(e.amount)]})})]},e.id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-gray-500",children:"No earnings yet"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Purchase mining units to start earning"})]})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(P.A,{className:"h-5 w-5 text-blue-500"}),(0,r.jsx)("span",{children:"FIFO Earnings Allocation System"})]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"How It Works"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• All earnings (Mining + Referral + Binary) are allocated to your oldest mining units first"}),(0,r.jsx)("li",{children:"• Mining units expire when they reach 5x their investment amount"}),(0,r.jsx)("li",{children:"• Units expire in First-In-First-Out (FIFO) order - oldest units expire first"}),(0,r.jsx)("li",{children:"• This ensures fair and predictable expiration timing"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg text-center",children:[(0,r.jsx)("div",{className:"font-medium text-green-700",children:"Mining Earnings"}),(0,r.jsx)("div",{className:"text-xs text-green-600 mt-1",children:"Daily ROI from your mining units"})]}),(0,r.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg text-center",children:[(0,r.jsx)("div",{className:"font-medium text-blue-700",children:"Referral Earnings"}),(0,r.jsx)("div",{className:"text-xs text-blue-600 mt-1",children:"10% commission from direct referrals"})]}),(0,r.jsxs)("div",{className:"p-3 bg-purple-50 rounded-lg text-center",children:[(0,r.jsx)("div",{className:"font-medium text-purple-700",children:"Binary Earnings"}),(0,r.jsx)("div",{className:"text-xs text-purple-600 mt-1",children:"Weekly matching bonuses"})]})]}),(0,r.jsxs)("div",{className:"p-4 bg-yellow-50 rounded-lg border border-yellow-200",children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-900 mb-2",children:"\uD83D\uDCA1 Pro Tip"}),(0,r.jsx)("p",{className:"text-sm text-yellow-800",children:"Check your Mining Units tab to see the detailed earnings breakdown for each unit and track which unit will expire next in the FIFO queue."})]})]})})]})]})},U=(0,R.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),W=(0,R.A)("arrow-down-left",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]]);var B=s(5336),Z=s(35071),L=s(80462),V=s(78122),G=s(99270),H=s(43649);let Y=(0,R.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var q=s(96882);let X=()=>{let[e,t]=(0,n.useState)(null),[s,a]=(0,n.useState)(null),[i,l]=(0,n.useState)([]),[c,d]=(0,n.useState)(!0),[o,x]=(0,n.useState)(!1),[h,m]=(0,n.useState)(null),[f,p]=(0,n.useState)(null),[y,j]=(0,n.useState)(""),[v,N]=(0,n.useState)(!1),[b,w]=(0,n.useState)(null),A=async()=>{try{d(!0);let e=await fetch("/api/wallet/deposit/info",{credentials:"include"});if(!e.ok)throw Error("Failed to fetch deposit information");let s=await e.json();if(s.success)t(s.data.depositInfo),a(s.data.userStats),l(s.data.deposits);else throw Error(s.error||"Failed to fetch deposit information")}catch(e){m(e instanceof Error?e.message:"An error occurred")}finally{d(!1)}},_=async()=>{try{let e=await fetch("/api/wallet/deposit/info",{credentials:"include"});if(!e.ok)return;let s=await e.json();s.success&&(t(s.data.depositInfo),a(s.data.userStats),l(s.data.deposits))}catch(e){console.error("Silent fetch failed:",e)}},k=async()=>{if(e?.depositAddress)try{await navigator.clipboard.writeText(e.depositAddress),N(!0),setTimeout(()=>N(!1),2e3)}catch(e){console.error("Failed to copy address:",e)}},E=async e=>{if(e.preventDefault(),y.trim())try{x(!0),m(null),p(null);let e=await fetch("/api/wallet/deposit/verify",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({transactionId:y.trim()})}),t=await e.json();t.success?("PENDING_VERIFICATION"===t.data.status?p("Transaction submitted successfully! We are now verifying your deposit. This may take up to 2 minutes."):p(`Deposit verified successfully! ${(0,I.vv)(t.data.amount)} USDT has been credited to your wallet.`),j(""),await A()):m(t.error||"Failed to verify transaction")}catch(e){m(e instanceof Error?e.message:"An error occurred")}finally{x(!1)}},S=e=>{switch(e){case"COMPLETED":case"CONFIRMED":return(0,r.jsx)(B.A,{className:"w-4 h-4 text-green-600"});case"PENDING_VERIFICATION":return(0,r.jsx)(V.A,{className:"w-4 h-4 text-blue-600 animate-spin"});case"WAITING_FOR_CONFIRMATIONS":return(0,r.jsx)(P.A,{className:"w-4 h-4 text-orange-600"});case"PENDING":return(0,r.jsx)(P.A,{className:"w-4 h-4 text-yellow-600"});case"FAILED":case"REJECTED":return(0,r.jsx)(H.A,{className:"w-4 h-4 text-red-600"});default:return(0,r.jsx)(P.A,{className:"w-4 h-4 text-gray-600"})}},C=e=>{switch(e){case"COMPLETED":case"CONFIRMED":return"text-green-700 bg-green-100";case"PENDING_VERIFICATION":return"text-blue-700 bg-blue-100";case"WAITING_FOR_CONFIRMATIONS":return"text-orange-700 bg-orange-100";case"PENDING":return"text-yellow-700 bg-yellow-100";case"FAILED":case"REJECTED":return"text-red-700 bg-red-100";default:return"text-gray-700 bg-gray-100"}},R=(e,t,s)=>{switch(e){case"COMPLETED":case"CONFIRMED":return"Deposit completed successfully";case"PENDING_VERIFICATION":return"Verifying transaction on blockchain...";case"WAITING_FOR_CONFIRMATIONS":return`Waiting for confirmations (${t||0}/${s||10})`;case"PENDING":return"Transaction verified, processing deposit...";case"FAILED":return"Transaction verification failed";case"REJECTED":return"Deposit rejected by admin";default:return"Processing..."}},T=(e,t,s)=>{switch(e){case"COMPLETED":case"CONFIRMED":default:return null;case"PENDING_VERIFICATION":return"Within 2 minutes";case"WAITING_FOR_CONFIRMATIONS":let a=(s||10)-(t||0);return a>0?`~${3*a} minutes`:"Processing...";case"PENDING":return"Within 1 minute"}};return((0,n.useEffect)(()=>{A();let e=setInterval(()=>{_()},3e4);return i.some(e=>["PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS","PENDING"].includes(e.status))&&(b&&clearInterval(b),w(setInterval(()=>{_()},1e4))),()=>{clearInterval(e),b&&clearInterval(b)}},[i.length]),(0,n.useEffect)(()=>{!i.some(e=>["PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS","PENDING"].includes(e.status))&&b&&(clearInterval(b),w(null))},[i,b]),c)?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)(V.A,{className:"w-8 h-8 animate-spin text-blue-600"})}):e?e.depositEnabled?e.depositAddress?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Deposit USDT"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Add funds to your wallet using USDT TRC20"})]}),(0,r.jsxs)("button",{onClick:A,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[(0,r.jsx)(V.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Refresh"})]})]}),s&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Total Deposited"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(0,I.vv)(s.totalDeposited)," USDT"]})]}),(0,r.jsx)(M.A,{className:"w-8 h-8 text-green-600"})]})})}),(0,r.jsx)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Successful Deposits"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s.depositCount})]}),(0,r.jsx)(B.A,{className:"w-8 h-8 text-blue-600"})]})})}),(0,r.jsx)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Pending Deposits"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s.pendingDeposits})]}),(0,r.jsx)(P.A,{className:"w-8 h-8 text-yellow-600"})]})})})]}),(0,r.jsxs)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"text-gray-900 flex items-center space-x-2",children:[(0,r.jsx)(q.A,{className:"w-5 h-5 text-blue-600"}),(0,r.jsx)("span",{children:"How to Deposit"})]})}),(0,r.jsxs)(u.Wu,{className:"space-y-4",children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-900 font-medium",children:"Send USDT TRC20 to the address below"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Only send USDT on the TRC20 network. Other tokens or networks will result in loss of funds."})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-900 font-medium",children:"Copy your transaction ID"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"After sending, copy the transaction ID from your wallet or block explorer."})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-900 font-medium",children:"Submit transaction ID below"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Paste your transaction ID in the form below to verify and credit your deposit."})]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Network:"}),(0,r.jsx)("span",{className:"text-gray-900 ml-2 font-medium",children:e.network})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Currency:"}),(0,r.jsx)("span",{className:"text-gray-900 ml-2 font-medium",children:e.currency})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Min Amount:"}),(0,r.jsxs)("span",{className:"text-gray-900 ml-2 font-medium",children:[(0,I.vv)(e.minDepositAmount)," USDT"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Max Amount:"}),(0,r.jsxs)("span",{className:"text-gray-900 ml-2 font-medium",children:[(0,I.vv)(e.maxDepositAmount)," USDT"]})]})]})]})]}),(0,r.jsxs)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(u.ZB,{className:"text-gray-900",children:"Deposit Address"}),e.tronNetwork&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full ${"mainnet"===e.tronNetwork?"bg-green-500":"bg-orange-500"} animate-pulse`}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"mainnet"===e.tronNetwork?"Mainnet Active":"Testnet Active"})]})]})}),(0,r.jsxs)(u.Wu,{className:"space-y-4",children:[(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 mr-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"USDT Address"}),e.tronNetwork&&(0,r.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"mainnet"===e.tronNetwork?"bg-green-100 text-green-700":"bg-orange-100 text-orange-700"}`,children:"mainnet"===e.tronNetwork?"Mainnet":"Testnet"})]}),(0,r.jsx)("p",{className:"text-gray-900 font-mono text-sm break-all",children:e.depositAddress})]}),(0,r.jsxs)("button",{onClick:k,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[v?(0,r.jsx)(B.A,{className:"w-4 h-4"}):(0,r.jsx)(Y,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:v?"Copied!":"Copy"})]})]})}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(H.A,{className:"w-5 h-5 text-yellow-600 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-yellow-700 font-medium",children:"Important Notice"}),(0,r.jsx)("p",{className:"text-gray-700 text-sm mt-1",children:"Only send USDT on the TRC20 network to this address. Sending other cryptocurrencies or using different networks will result in permanent loss of funds."})]})]})})]})]}),(0,r.jsxs)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{className:"text-gray-900",children:"Verify Your Deposit"})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("form",{onSubmit:E,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-gray-700 text-sm mb-2",children:"Transaction ID"}),(0,r.jsx)(u.pd,{type:"text",value:y,onChange:e=>j(e.target.value),placeholder:"Enter your TRON transaction ID (64 characters)",className:"bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500",disabled:o}),(0,r.jsx)("p",{className:"text-gray-600 text-xs mt-1",children:"Transaction ID should be 64 characters long and contain only letters and numbers"})]}),h&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-red-700",children:h})}),f&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-green-700",children:f})}),(0,r.jsx)(u.$n,{type:"submit",disabled:!y.trim()||o,className:"w-full bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50",children:o?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(V.A,{className:"w-4 h-4 animate-spin"}),(0,r.jsx)("span",{children:"Verifying Transaction..."})]}):"Verify Deposit"})]})})]}),i.length>0&&(0,r.jsxs)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{className:"text-gray-900",children:"Recent Deposits"})}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:i.map(t=>(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[S(t.status),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-gray-900 font-medium",children:[t.amount>0?(0,I.vv)(t.amount):"---"," USDT"]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:(0,I.Yq)(t.createdAt)})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${C(t.status)}`,children:(0,r.jsx)("span",{children:t.status.replace("_"," ")})}),(0,r.jsxs)("p",{className:"text-gray-600 text-xs mt-1",children:["TX: ",t.transactionId.slice(0,8),"...",t.transactionId.slice(-8)]})]})]}),(0,r.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("p",{className:"text-blue-700 text-sm",children:R(t.status,t.confirmations,e?.minConfirmations)}),T(t.status,t.confirmations,e?.minConfirmations)&&(0,r.jsxs)("p",{className:"text-blue-600 text-xs",children:["ETA: ",T(t.status,t.confirmations,e?.minConfirmations)]})]}),t.confirmations>0&&e?.minConfirmations&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs text-blue-600 mb-1",children:[(0,r.jsx)("span",{children:"Confirmations"}),(0,r.jsxs)("span",{children:[t.confirmations,"/",e.minConfirmations]})]}),(0,r.jsx)("div",{className:"w-full bg-blue-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(t.confirmations/e.minConfirmations*100,100)}%`}})})]})]}),t.failureReason&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded",children:(0,r.jsx)("p",{className:"text-red-700 text-sm",children:t.failureReason})})]},t.id))})})]})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(g.A,{className:"h-12 w-12 text-gray-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Deposit Address Not Configured"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Please contact support to enable deposits."})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(H.A,{className:"h-12 w-12 text-yellow-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Deposits Temporarily Disabled"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Deposit service is currently unavailable. Please check back later."})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(H.A,{className:"h-12 w-12 text-red-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Deposit Service Unavailable"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Unable to load deposit information. Please try again later."})]})},K=()=>{let[e,t]=(0,n.useState)("overview"),[s,a]=(0,n.useState)(null),[i,l]=(0,n.useState)(null),[c,d]=(0,n.useState)(!0),[o,h]=(0,n.useState)(!1),[m,f]=(0,n.useState)({amount:"",usdtAddress:""}),[p,y]=(0,n.useState)(""),[v,N]=(0,n.useState)(!1),[b,w]=(0,n.useState)(""),{showConfirm:A,hideConfirm:_,ConfirmDialog:k}=(0,u.G_)(),{showMessage:E,hideMessage:S,MessageBoxComponent:C}=(0,u.eC)(),[R,T]=(0,n.useState)([]),[D,M]=(0,n.useState)(!1),[O,$]=(0,n.useState)(""),[F,z]=(0,n.useState)("ALL"),[q,K]=(0,n.useState)("ALL"),[J,Q]=(0,n.useState)(!1),[ee,et]=(0,n.useState)([]),[es,ea]=(0,n.useState)([]),[er,en]=(0,n.useState)(null),[ei,el]=(0,n.useState)(!1);(0,n.useEffect)(()=>{ec(),eh(),eo(),em();let e=setInterval(()=>{ed(),ex()},3e4);return()=>{clearInterval(e)}},[]),(0,n.useEffect)(()=>{eo()},[O,F,q]);let ec=async()=>{try{let e=await fetch("/api/wallet/balance",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&a(t.data)}}catch(e){console.error("Failed to fetch wallet data:",e)}finally{d(!1)}},ed=async()=>{try{let e=await fetch("/api/wallet/balance",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&a(t.data)}}catch(e){console.error("Failed to fetch wallet data silently:",e)}},eo=async()=>{try{M(!0);let e=new URLSearchParams;e.append("limit","50"),O&&e.append("search",O),"ALL"!==F&&e.append("type",F),"ALL"!==q&&e.append("status",q);let t=await fetch(`/api/wallet/transactions?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(T(e.data.transactions),et(e.data.filters.transactionTypes),ea(e.data.filters.statusOptions))}}catch(e){console.error("Failed to fetch transactions:",e)}finally{M(!1)}},ex=async()=>{try{let e=new URLSearchParams;e.append("limit","50"),O&&e.append("search",O),"ALL"!==F&&e.append("type",F),"ALL"!==q&&e.append("status",q);let t=await fetch(`/api/wallet/transactions?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(T(e.data.transactions),et(e.data.filters.transactionTypes),ea(e.data.filters.statusOptions))}}catch(e){console.error("Failed to fetch transactions silently:",e)}},eu=e=>{en(e),el(!0)},eh=async()=>{try{let e=await fetch("/api/wallet/withdrawal-settings",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&l(t.data)}}catch(e){console.error("Failed to fetch withdrawal settings:",e)}},em=async()=>{try{let e=await fetch("/api/user/withdrawal-address",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&t.data.withdrawalAddress&&(y(t.data.withdrawalAddress),m.usdtAddress||f(e=>({...e,usdtAddress:t.data.withdrawalAddress})))}}catch(e){console.error("Failed to fetch user withdrawal address:",e)}},ef=async e=>{e.preventDefault(),w("");try{let e=parseFloat(m.amount);if(!e||e<=0)throw Error("Please enter a valid amount");if(!m.usdtAddress)throw Error("Please enter a USDT address");let t=i?.fixedFee||3,s=e*(i?.percentageFee||1)/100,a=t+s,n=e-a;A({title:"Confirm Withdrawal",message:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Withdrawal Amount:"}),(0,r.jsxs)("span",{className:"font-semibold",children:["$",e.toFixed(2)," USDT"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Fixed Fee:"}),(0,r.jsxs)("span",{className:"text-red-600",children:["-$",t.toFixed(2)," USDT"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Percentage Fee (1%):"}),(0,r.jsxs)("span",{className:"text-red-600",children:["-$",s.toFixed(2)," USDT"]})]}),(0,r.jsxs)("div",{className:"border-t pt-2 flex justify-between font-semibold",children:[(0,r.jsx)("span",{className:"text-green-600",children:"You Receive:"}),(0,r.jsxs)("span",{className:"text-green-600",children:["$",n.toFixed(2)," USDT"]})]})]}),(0,r.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,r.jsx)("strong",{children:"USDT Address:"}),(0,r.jsx)("br",{}),m.usdtAddress]})}),(0,r.jsx)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm text-yellow-800",children:"⚠️ Please double-check your address. Transactions cannot be reversed."})})]}),confirmText:"Confirm Withdrawal",cancelText:"Cancel",variant:"warning",onConfirm:()=>ep(e)})}catch(e){w(e.message||"Invalid withdrawal details")}},ep=async e=>{N(!0);try{let s=await fetch("/api/wallet/withdraw",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({amount:e,usdtAddress:m.usdtAddress})}),a=await s.json();if(!a.success)throw Error(a.error||"Withdrawal failed");E({title:"Withdrawal Submitted",message:"Your withdrawal request has been submitted successfully. It will be processed within 3 business days.",variant:"success",buttonText:"OK"}),f({amount:"",usdtAddress:""}),t("overview"),ec(),eo()}catch(e){E({title:"Withdrawal Failed",message:e.message||"Failed to process withdrawal. Please try again.",variant:"error",buttonText:"OK"})}finally{N(!1)}},eg=e=>{switch(e){case"WITHDRAWAL":return(0,r.jsx)(U,{className:"h-4 w-4 text-red-500"});case"DEPOSIT":case"MINING_EARNINGS":case"DIRECT_REFERRAL":case"BINARY_BONUS":return(0,r.jsx)(W,{className:"h-4 w-4 text-eco-500"});default:return(0,r.jsx)(g.A,{className:"h-4 w-4 text-gray-500"})}},ey=e=>{switch(e){case"WITHDRAWAL":case"PURCHASE":return"text-red-600";case"DEPOSIT":case"MINING_EARNINGS":case"DIRECT_REFERRAL":case"BINARY_BONUS":return"text-eco-600";default:return"text-gray-600"}},ej=e=>{switch(e){case"COMPLETED":case"APPROVED":return(0,r.jsx)(B.A,{className:"h-4 w-4 text-eco-500"});case"PENDING":return(0,r.jsx)(P.A,{className:"h-4 w-4 text-solar-500"});case"FAILED":case"REJECTED":return(0,r.jsx)(Z.A,{className:"h-4 w-4 text-red-500"});default:return(0,r.jsx)(P.A,{className:"h-4 w-4 text-gray-500"})}},ev=e=>{if(!i||!e)return{fixedFee:0,percentageFee:0,totalFees:0,totalDeduction:0,netAmount:0};let t=i.fixedFee,s=e*i.percentageFee/100,a=t+s;return{fixedFee:t,percentageFee:s,totalFees:a,totalDeduction:e+a,netAmount:e}};if(c)return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-xl"})})});if(!s)return(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Failed to load wallet data"})})});let eN=[{id:"overview",label:"Overview",icon:g.A},{id:"deposit",label:"Deposit",icon:W},{id:"withdraw",label:"Withdraw",icon:U}],eb=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("div",{children:(0,r.jsxs)(x.xA,{cols:{default:1,lg:2},gap:8,children:[(0,r.jsx)(u.Zp,{children:(0,r.jsxs)(u.Wu,{className:"p-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Available Balance"}),(0,r.jsx)("p",{className:"text-4xl font-bold text-dark-900",children:(0,I.vv)(s.balance)})]}),(0,r.jsx)("div",{className:"h-16 w-16 bg-eco-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(g.A,{className:"h-8 w-8 text-eco-600"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,r.jsxs)(u.$n,{onClick:()=>t("deposit"),variant:"outline",className:"h-12 text-base font-semibold rounded-xl",children:[(0,r.jsx)(W,{className:"h-5 w-5 mr-2"}),"Deposit"]}),(0,r.jsxs)(u.$n,{onClick:()=>t("withdraw"),className:"h-12 text-base font-semibold rounded-xl",disabled:s.balance<10,children:[(0,r.jsx)(U,{className:"h-5 w-5 mr-2"}),"Withdraw"]})]})]})}),(0,r.jsx)(u.Zp,{children:(0,r.jsxs)(u.Wu,{className:"p-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-2",children:"Pending Earnings"}),(0,r.jsx)("p",{className:"text-4xl font-bold text-solar-600",children:(0,I.vv)(s.pendingEarnings)})]}),(0,r.jsx)("div",{className:"h-16 w-16 bg-solar-100 rounded-xl flex items-center justify-center",children:(0,r.jsx)(P.A,{className:"h-8 w-8 text-solar-600"})})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Will be transferred on Saturday at 15:00 UTC"})]})})]})}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[(0,r.jsx)(u.ZB,{children:"Transaction History"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-stretch sm:items-center gap-2",children:[(0,r.jsxs)(u.$n,{variant:"outline",size:"sm",onClick:()=>Q(!J),className:"flex items-center justify-center space-x-2 w-full sm:w-auto",children:[(0,r.jsx)(L.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Filters"})]}),(0,r.jsxs)(u.$n,{variant:"outline",size:"sm",onClick:eo,disabled:D,className:"flex items-center justify-center space-x-2 w-full sm:w-auto",children:[(0,r.jsx)(V.A,{className:`h-4 w-4 ${D?"animate-spin":""}`}),(0,r.jsx)("span",{children:"Refresh"})]})]})]})}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(G.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(u.pd,{placeholder:"Search transactions by description, type, or reference...",value:O,onChange:e=>$(e.target.value),className:"pl-10"})]}),J&&(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Transaction Type"}),(0,r.jsx)("select",{value:F,onChange:e=>z(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500 text-sm",children:ee.map(e=>(0,r.jsx)("option",{value:e,children:"ALL"===e?"All Types":e.replace("_"," ")},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,r.jsx)("select",{value:q,onChange:e=>K(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500 text-sm",children:es.map(e=>(0,r.jsx)("option",{value:e,children:"ALL"===e?"All Status":e.replace("_"," ")},e))})]})]})]}),D?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(V.A,{className:"h-8 w-8 animate-spin mx-auto text-gray-400 mb-2"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Loading transactions..."})]}):R.length>0?(0,r.jsx)("div",{className:"space-y-3",children:R.map(e=>(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer gap-3",onClick:()=>eu(e),children:[(0,r.jsxs)("div",{className:"flex items-start sm:items-center space-x-3 flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"flex-shrink-0 mt-1 sm:mt-0",children:eg(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"font-medium text-dark-900 truncate",children:e.description}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-gray-500 mt-1 gap-1 sm:gap-0",children:[(0,r.jsx)("span",{className:"text-xs sm:text-sm",children:(0,I.r6)(e.createdAt)}),(0,r.jsx)("span",{className:"px-2 py-1 bg-gray-200 rounded text-xs w-fit",children:e.type.replace("_"," ")}),e.reference&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs cursor-pointer hover:bg-blue-200 w-fit",onClick:t=>{t.stopPropagation(),(0,I.lW)(e.reference)},title:"Click to copy reference",children:["Ref: ",e.reference.substring(0,8),"..."]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between sm:justify-end sm:space-x-3 gap-2 flex-shrink-0",children:[(0,r.jsxs)("span",{className:`font-semibold text-lg sm:text-base ${ey(e.type)}`,children:["WITHDRAWAL"===e.type||"PURCHASE"===e.type?"-":"+",(0,I.vv)(e.amount)]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:ej(e.status)})]})]},e.id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-gray-500",children:"No transactions found"}),(O||"ALL"!==F||"ALL"!==q)&&(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"Try adjusting your search or filter criteria"})]})]})]})]}),ew=()=>{let e=parseFloat(m.amount)||0,a=ev(e);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Withdraw USDT"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Withdraw funds from your wallet to your USDT TRC20 address"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Available Balance"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(0,I.vv)(s?.balance||0)," USDT"]})]}),(0,r.jsx)(g.A,{className:"w-8 h-8 text-green-600"})]})})}),(0,r.jsx)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Minimum Withdrawal"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(0,I.vv)(i?.minWithdrawalAmount||10)," USDT"]})]}),(0,r.jsx)(U,{className:"w-8 h-8 text-blue-600"})]})})}),(0,r.jsx)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:(0,r.jsx)(u.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Network"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"USDT (TRC20)"})]}),(0,r.jsx)(j.A,{className:"w-8 h-8 text-purple-600"})]})})})]}),(0,r.jsxs)(u.Zp,{className:"bg-white border-gray-200 shadow-sm",children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{className:"text-gray-900",children:"Withdrawal Details"})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("form",{onSubmit:ef,className:"space-y-6",children:[b&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:b}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Withdrawal Amount (USDT)"}),(0,r.jsx)(u.pd,{type:"number",step:"0.01",min:i?.minWithdrawalAmount||10,max:s?.balance||0,value:m.amount,onChange:e=>f(t=>({...t,amount:e.target.value})),placeholder:"Enter amount to withdraw",className:"bg-white border-gray-300 text-gray-900",required:!0}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Min: ",(0,I.vv)(i?.minWithdrawalAmount||10)," USDT"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"USDT TRC20 Address"}),(0,r.jsx)(u.pd,{type:"text",value:m.usdtAddress,onChange:e=>f(t=>({...t,usdtAddress:e.target.value})),placeholder:"Enter your USDT TRC20 address",className:"bg-white border-gray-300 text-gray-900",required:!0}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:p&&m.usdtAddress===p?"Auto-filled from your saved withdrawal address":"Only TRC20 addresses are supported"})]})]}),e>0&&(0,r.jsxs)(u.Zp,{className:"bg-gray-50 border-gray-200",children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{className:"text-lg text-gray-900",children:"Transaction Summary"})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Withdrawal Amount:"}),(0,r.jsxs)("span",{className:"font-semibold text-gray-900",children:[(0,I.vv)(e)," USDT"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Fixed Fee:"}),(0,r.jsxs)("span",{className:"font-semibold text-red-600",children:["-",(0,I.vv)(a.fixedFee)," USDT"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-gray-600",children:["Percentage Fee (",i?.percentageFee||0,"%):"]}),(0,r.jsxs)("span",{className:"font-semibold text-red-600",children:["-",(0,I.vv)(a.percentageFee)," USDT"]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-300 pt-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:"Total Fees:"}),(0,r.jsxs)("span",{className:"font-bold text-red-600",children:[(0,I.vv)(a.totalFees)," USDT"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:"Total Deduction:"}),(0,r.jsxs)("span",{className:"font-bold text-red-600",children:[(0,I.vv)(a.totalDeduction)," USDT"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mt-3 p-3 bg-green-50 rounded-lg",children:[(0,r.jsx)("span",{className:"text-green-800 font-bold text-lg",children:"You Receive:"}),(0,r.jsxs)("span",{className:"font-bold text-green-600 text-xl",children:[(0,I.vv)(a.netAmount)," USDT"]})]})]})]})})]})]}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(H.A,{className:"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"Important Information"}),(0,r.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,r.jsx)("li",{children:"• Only USDT TRC20 addresses are supported"}),(0,r.jsx)("li",{children:"• Withdrawals require KYC verification"}),(0,r.jsxs)("li",{children:["• Processing time: ",i?.processingDays||3," business days"]}),(0,r.jsx)("li",{children:"• Double-check your address - transactions cannot be reversed"}),(0,r.jsx)("li",{children:"• Fees are deducted from your balance in addition to the withdrawal amount"})]})]})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,r.jsx)(u.$n,{type:"button",variant:"outline",onClick:()=>t("overview"),className:"flex-1 border-gray-300 text-gray-700 hover:bg-gray-50",children:"Back to Overview"}),(0,r.jsx)(u.$n,{type:"submit",loading:v,className:"flex-1 bg-yellow-500 hover:bg-yellow-600 text-white",disabled:!s||!i||e<(i?.minWithdrawalAmount||10)||a.totalDeduction>s.balance,children:a.totalDeduction>(s?.balance||0)?"Insufficient Balance":"Submit Withdrawal"})]})]})})]})]})};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:eN.map(s=>{let a=s.icon,n=e===s.id;return(0,r.jsxs)("button",{onClick:()=>t(s.id),className:`
                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors
                  ${n?"border-solar-500 text-solar-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
                `,children:[(0,r.jsx)(a,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:s.label})]},s.id)})})}),(()=>{switch(e){case"deposit":return(0,r.jsx)(X,{});case"withdraw":return ew();default:return eb()}})(),ei&&er&&(0,r.jsx)(u.aF,{isOpen:ei,onClose:()=>el(!1),title:"Transaction Details",size:"lg",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[eg(er.type),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg text-dark-900",children:er.description}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[er.type.replace("_"," ")," • ",(0,I.r6)(er.createdAt)]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[ej(er.status),(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"COMPLETED"===er.status||"CONFIRMED"===er.status?"bg-eco-100 text-eco-700":"PENDING"===er.status?"bg-solar-100 text-solar-700":"bg-red-100 text-red-700"}`,children:er.status})]}),(0,r.jsxs)("p",{className:`text-xl font-bold ${ey(er.type)}`,children:["WITHDRAWAL"===er.type||"PURCHASE"===er.type?"-":"+",(0,I.vv)(er.amount)]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Transaction ID"}),(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0",children:er.id}),(0,r.jsx)("button",{onClick:()=>(0,I.lW)(er.id),className:"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1",children:(0,r.jsx)(Y,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:er.type.replace("_"," ")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[ej(er.status),(0,r.jsx)("span",{className:"text-sm text-gray-900",children:er.status})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date & Time"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:(0,I.r6)(er.createdAt)})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[er.reference&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reference"}),(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0",children:er.reference}),(0,r.jsx)("button",{onClick:()=>(0,I.lW)(er.reference),className:"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1",children:(0,r.jsx)(Y,{className:"h-4 w-4"})})]})]}),er.txid&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Transaction Hash"}),(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0",children:er.txid}),(0,r.jsx)("button",{onClick:()=>(0,I.lW)(er.txid),className:"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1",children:(0,r.jsx)(Y,{className:"h-4 w-4"})})]})]}),er.usdtAddress&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"DEPOSIT"===er.type?"Deposit Address":"Withdrawal Address"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all",children:er.usdtAddress}),(0,r.jsx)("button",{onClick:()=>(0,I.lW)(er.usdtAddress),className:"text-gray-400 hover:text-gray-600 flex-shrink-0",children:(0,r.jsx)(Y,{className:"h-4 w-4"})})]})]}),void 0!==er.confirmations&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirmations"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:er.confirmations})]}),er.processedAt&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Processed At"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:(0,I.r6)(er.processedAt)})]})]})]}),er.rejectionReason&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-red-800 mb-2",children:"Rejection Reason"}),(0,r.jsx)("p",{className:"text-sm text-red-700",children:er.rejectionReason})]}),er.senderAddress&&(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"Sender Information"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("p",{className:"text-sm text-blue-700 font-mono",children:er.senderAddress}),(0,r.jsx)("button",{onClick:()=>(0,I.lW)(er.senderAddress),className:"text-blue-400 hover:text-blue-600",children:(0,r.jsx)(Y,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t",children:[(0,r.jsx)(u.$n,{variant:"outline",onClick:()=>el(!1),children:"Close"}),er.txid&&(0,r.jsxs)(u.$n,{onClick:()=>{let e=`https://tronscan.org/#/transaction/${er.txid}`;window.open(e,"_blank")},className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:"View on Explorer"}),(0,r.jsx)(U,{className:"h-4 w-4"})]})]})]})}),(0,r.jsx)(k,{}),(0,r.jsx)(C,{})]})},J=(0,R.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var Q=s(12597),ee=s(13861),et=s(53411),es=s(28947);function ea(){let[e,t]=(0,n.useState)(!1);return e}let er=()=>{let[e,t]=(0,n.useState)(null),[s,a]=(0,n.useState)(!0),i=function(e,t=1e3){let[s,a]=(0,n.useState)(null);return ea(),s}(I.D1)||{days:0,hours:0,minutes:0,seconds:0};(0,n.useEffect)(()=>{l()},[]);let l=async()=>{try{let e=await fetch("/api/binary-points/info",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&t(s.data)}}catch(e){console.error("Failed to fetch binary points info:",e)}finally{a(!1)}};if(s)return(0,r.jsx)("div",{className:"space-y-6",children:Array.from({length:3}).map((e,t)=>(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-xl"})},t))});if(!e)return(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Failed to load binary points information"})})});let c=({value:e,max:t,color:s="bg-green-500",warningThreshold:a=90})=>{let n=e/t*100;return(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,r.jsx)("div",{className:`h-3 rounded-full transition-all duration-300 ${n>=a?"bg-red-500":s}`,style:{width:`${Math.min(n,100)}%`}})})};return(0,r.jsxs)("div",{className:"space-y-6",children:[e.hasWarnings&&(0,r.jsxs)(u.Zp,{className:"border-orange-200 bg-orange-50",children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2 text-orange-800",children:[(0,r.jsx)(H.A,{className:"h-5 w-5"}),"Important Notices"]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("ul",{className:"space-y-2",children:e.warnings.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start gap-2 text-orange-700",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-sm",children:e})]},t))})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(et.A,{className:"h-5 w-5 text-green-600"}),"Current Binary Points Status"]})}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)(x.xA,{cols:{default:1,md:2},gap:6,children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Left Side Points"}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:(0,I.ZV)(e.currentPoints.left,0)})]}),(0,r.jsx)(c,{value:e.currentPoints.leftCapped,max:e.limits.maxPointsPerSide,color:"bg-green-500"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,r.jsx)("span",{children:"0"}),(0,r.jsxs)("span",{children:[(0,I.ZV)(e.limits.maxPointsPerSide,0)," (cap)"]})]}),e.pressureOut.leftAmount>0&&(0,r.jsxs)("p",{className:"text-xs text-red-600",children:[(0,I.ZV)(e.pressureOut.leftAmount,0)," points will be flushed"]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Right Side Points"}),(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:(0,I.ZV)(e.currentPoints.right,0)})]}),(0,r.jsx)(c,{value:e.currentPoints.rightCapped,max:e.limits.maxPointsPerSide,color:"bg-blue-500"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,r.jsx)("span",{children:"0"}),(0,r.jsxs)("span",{children:[(0,I.ZV)(e.limits.maxPointsPerSide,0)," (cap)"]})]}),e.pressureOut.rightAmount>0&&(0,r.jsxs)("p",{className:"text-xs text-red-600",children:[(0,I.ZV)(e.pressureOut.rightAmount,0)," points will be flushed"]})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(es.A,{className:"h-5 w-5 text-purple-600"}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:"Points Available for Matching"})]}),(0,r.jsx)("span",{className:"text-2xl font-bold text-purple-600",children:(0,I.ZV)(e.currentPoints.matchable,0)})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:["Minimum of left (",(0,I.ZV)(e.currentPoints.leftCapped,0),") and right (",(0,I.ZV)(e.currentPoints.rightCapped,0),") sides"]})]})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(P.A,{className:"h-5 w-5 text-blue-600"}),"Next Weekly Binary Matching"]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:e.nextMatching.schedule}),(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4 mb-4",suppressHydrationWarning:!0,children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.days}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Days"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.hours}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Hours"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.minutes}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Minutes"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.seconds}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Seconds"})]})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.nextMatching.description})]})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(M.A,{className:"h-5 w-5 text-green-600"}),"Earnings Estimation"]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,r.jsx)("span",{className:"font-medium text-green-800",children:"Estimated Weekly Payout"}),(0,r.jsx)("span",{className:"text-xl font-bold text-green-600",children:(0,I.vv)(e.earnings.estimatedPayout)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Point Value:"}),(0,r.jsxs)("div",{className:"font-semibold",children:[(0,I.vv)(e.earnings.pointValue)," per point"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Your Matchable Points:"}),(0,r.jsx)("div",{className:"font-semibold",children:(0,I.ZV)(e.earnings.matchablePoints,0)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Estimated Payout:"}),(0,r.jsx)("div",{className:"font-semibold",children:(0,I.vv)(e.earnings.estimatedPayout)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Max Points Per Side:"}),(0,r.jsx)("div",{className:"font-semibold",children:(0,I.ZV)(e.limits.maxPointsPerSide,0)})]})]})]})})]}),e.history.recentMatches.length>0&&(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-purple-600"}),"Recent Binary Matching History"]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-purple-50 rounded-lg",children:[(0,r.jsx)("span",{className:"font-medium text-purple-800",children:"Average Weekly Earnings"}),(0,r.jsx)("span",{className:"text-lg font-bold text-purple-600",children:(0,I.vv)(e.history.averageWeeklyEarnings)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Last 4 Weeks"}),e.history.recentMatches.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:(0,I.vv)(e.amount)}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:(0,I.Yq)(e.date)})]}),(0,r.jsx)("div",{className:"text-xs text-gray-600 max-w-xs text-right",children:e.description})]},t))]})]})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(q.A,{className:"h-5 w-5 text-blue-600"}),"How Binary Matching Works"]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4 text-sm text-gray-700",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Point Accumulation"}),(0,r.jsxs)("ul",{className:"space-y-1 ml-4",children:[(0,r.jsx)("li",{children:"• Every $1 invested by your downline = 1 binary point"}),(0,r.jsx)("li",{children:"• Points are added to left or right side based on placement"}),(0,r.jsxs)("li",{children:["• Maximum ",(0,I.ZV)(e.limits.maxPointsPerSide,0)," points per side"]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Weekly Matching Process"}),(0,r.jsxs)("ul",{className:"space-y-1 ml-4",children:[(0,r.jsx)("li",{children:"• Matching occurs every Saturday at 15:00 UTC"}),(0,r.jsx)("li",{children:"• Matched points = minimum of left and right sides"}),(0,r.jsxs)("li",{children:["• Fixed earnings: ",(0,I.vv)(e.limits.pointValue)," per matched point"]}),(0,r.jsx)("li",{children:"• Excess points beyond cap are flushed (pressure-out)"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Pressure-Out System"}),(0,r.jsxs)("ul",{className:"space-y-1 ml-4",children:[(0,r.jsxs)("li",{children:["• Points exceeding ",(0,I.ZV)(e.limits.maxPointsPerSide,0)," per side are automatically flushed"]}),(0,r.jsx)("li",{children:"• Encourages balanced team building on both sides"}),(0,r.jsx)("li",{children:"• Prevents point hoarding and ensures fair distribution"})]})]})]})})]})]})};function en(e){var t=0,s=e.children,a=s&&s.length;if(a)for(;--a>=0;)t+=s[a].value;else t=1;e.value=t}function ei(e,t){e instanceof Map?(e=[void 0,e],void 0===t&&(t=ec)):void 0===t&&(t=el);for(var s,a,r,n,i,l=new ex(e),c=[l];s=c.pop();)if((r=t(s.data))&&(i=(r=Array.from(r)).length))for(s.children=r,n=i-1;n>=0;--n)c.push(a=r[n]=new ex(r[n])),a.parent=s,a.depth=s.depth+1;return l.eachBefore(eo)}function el(e){return e.children}function ec(e){return Array.isArray(e)?e[1]:null}function ed(e){void 0!==e.data.value&&(e.value=e.data.value),e.data=e.data.data}function eo(e){var t=0;do e.height=t;while((e=e.parent)&&e.height<++t)}function ex(e){this.data=e,this.depth=this.height=0,this.parent=null}function eu(e,t){return e.parent===t.parent?1:2}function eh(e){var t=e.children;return t?t[0]:e.t}function em(e){var t=e.children;return t?t[t.length-1]:e.t}function ef(e,t){this._=e,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=t}function ep(){}function eg(e){return null==e?ep:function(){return this.querySelector(e)}}function ey(){return[]}function ej(e){return null==e?ey:function(){return this.querySelectorAll(e)}}function ev(e){return function(){return this.matches(e)}}function eN(e){return function(t){return t.matches(e)}}ex.prototype=ei.prototype={constructor:ex,count:function(){return this.eachAfter(en)},each:function(e,t){let s=-1;for(let a of this)e.call(t,a,++s,this);return this},eachAfter:function(e,t){for(var s,a,r,n=this,i=[n],l=[],c=-1;n=i.pop();)if(l.push(n),s=n.children)for(a=0,r=s.length;a<r;++a)i.push(s[a]);for(;n=l.pop();)e.call(t,n,++c,this);return this},eachBefore:function(e,t){for(var s,a,r=this,n=[r],i=-1;r=n.pop();)if(e.call(t,r,++i,this),s=r.children)for(a=s.length-1;a>=0;--a)n.push(s[a]);return this},find:function(e,t){let s=-1;for(let a of this)if(e.call(t,a,++s,this))return a},sum:function(e){return this.eachAfter(function(t){for(var s=+e(t.data)||0,a=t.children,r=a&&a.length;--r>=0;)s+=a[r].value;t.value=s})},sort:function(e){return this.eachBefore(function(t){t.children&&t.children.sort(e)})},path:function(e){for(var t=this,s=function(e,t){if(e===t)return e;var s=e.ancestors(),a=t.ancestors(),r=null;for(e=s.pop(),t=a.pop();e===t;)r=e,e=s.pop(),t=a.pop();return r}(t,e),a=[t];t!==s;)a.push(t=t.parent);for(var r=a.length;e!==s;)a.splice(r,0,e),e=e.parent;return a},ancestors:function(){for(var e=this,t=[e];e=e.parent;)t.push(e);return t},descendants:function(){return Array.from(this)},leaves:function(){var e=[];return this.eachBefore(function(t){t.children||e.push(t)}),e},links:function(){var e=this,t=[];return e.each(function(s){s!==e&&t.push({source:s.parent,target:s})}),t},copy:function(){return ei(this).eachBefore(ed)},[Symbol.iterator]:function*(){var e,t,s,a,r=this,n=[r];do for(e=n.reverse(),n=[];r=e.pop();)if(yield r,t=r.children)for(s=0,a=t.length;s<a;++s)n.push(t[s]);while(n.length)}},ef.prototype=Object.create(ex.prototype);var eb=Array.prototype.find;function ew(){return this.firstElementChild}var eA=Array.prototype.filter;function e_(){return Array.from(this.children)}function ek(e){return Array(e.length)}function eE(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function eS(e,t,s,a,r,n){for(var i,l=0,c=t.length,d=n.length;l<d;++l)(i=t[l])?(i.__data__=n[l],a[l]=i):s[l]=new eE(e,n[l]);for(;l<c;++l)(i=t[l])&&(r[l]=i)}function eC(e,t,s,a,r,n,i){var l,c,d,o=new Map,x=t.length,u=n.length,h=Array(x);for(l=0;l<x;++l)(c=t[l])&&(h[l]=d=i.call(c,c.__data__,l,t)+"",o.has(d)?r[l]=c:o.set(d,c));for(l=0;l<u;++l)d=i.call(e,n[l],l,n)+"",(c=o.get(d))?(a[l]=c,c.__data__=n[l],o.delete(d)):s[l]=new eE(e,n[l]);for(l=0;l<x;++l)(c=t[l])&&o.get(h[l])===c&&(r[l]=c)}function eP(e){return e.__data__}function eR(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}eE.prototype={constructor:eE,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var eT="http://www.w3.org/1999/xhtml";let eI={svg:"http://www.w3.org/2000/svg",xhtml:eT,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function eD(e){var t=e+="",s=t.indexOf(":");return s>=0&&"xmlns"!==(t=e.slice(0,s))&&(e=e.slice(s+1)),eI.hasOwnProperty(t)?{space:eI[t],local:e}:e}function eM(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function eO(e,t){return e.style.getPropertyValue(t)||eM(e).getComputedStyle(e,null).getPropertyValue(t)}function e$(e){return e.trim().split(/^|\s+/)}function eF(e){return e.classList||new ez(e)}function ez(e){this._node=e,this._names=e$(e.getAttribute("class")||"")}function eU(e,t){for(var s=eF(e),a=-1,r=t.length;++a<r;)s.add(t[a])}function eW(e,t){for(var s=eF(e),a=-1,r=t.length;++a<r;)s.remove(t[a])}function eB(){this.textContent=""}function eZ(){this.innerHTML=""}function eL(){this.nextSibling&&this.parentNode.appendChild(this)}function eV(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function eG(e){var t=eD(e);return(t.local?function(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}:function(e){return function(){var t=this.ownerDocument,s=this.namespaceURI;return s===eT&&t.documentElement.namespaceURI===eT?t.createElement(e):t.createElementNS(s,e)}})(t)}function eH(){return null}function eY(){var e=this.parentNode;e&&e.removeChild(this)}function eq(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function eX(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function eK(e){return function(){var t=this.__on;if(t){for(var s,a=0,r=-1,n=t.length;a<n;++a)(s=t[a],e.type&&s.type!==e.type||s.name!==e.name)?t[++r]=s:this.removeEventListener(s.type,s.listener,s.options);++r?t.length=r:delete this.__on}}}function eJ(e,t,s){return function(){var a,r=this.__on,n=function(e){t.call(this,e,this.__data__)};if(r){for(var i=0,l=r.length;i<l;++i)if((a=r[i]).type===e.type&&a.name===e.name){this.removeEventListener(a.type,a.listener,a.options),this.addEventListener(a.type,a.listener=n,a.options=s),a.value=t;return}}this.addEventListener(e.type,n,s),a={type:e.type,name:e.name,value:t,listener:n,options:s},r?r.push(a):this.__on=[a]}}function eQ(e,t,s){var a=eM(e),r=a.CustomEvent;"function"==typeof r?r=new r(t,s):(r=a.document.createEvent("Event"),s?(r.initEvent(t,s.bubbles,s.cancelable),r.detail=s.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}ez.prototype={add:function(e){0>this._names.indexOf(e)&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var e0=[null];function e1(e,t){this._groups=e,this._parents=t}function e2(){return new e1([[document.documentElement]],e0)}function e5(e){return"string"==typeof e?new e1([[document.querySelector(e)]],[document.documentElement]):new e1([[e]],e0)}e1.prototype=e2.prototype={constructor:e1,select:function(e){"function"!=typeof e&&(e=eg(e));for(var t=this._groups,s=t.length,a=Array(s),r=0;r<s;++r)for(var n,i,l=t[r],c=l.length,d=a[r]=Array(c),o=0;o<c;++o)(n=l[o])&&(i=e.call(n,n.__data__,o,l))&&("__data__"in n&&(i.__data__=n.__data__),d[o]=i);return new e1(a,this._parents)},selectAll:function(e){if("function"==typeof e){var t;t=e,e=function(){var e;return e=t.apply(this,arguments),null==e?[]:Array.isArray(e)?e:Array.from(e)}}else e=ej(e);for(var s=this._groups,a=s.length,r=[],n=[],i=0;i<a;++i)for(var l,c=s[i],d=c.length,o=0;o<d;++o)(l=c[o])&&(r.push(e.call(l,l.__data__,o,c)),n.push(l));return new e1(r,n)},selectChild:function(e){var t;return this.select(null==e?ew:(t="function"==typeof e?e:eN(e),function(){return eb.call(this.children,t)}))},selectChildren:function(e){var t;return this.selectAll(null==e?e_:(t="function"==typeof e?e:eN(e),function(){return eA.call(this.children,t)}))},filter:function(e){"function"!=typeof e&&(e=ev(e));for(var t=this._groups,s=t.length,a=Array(s),r=0;r<s;++r)for(var n,i=t[r],l=i.length,c=a[r]=[],d=0;d<l;++d)(n=i[d])&&e.call(n,n.__data__,d,i)&&c.push(n);return new e1(a,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,eP);var s=t?eC:eS,a=this._parents,r=this._groups;"function"!=typeof e&&(y=e,e=function(){return y});for(var n=r.length,i=Array(n),l=Array(n),c=Array(n),d=0;d<n;++d){var o=a[d],x=r[d],u=x.length,h="object"==typeof(g=e.call(o,o&&o.__data__,d,a))&&"length"in g?g:Array.from(g),m=h.length,f=l[d]=Array(m),p=i[d]=Array(m);s(o,x,f,p,c[d]=Array(u),h,t);for(var g,y,j,v,N=0,b=0;N<m;++N)if(j=f[N]){for(N>=b&&(b=N+1);!(v=p[b])&&++b<m;);j._next=v||null}}return(i=new e1(i,a))._enter=l,i._exit=c,i},enter:function(){return new e1(this._enter||this._groups.map(ek),this._parents)},exit:function(){return new e1(this._exit||this._groups.map(ek),this._parents)},join:function(e,t,s){var a=this.enter(),r=this,n=this.exit();return"function"==typeof e?(a=e(a))&&(a=a.selection()):a=a.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==s?n.remove():s(n),a&&r?a.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,s=this._groups,a=t._groups,r=s.length,n=a.length,i=Math.min(r,n),l=Array(r),c=0;c<i;++c)for(var d,o=s[c],x=a[c],u=o.length,h=l[c]=Array(u),m=0;m<u;++m)(d=o[m]||x[m])&&(h[m]=d);for(;c<r;++c)l[c]=s[c];return new e1(l,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,s=e.length;++t<s;)for(var a,r=e[t],n=r.length-1,i=r[n];--n>=0;)(a=r[n])&&(i&&4^a.compareDocumentPosition(i)&&i.parentNode.insertBefore(a,i),i=a);return this},sort:function(e){function t(t,s){return t&&s?e(t.__data__,s.__data__):!t-!s}e||(e=eR);for(var s=this._groups,a=s.length,r=Array(a),n=0;n<a;++n){for(var i,l=s[n],c=l.length,d=r[n]=Array(c),o=0;o<c;++o)(i=l[o])&&(d[o]=i);d.sort(t)}return new e1(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,s=e.length;t<s;++t)for(var a=e[t],r=0,n=a.length;r<n;++r){var i=a[r];if(i)return i}return null},size:function(){let e=0;for(let t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,s=0,a=t.length;s<a;++s)for(var r,n=t[s],i=0,l=n.length;i<l;++i)(r=n[i])&&e.call(r,r.__data__,i,n);return this},attr:function(e,t){var s=eD(e);if(arguments.length<2){var a=this.node();return s.local?a.getAttributeNS(s.space,s.local):a.getAttribute(s)}return this.each((null==t?s.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}}:"function"==typeof t?s.local?function(e,t){return function(){var s=t.apply(this,arguments);null==s?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,s)}}:function(e,t){return function(){var s=t.apply(this,arguments);null==s?this.removeAttribute(e):this.setAttribute(e,s)}}:s.local?function(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}:function(e,t){return function(){this.setAttribute(e,t)}})(s,t))},style:function(e,t,s){return arguments.length>1?this.each((null==t?function(e){return function(){this.style.removeProperty(e)}}:"function"==typeof t?function(e,t,s){return function(){var a=t.apply(this,arguments);null==a?this.style.removeProperty(e):this.style.setProperty(e,a,s)}}:function(e,t,s){return function(){this.style.setProperty(e,t,s)}})(e,t,null==s?"":s)):eO(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?function(e){return function(){delete this[e]}}:"function"==typeof t?function(e,t){return function(){var s=t.apply(this,arguments);null==s?delete this[e]:this[e]=s}}:function(e,t){return function(){this[e]=t}})(e,t)):this.node()[e]},classed:function(e,t){var s=e$(e+"");if(arguments.length<2){for(var a=eF(this.node()),r=-1,n=s.length;++r<n;)if(!a.contains(s[r]))return!1;return!0}return this.each(("function"==typeof t?function(e,t){return function(){(t.apply(this,arguments)?eU:eW)(this,e)}}:t?function(e){return function(){eU(this,e)}}:function(e){return function(){eW(this,e)}})(s,t))},text:function(e){return arguments.length?this.each(null==e?eB:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}:function(e){return function(){this.textContent=e}})(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?eZ:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}:function(e){return function(){this.innerHTML=e}})(e)):this.node().innerHTML},raise:function(){return this.each(eL)},lower:function(){return this.each(eV)},append:function(e){var t="function"==typeof e?e:eG(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})},insert:function(e,t){var s="function"==typeof e?e:eG(e),a=null==t?eH:"function"==typeof t?t:eg(t);return this.select(function(){return this.insertBefore(s.apply(this,arguments),a.apply(this,arguments)||null)})},remove:function(){return this.each(eY)},clone:function(e){return this.select(e?eX:eq)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,s){var a,r,n=(e+"").trim().split(/^|\s+/).map(function(e){var t="",s=e.indexOf(".");return s>=0&&(t=e.slice(s+1),e=e.slice(0,s)),{type:e,name:t}}),i=n.length;if(arguments.length<2){var l=this.node().__on;if(l){for(var c,d=0,o=l.length;d<o;++d)for(a=0,c=l[d];a<i;++a)if((r=n[a]).type===c.type&&r.name===c.name)return c.value}return}for(a=0,l=t?eJ:eK;a<i;++a)this.each(l(n[a],t,s));return this},dispatch:function(e,t){return this.each(("function"==typeof t?function(e,t){return function(){return eQ(this,e,t.apply(this,arguments))}}:function(e,t){return function(){return eQ(this,e,t)}})(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,s=e.length;t<s;++t)for(var a,r=e[t],n=0,i=r.length;n<i;++n)(a=r[n])&&(yield a)}};var e6={value:()=>{}};function e4(){for(var e,t=0,s=arguments.length,a={};t<s;++t){if(!(e=arguments[t]+"")||e in a||/[\s.]/.test(e))throw Error("illegal type: "+e);a[e]=[]}return new e3(a)}function e3(e){this._=e}function e8(e,t,s){for(var a=0,r=e.length;a<r;++a)if(e[a].name===t){e[a]=e6,e=e.slice(0,a).concat(e.slice(a+1));break}return null!=s&&e.push({name:t,value:s}),e}e3.prototype=e4.prototype={constructor:e3,on:function(e,t){var s,a=this._,r=(e+"").trim().split(/^|\s+/).map(function(e){var t="",s=e.indexOf(".");if(s>=0&&(t=e.slice(s+1),e=e.slice(0,s)),e&&!a.hasOwnProperty(e))throw Error("unknown type: "+e);return{type:e,name:t}}),n=-1,i=r.length;if(arguments.length<2){for(;++n<i;)if((s=(e=r[n]).type)&&(s=function(e,t){for(var s,a=0,r=e.length;a<r;++a)if((s=e[a]).name===t)return s.value}(a[s],e.name)))return s;return}if(null!=t&&"function"!=typeof t)throw Error("invalid callback: "+t);for(;++n<i;)if(s=(e=r[n]).type)a[s]=e8(a[s],e.name,t);else if(null==t)for(s in a)a[s]=e8(a[s],e.name,null);return this},copy:function(){var e={},t=this._;for(var s in t)e[s]=t[s].slice();return new e3(e)},call:function(e,t){if((s=arguments.length-2)>0)for(var s,a,r=Array(s),n=0;n<s;++n)r[n]=arguments[n+2];if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(a=this._[e],n=0,s=a.length;n<s;++n)a[n].value.apply(t,r)},apply:function(e,t,s){if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(var a=this._[e],r=0,n=a.length;r<n;++r)a[r].value.apply(t,s)}};let e7={capture:!0,passive:!1};function e9(e){e.preventDefault(),e.stopImmediatePropagation()}function te(e){return((e=Math.exp(e))+1/e)/2}let tt=function e(t,s,a){function r(e,r){var n,i,l=e[0],c=e[1],d=e[2],o=r[0],x=r[1],u=r[2],h=o-l,m=x-c,f=h*h+m*m;if(f<1e-12)i=Math.log(u/d)/t,n=function(e){return[l+e*h,c+e*m,d*Math.exp(t*e*i)]};else{var p=Math.sqrt(f),g=(u*u-d*d+a*f)/(2*d*s*p),y=(u*u-d*d-a*f)/(2*u*s*p),j=Math.log(Math.sqrt(g*g+1)-g);i=(Math.log(Math.sqrt(y*y+1)-y)-j)/t,n=function(e){var a,r,n=e*i,o=te(j),x=d/(s*p)*(o*(((a=Math.exp(2*(a=t*n+j)))-1)/(a+1))-((r=Math.exp(r=j))-1/r)/2);return[l+x*h,c+x*m,d*o/te(t*n+j)]}}return n.duration=1e3*i*t/Math.SQRT2,n}return r.rho=function(t){var s=Math.max(.001,+t),a=s*s;return e(s,a,a*a)},r}(Math.SQRT2,2,4);function ts(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var s=t.ownerSVGElement||t;if(s.createSVGPoint){var a=s.createSVGPoint();return a.x=e.clientX,a.y=e.clientY,[(a=a.matrixTransform(t.getScreenCTM().inverse())).x,a.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}var ta,tr,tn=0,ti=0,tl=0,tc=0,td=0,to=0,tx="object"==typeof performance&&performance.now?performance:Date,tu="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function th(){return td||(tu(tm),td=tx.now()+to)}function tm(){td=0}function tf(){this._call=this._time=this._next=null}function tp(e,t,s){var a=new tf;return a.restart(e,t,s),a}function tg(){td=(tc=tx.now())+to,tn=ti=0;try{th(),++tn;for(var e,t=ta;t;)(e=td-t._time)>=0&&t._call.call(void 0,e),t=t._next;--tn}finally{tn=0,function(){for(var e,t,s=ta,a=1/0;s;)s._call?(a>s._time&&(a=s._time),e=s,s=s._next):(t=s._next,s._next=null,s=e?e._next=t:ta=t);tr=e,tj(a)}(),td=0}}function ty(){var e=tx.now(),t=e-tc;t>1e3&&(to-=t,tc=e)}function tj(e){!tn&&(ti&&(ti=clearTimeout(ti)),e-td>24?(e<1/0&&(ti=setTimeout(tg,e-tx.now()-to)),tl&&(tl=clearInterval(tl))):(tl||(tc=tx.now(),tl=setInterval(ty,1e3)),tn=1,tu(tg)))}function tv(e,t,s){var a=new tf;return t=null==t?0:+t,a.restart(s=>{a.stop(),e(s+t)},t,s),a}tf.prototype=tp.prototype={constructor:tf,restart:function(e,t,s){if("function"!=typeof e)throw TypeError("callback is not a function");s=(null==s?th():+s)+(null==t?0:+t),this._next||tr===this||(tr?tr._next=this:ta=this,tr=this),this._call=e,this._time=s,tj()},stop:function(){this._call&&(this._call=null,this._time=1/0,tj())}};var tN=e4("start","end","cancel","interrupt"),tb=[];function tw(e,t,s,a,r,n){var i=e.__transition;if(i){if(s in i)return}else e.__transition={};!function(e,t,s){var a,r=e.__transition;function n(c){var d,o,x,u;if(1!==s.state)return l();for(d in r)if((u=r[d]).name===s.name){if(3===u.state)return tv(n);4===u.state?(u.state=6,u.timer.stop(),u.on.call("interrupt",e,e.__data__,u.index,u.group),delete r[d]):+d<t&&(u.state=6,u.timer.stop(),u.on.call("cancel",e,e.__data__,u.index,u.group),delete r[d])}if(tv(function(){3===s.state&&(s.state=4,s.timer.restart(i,s.delay,s.time),i(c))}),s.state=2,s.on.call("start",e,e.__data__,s.index,s.group),2===s.state){for(d=0,s.state=3,a=Array(x=s.tween.length),o=-1;d<x;++d)(u=s.tween[d].value.call(e,e.__data__,s.index,s.group))&&(a[++o]=u);a.length=o+1}}function i(t){for(var r=t<s.duration?s.ease.call(null,t/s.duration):(s.timer.restart(l),s.state=5,1),n=-1,i=a.length;++n<i;)a[n].call(e,r);5===s.state&&(s.on.call("end",e,e.__data__,s.index,s.group),l())}function l(){for(var a in s.state=6,s.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=s,s.timer=tp(function(e){s.state=1,s.timer.restart(n,s.delay,s.time),s.delay<=e&&n(e-s.delay)},0,s.time)}(e,s,{name:t,index:a,group:r,on:tN,tween:tb,time:n.time,delay:n.delay,duration:n.duration,ease:n.ease,timer:null,state:0})}function tA(e,t){var s=tk(e,t);if(s.state>0)throw Error("too late; already scheduled");return s}function t_(e,t){var s=tk(e,t);if(s.state>3)throw Error("too late; already running");return s}function tk(e,t){var s=e.__transition;if(!s||!(s=s[t]))throw Error("transition not found");return s}function tE(e,t){var s,a,r,n=e.__transition,i=!0;if(n){for(r in t=null==t?null:t+"",n){if((s=n[r]).name!==t){i=!1;continue}a=s.state>2&&s.state<5,s.state=6,s.timer.stop(),s.on.call(a?"interrupt":"cancel",e,e.__data__,s.index,s.group),delete n[r]}i&&delete e.__transition}}function tS(e,t){return e*=1,t*=1,function(s){return e*(1-s)+t*s}}var tC=180/Math.PI,tP={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function tR(e,t,s,a,r,n){var i,l,c;return(i=Math.sqrt(e*e+t*t))&&(e/=i,t/=i),(c=e*s+t*a)&&(s-=e*c,a-=t*c),(l=Math.sqrt(s*s+a*a))&&(s/=l,a/=l,c/=l),e*a<t*s&&(e=-e,t=-t,c=-c,i=-i),{translateX:r,translateY:n,rotate:Math.atan2(t,e)*tC,skewX:Math.atan(c)*tC,scaleX:i,scaleY:l}}function tT(e,t,s,a){function r(e){return e.length?e.pop()+" ":""}return function(n,i){var l,c,d,o,x=[],u=[];return n=e(n),i=e(i),!function(e,a,r,n,i,l){if(e!==r||a!==n){var c=i.push("translate(",null,t,null,s);l.push({i:c-4,x:tS(e,r)},{i:c-2,x:tS(a,n)})}else(r||n)&&i.push("translate("+r+t+n+s)}(n.translateX,n.translateY,i.translateX,i.translateY,x,u),l=n.rotate,c=i.rotate,l!==c?(l-c>180?c+=360:c-l>180&&(l+=360),u.push({i:x.push(r(x)+"rotate(",null,a)-2,x:tS(l,c)})):c&&x.push(r(x)+"rotate("+c+a),d=n.skewX,o=i.skewX,d!==o?u.push({i:x.push(r(x)+"skewX(",null,a)-2,x:tS(d,o)}):o&&x.push(r(x)+"skewX("+o+a),!function(e,t,s,a,n,i){if(e!==s||t!==a){var l=n.push(r(n)+"scale(",null,",",null,")");i.push({i:l-4,x:tS(e,s)},{i:l-2,x:tS(t,a)})}else(1!==s||1!==a)&&n.push(r(n)+"scale("+s+","+a+")")}(n.scaleX,n.scaleY,i.scaleX,i.scaleY,x,u),n=i=null,function(e){for(var t,s=-1,a=u.length;++s<a;)x[(t=u[s]).i]=t.x(e);return x.join("")}}}var tI=tT(function(e){let t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?tP:tR(t.a,t.b,t.c,t.d,t.e,t.f)},"px, ","px)","deg)"),tD=tT(function(e){return null==e?tP:(a||(a=document.createElementNS("http://www.w3.org/2000/svg","g")),a.setAttribute("transform",e),e=a.transform.baseVal.consolidate())?tR((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):tP},", ",")",")");function tM(e,t,s){var a=e._id;return e.each(function(){var e=t_(this,a);(e.value||(e.value={}))[t]=s.apply(this,arguments)}),function(e){return tk(e,a).value[t]}}function tO(e,t,s){e.prototype=t.prototype=s,s.constructor=e}function t$(e,t){var s=Object.create(e.prototype);for(var a in t)s[a]=t[a];return s}function tF(){}var tz="\\s*([+-]?\\d+)\\s*",tU="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",tW="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",tB=/^#([0-9a-f]{3,8})$/,tZ=RegExp(`^rgb\\(${tz},${tz},${tz}\\)$`),tL=RegExp(`^rgb\\(${tW},${tW},${tW}\\)$`),tV=RegExp(`^rgba\\(${tz},${tz},${tz},${tU}\\)$`),tG=RegExp(`^rgba\\(${tW},${tW},${tW},${tU}\\)$`),tH=RegExp(`^hsl\\(${tU},${tW},${tW}\\)$`),tY=RegExp(`^hsla\\(${tU},${tW},${tW},${tU}\\)$`),tq={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function tX(){return this.rgb().formatHex()}function tK(){return this.rgb().formatRgb()}function tJ(e){var t,s;return e=(e+"").trim().toLowerCase(),(t=tB.exec(e))?(s=t[1].length,t=parseInt(t[1],16),6===s?tQ(t):3===s?new t2(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===s?t0(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===s?t0(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=tZ.exec(e))?new t2(t[1],t[2],t[3],1):(t=tL.exec(e))?new t2(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=tV.exec(e))?t0(t[1],t[2],t[3],t[4]):(t=tG.exec(e))?t0(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=tH.exec(e))?t7(t[1],t[2]/100,t[3]/100,1):(t=tY.exec(e))?t7(t[1],t[2]/100,t[3]/100,t[4]):tq.hasOwnProperty(e)?tQ(tq[e]):"transparent"===e?new t2(NaN,NaN,NaN,0):null}function tQ(e){return new t2(e>>16&255,e>>8&255,255&e,1)}function t0(e,t,s,a){return a<=0&&(e=t=s=NaN),new t2(e,t,s,a)}function t1(e,t,s,a){var r;return 1==arguments.length?((r=e)instanceof tF||(r=tJ(r)),r)?new t2((r=r.rgb()).r,r.g,r.b,r.opacity):new t2:new t2(e,t,s,null==a?1:a)}function t2(e,t,s,a){this.r=+e,this.g=+t,this.b=+s,this.opacity=+a}function t5(){return`#${t8(this.r)}${t8(this.g)}${t8(this.b)}`}function t6(){let e=t4(this.opacity);return`${1===e?"rgb(":"rgba("}${t3(this.r)}, ${t3(this.g)}, ${t3(this.b)}${1===e?")":`, ${e})`}`}function t4(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function t3(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function t8(e){return((e=t3(e))<16?"0":"")+e.toString(16)}function t7(e,t,s,a){return a<=0?e=t=s=NaN:s<=0||s>=1?e=t=NaN:t<=0&&(e=NaN),new se(e,t,s,a)}function t9(e){if(e instanceof se)return new se(e.h,e.s,e.l,e.opacity);if(e instanceof tF||(e=tJ(e)),!e)return new se;if(e instanceof se)return e;var t=(e=e.rgb()).r/255,s=e.g/255,a=e.b/255,r=Math.min(t,s,a),n=Math.max(t,s,a),i=NaN,l=n-r,c=(n+r)/2;return l?(i=t===n?(s-a)/l+(s<a)*6:s===n?(a-t)/l+2:(t-s)/l+4,l/=c<.5?n+r:2-n-r,i*=60):l=c>0&&c<1?0:i,new se(i,l,c,e.opacity)}function se(e,t,s,a){this.h=+e,this.s=+t,this.l=+s,this.opacity=+a}function st(e){return(e=(e||0)%360)<0?e+360:e}function ss(e){return Math.max(0,Math.min(1,e||0))}function sa(e,t,s){return(e<60?t+(s-t)*e/60:e<180?s:e<240?t+(s-t)*(240-e)/60:t)*255}function sr(e,t,s,a,r){var n=e*e,i=n*e;return((1-3*e+3*n-i)*t+(4-6*n+3*i)*s+(1+3*e+3*n-3*i)*a+i*r)/6}tO(tF,tJ,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:tX,formatHex:tX,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return t9(this).formatHsl()},formatRgb:tK,toString:tK}),tO(t2,t1,t$(tF,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new t2(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new t2(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new t2(t3(this.r),t3(this.g),t3(this.b),t4(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:t5,formatHex:t5,formatHex8:function(){return`#${t8(this.r)}${t8(this.g)}${t8(this.b)}${t8((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:t6,toString:t6})),tO(se,function(e,t,s,a){return 1==arguments.length?t9(e):new se(e,t,s,null==a?1:a)},t$(tF,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new se(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new se(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,s=this.l,a=s+(s<.5?s:1-s)*t,r=2*s-a;return new t2(sa(e>=240?e-240:e+120,r,a),sa(e,r,a),sa(e<120?e+240:e-120,r,a),this.opacity)},clamp(){return new se(st(this.h),ss(this.s),ss(this.l),t4(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=t4(this.opacity);return`${1===e?"hsl(":"hsla("}${st(this.h)}, ${100*ss(this.s)}%, ${100*ss(this.l)}%${1===e?")":`, ${e})`}`}}));let sn=e=>()=>e;function si(e,t){var s,a,r=t-e;return r?(s=e,a=r,function(e){return s+e*a}):sn(isNaN(e)?t:e)}let sl=function e(t){var s,a=1==(s=+t)?si:function(e,t){var a,r,n;return t-e?(a=e,r=t,a=Math.pow(a,n=s),r=Math.pow(r,n)-a,n=1/n,function(e){return Math.pow(a+e*r,n)}):sn(isNaN(e)?t:e)};function r(e,t){var s=a((e=t1(e)).r,(t=t1(t)).r),r=a(e.g,t.g),n=a(e.b,t.b),i=si(e.opacity,t.opacity);return function(t){return e.r=s(t),e.g=r(t),e.b=n(t),e.opacity=i(t),e+""}}return r.gamma=e,r}(1);function sc(e){return function(t){var s,a,r=t.length,n=Array(r),i=Array(r),l=Array(r);for(s=0;s<r;++s)a=t1(t[s]),n[s]=a.r||0,i[s]=a.g||0,l[s]=a.b||0;return n=e(n),i=e(i),l=e(l),a.opacity=1,function(e){return a.r=n(e),a.g=i(e),a.b=l(e),a+""}}}sc(function(e){var t=e.length-1;return function(s){var a=s<=0?s=0:s>=1?(s=1,t-1):Math.floor(s*t),r=e[a],n=e[a+1],i=a>0?e[a-1]:2*r-n,l=a<t-1?e[a+2]:2*n-r;return sr((s-a/t)*t,i,r,n,l)}}),sc(function(e){var t=e.length;return function(s){var a=Math.floor(((s%=1)<0?++s:s)*t),r=e[(a+t-1)%t],n=e[a%t],i=e[(a+1)%t],l=e[(a+2)%t];return sr((s-a/t)*t,r,n,i,l)}});var sd=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,so=RegExp(sd.source,"g");function sx(e,t){var s;return("number"==typeof t?tS:t instanceof tJ?sl:(s=tJ(t))?(t=s,sl):function(e,t){var s,a,r,n,i,l=sd.lastIndex=so.lastIndex=0,c=-1,d=[],o=[];for(e+="",t+="";(r=sd.exec(e))&&(n=so.exec(t));)(i=n.index)>l&&(i=t.slice(l,i),d[c]?d[c]+=i:d[++c]=i),(r=r[0])===(n=n[0])?d[c]?d[c]+=n:d[++c]=n:(d[++c]=null,o.push({i:c,x:tS(r,n)})),l=so.lastIndex;return l<t.length&&(i=t.slice(l),d[c]?d[c]+=i:d[++c]=i),d.length<2?o[0]?(s=o[0].x,function(e){return s(e)+""}):(a=t,function(){return a}):(t=o.length,function(e){for(var s,a=0;a<t;++a)d[(s=o[a]).i]=s.x(e);return d.join("")})})(e,t)}var su=e2.prototype.constructor;function sh(e){return function(){this.style.removeProperty(e)}}var sm=0;function sf(e,t,s,a){this._groups=e,this._parents=t,this._name=s,this._id=a}var sp=e2.prototype;sf.prototype=(function(e){return e2().transition(e)}).prototype={constructor:sf,select:function(e){var t=this._name,s=this._id;"function"!=typeof e&&(e=eg(e));for(var a=this._groups,r=a.length,n=Array(r),i=0;i<r;++i)for(var l,c,d=a[i],o=d.length,x=n[i]=Array(o),u=0;u<o;++u)(l=d[u])&&(c=e.call(l,l.__data__,u,d))&&("__data__"in l&&(c.__data__=l.__data__),x[u]=c,tw(x[u],t,s,u,x,tk(l,s)));return new sf(n,this._parents,t,s)},selectAll:function(e){var t=this._name,s=this._id;"function"!=typeof e&&(e=ej(e));for(var a=this._groups,r=a.length,n=[],i=[],l=0;l<r;++l)for(var c,d=a[l],o=d.length,x=0;x<o;++x)if(c=d[x]){for(var u,h=e.call(c,c.__data__,x,d),m=tk(c,s),f=0,p=h.length;f<p;++f)(u=h[f])&&tw(u,t,s,f,h,m);n.push(h),i.push(c)}return new sf(n,i,t,s)},selectChild:sp.selectChild,selectChildren:sp.selectChildren,filter:function(e){"function"!=typeof e&&(e=ev(e));for(var t=this._groups,s=t.length,a=Array(s),r=0;r<s;++r)for(var n,i=t[r],l=i.length,c=a[r]=[],d=0;d<l;++d)(n=i[d])&&e.call(n,n.__data__,d,i)&&c.push(n);return new sf(a,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw Error();for(var t=this._groups,s=e._groups,a=t.length,r=s.length,n=Math.min(a,r),i=Array(a),l=0;l<n;++l)for(var c,d=t[l],o=s[l],x=d.length,u=i[l]=Array(x),h=0;h<x;++h)(c=d[h]||o[h])&&(u[h]=c);for(;l<a;++l)i[l]=t[l];return new sf(i,this._parents,this._name,this._id)},selection:function(){return new su(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,s=++sm,a=this._groups,r=a.length,n=0;n<r;++n)for(var i,l=a[n],c=l.length,d=0;d<c;++d)if(i=l[d]){var o=tk(i,t);tw(i,e,s,d,l,{time:o.time+o.delay+o.duration,delay:0,duration:o.duration,ease:o.ease})}return new sf(a,this._parents,e,s)},call:sp.call,nodes:sp.nodes,node:sp.node,size:sp.size,empty:sp.empty,each:sp.each,on:function(e,t){var s,a,r,n,i,l,c=this._id;return arguments.length<2?tk(this.node(),c).on.on(e):this.each((s=c,a=e,r=t,l=(a+"").trim().split(/^|\s+/).every(function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e})?tA:t_,function(){var e=l(this,s),t=e.on;t!==n&&(i=(n=t).copy()).on(a,r),e.on=i}))},attr:function(e,t){var s=eD(e),a="transform"===s?tD:sx;return this.attrTween(e,"function"==typeof t?(s.local?function(e,t,s){var a,r,n;return function(){var i,l,c=s(this);return null==c?void this.removeAttributeNS(e.space,e.local):(i=this.getAttributeNS(e.space,e.local))===(l=c+"")?null:i===a&&l===r?n:(r=l,n=t(a=i,c))}}:function(e,t,s){var a,r,n;return function(){var i,l,c=s(this);return null==c?void this.removeAttribute(e):(i=this.getAttribute(e))===(l=c+"")?null:i===a&&l===r?n:(r=l,n=t(a=i,c))}})(s,a,tM(this,"attr."+e,t)):null==t?(s.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}})(s):(s.local?function(e,t,s){var a,r,n=s+"";return function(){var i=this.getAttributeNS(e.space,e.local);return i===n?null:i===a?r:r=t(a=i,s)}}:function(e,t,s){var a,r,n=s+"";return function(){var i=this.getAttribute(e);return i===n?null:i===a?r:r=t(a=i,s)}})(s,a,t))},attrTween:function(e,t){var s="attr."+e;if(arguments.length<2)return(s=this.tween(s))&&s._value;if(null==t)return this.tween(s,null);if("function"!=typeof t)throw Error();var a=eD(e);return this.tween(s,(a.local?function(e,t){var s,a;function r(){var r=t.apply(this,arguments);return r!==a&&(s=(a=r)&&function(t){this.setAttributeNS(e.space,e.local,r.call(this,t))}),s}return r._value=t,r}:function(e,t){var s,a;function r(){var r=t.apply(this,arguments);return r!==a&&(s=(a=r)&&function(t){this.setAttribute(e,r.call(this,t))}),s}return r._value=t,r})(a,t))},style:function(e,t,s){var a,r,n,i,l,c,d,o,x,u,h,m,f,p,g,y,j,v,N,b,w,A="transform"==(e+="")?tI:sx;return null==t?this.styleTween(e,(a=e,function(){var e=eO(this,a),t=(this.style.removeProperty(a),eO(this,a));return e===t?null:e===r&&t===n?i:i=A(r=e,n=t)})).on("end.style."+e,sh(e)):"function"==typeof t?this.styleTween(e,(l=e,c=tM(this,"style."+e,t),function(){var e=eO(this,l),t=c(this),s=t+"";return null==t&&(this.style.removeProperty(l),s=t=eO(this,l)),e===s?null:e===d&&s===o?x:(o=s,x=A(d=e,t))})).each((u=this._id,j="end."+(y="style."+(h=e)),function(){var e=t_(this,u),t=e.on,s=null==e.value[y]?g||(g=sh(h)):void 0;(t!==m||p!==s)&&(f=(m=t).copy()).on(j,p=s),e.on=f})):this.styleTween(e,(v=e,w=t+"",function(){var e=eO(this,v);return e===w?null:e===N?b:b=A(N=e,t)}),s).on("end.style."+e,null)},styleTween:function(e,t,s){var a="style."+(e+="");if(arguments.length<2)return(a=this.tween(a))&&a._value;if(null==t)return this.tween(a,null);if("function"!=typeof t)throw Error();return this.tween(a,function(e,t,s){var a,r;function n(){var n=t.apply(this,arguments);return n!==r&&(a=(r=n)&&function(t){this.style.setProperty(e,n.call(this,t),s)}),a}return n._value=t,n}(e,t,null==s?"":s))},text:function(e){var t,s;return this.tween("text","function"==typeof e?(t=tM(this,"text",e),function(){var e=t(this);this.textContent=null==e?"":e}):(s=null==e?"":e+"",function(){this.textContent=s}))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw Error();return this.tween(t,function(e){var t,s;function a(){var a=e.apply(this,arguments);return a!==s&&(t=(s=a)&&function(e){this.textContent=a.call(this,e)}),t}return a._value=e,a}(e))},remove:function(){var e;return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var s in this.__transition)if(+s!==e)return;t&&t.removeChild(this)}))},tween:function(e,t){var s=this._id;if(e+="",arguments.length<2){for(var a,r=tk(this.node(),s).tween,n=0,i=r.length;n<i;++n)if((a=r[n]).name===e)return a.value;return null}return this.each((null==t?function(e,t){var s,a;return function(){var r=t_(this,e),n=r.tween;if(n!==s){a=s=n;for(var i=0,l=a.length;i<l;++i)if(a[i].name===t){(a=a.slice()).splice(i,1);break}}r.tween=a}}:function(e,t,s){var a,r;if("function"!=typeof s)throw Error();return function(){var n=t_(this,e),i=n.tween;if(i!==a){r=(a=i).slice();for(var l={name:t,value:s},c=0,d=r.length;c<d;++c)if(r[c].name===t){r[c]=l;break}c===d&&r.push(l)}n.tween=r}})(s,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){tA(this,e).delay=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){tA(this,e).delay=t}})(t,e)):tk(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){t_(this,e).duration=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){t_(this,e).duration=t}})(t,e)):tk(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw Error();return function(){t_(this,e).ease=t}}(t,e)):tk(this.node(),t).ease},easeVarying:function(e){var t;if("function"!=typeof e)throw Error();return this.each((t=this._id,function(){var s=e.apply(this,arguments);if("function"!=typeof s)throw Error();t_(this,t).ease=s}))},end:function(){var e,t,s=this,a=s._id,r=s.size();return new Promise(function(n,i){var l={value:i},c={value:function(){0==--r&&n()}};s.each(function(){var s=t_(this,a),r=s.on;r!==e&&((t=(e=r).copy())._.cancel.push(l),t._.interrupt.push(l),t._.end.push(c)),s.on=t}),0===r&&n()})},[Symbol.iterator]:sp[Symbol.iterator]};var sg={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};e2.prototype.interrupt=function(e){return this.each(function(){tE(this,e)})},e2.prototype.transition=function(e){var t,s;e instanceof sf?(t=e._id,e=e._name):(t=++sm,(s=sg).time=th(),e=null==e?null:e+"");for(var a=this._groups,r=a.length,n=0;n<r;++n)for(var i,l=a[n],c=l.length,d=0;d<c;++d)(i=l[d])&&tw(i,e,t,d,l,s||function(e,t){for(var s;!(s=e.__transition)||!(s=s[t]);)if(!(e=e.parentNode))throw Error(`transition ${t} not found`);return s}(i,t));return new sf(a,this._parents,e,t)};let sy=e=>()=>e;function sj(e,{sourceEvent:t,target:s,transform:a,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:s,enumerable:!0,configurable:!0},transform:{value:a,enumerable:!0,configurable:!0},_:{value:r}})}function sv(e,t,s){this.k=e,this.x=t,this.y=s}sv.prototype={constructor:sv,scale:function(e){return 1===e?this:new sv(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new sv(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var sN=new sv(1,0,0);function sb(e){e.stopImmediatePropagation()}function sw(e){e.preventDefault(),e.stopImmediatePropagation()}function sA(e){return(!e.ctrlKey||"wheel"===e.type)&&!e.button}function s_(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function sk(){return this.__zoom||sN}function sE(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function sS(){return navigator.maxTouchPoints||"ontouchstart"in this}function sC(e,t,s){var a=e.invertX(t[0][0])-s[0][0],r=e.invertX(t[1][0])-s[1][0],n=e.invertY(t[0][1])-s[0][1],i=e.invertY(t[1][1])-s[1][1];return e.translate(r>a?(a+r)/2:Math.min(0,a)||Math.max(0,r),i>n?(n+i)/2:Math.min(0,n)||Math.max(0,i))}sv.prototype;let sP=()=>{let e=(0,n.useRef)(null),[t,s]=(0,n.useState)(null),[a,i]=(0,n.useState)(!0),[l,c]=(0,n.useState)(new Set),[d,o]=(0,n.useState)(!0),x={top:50,right:120,bottom:50,left:120},h=1200-x.left-x.right,m=800-x.bottom-x.top,f=async()=>{try{i(!0);let e=await fetch(`/api/referrals/tree?depth=20&enhanced=true&expanded=${Array.from(l).join(",")}`,{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch binary tree data:",e)}finally{i(!1)}},g=(0,n.useCallback)(e=>ei(e,t=>{let s=l.has(t.user.id);if(!(t===e||s))return null;let a=[];return t.leftChild&&(d||t.leftChild.user.isActive)&&a.push(t.leftChild),t.rightChild&&(d||t.rightChild.user.isActive)&&a.push(t.rightChild),a.length>0?a:null}),[d,l]),j=(0,n.useCallback)(()=>{if(t&&e.current)try{let s=e5(e.current);s.selectAll("*").remove();let a=s.attr("width",h+x.left+x.right).attr("height",m+x.top+x.bottom),r=a.append("g").attr("class","tree-container").attr("transform",`translate(${x.left},${x.top})`),n=(function(){var e,t,s,a=sA,r=s_,n=sC,i=sE,l=sS,c=[0,1/0],d=[[-1/0,-1/0],[1/0,1/0]],o=250,x=tt,u=e4("start","zoom","end"),h=0,m=10;function f(e){e.property("__zoom",sk).on("wheel.zoom",b,{passive:!1}).on("mousedown.zoom",w).on("dblclick.zoom",A).filter(l).on("touchstart.zoom",_).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",E).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function p(e,t){return(t=Math.max(c[0],Math.min(c[1],t)))===e.k?e:new sv(t,e.x,e.y)}function g(e,t,s){var a=t[0]-s[0]*e.k,r=t[1]-s[1]*e.k;return a===e.x&&r===e.y?e:new sv(e.k,a,r)}function y(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function j(e,t,s,a){e.on("start.zoom",function(){v(this,arguments).event(a).start()}).on("interrupt.zoom end.zoom",function(){v(this,arguments).event(a).end()}).tween("zoom",function(){var e=arguments,n=v(this,e).event(a),i=r.apply(this,e),l=null==s?y(i):"function"==typeof s?s.apply(this,e):s,c=Math.max(i[1][0]-i[0][0],i[1][1]-i[0][1]),d=this.__zoom,o="function"==typeof t?t.apply(this,e):t,u=x(d.invert(l).concat(c/d.k),o.invert(l).concat(c/o.k));return function(e){if(1===e)e=o;else{var t=u(e),s=c/t[2];e=new sv(s,l[0]-t[0]*s,l[1]-t[1]*s)}n.zoom(null,e)}})}function v(e,t,s){return!s&&e.__zooming||new N(e,t)}function N(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=r.apply(e,t),this.taps=0}function b(e,...t){if(a.apply(this,arguments)){var s=v(this,t).event(e),r=this.__zoom,l=Math.max(c[0],Math.min(c[1],r.k*Math.pow(2,i.apply(this,arguments)))),o=ts(e);if(s.wheel)(s.mouse[0][0]!==o[0]||s.mouse[0][1]!==o[1])&&(s.mouse[1]=r.invert(s.mouse[0]=o)),clearTimeout(s.wheel);else{if(r.k===l)return;s.mouse=[o,r.invert(o)],tE(this),s.start()}sw(e),s.wheel=setTimeout(function(){s.wheel=null,s.end()},150),s.zoom("mouse",n(g(p(r,l),s.mouse[0],s.mouse[1]),s.extent,d))}}function w(e,...t){if(!s&&a.apply(this,arguments)){var r,i,l,c=e.currentTarget,o=v(this,t,!0).event(e),x=e5(e.view).on("mousemove.zoom",function(e){if(sw(e),!o.moved){var t=e.clientX-m,s=e.clientY-f;o.moved=t*t+s*s>h}o.event(e).zoom("mouse",n(g(o.that.__zoom,o.mouse[0]=ts(e,c),o.mouse[1]),o.extent,d))},!0).on("mouseup.zoom",function(e){var t,s,a,r;x.on("mousemove.zoom mouseup.zoom",null),t=e.view,s=o.moved,a=t.document.documentElement,r=e5(t).on("dragstart.drag",null),s&&(r.on("click.drag",e9,e7),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in a?r.on("selectstart.drag",null):(a.style.MozUserSelect=a.__noselect,delete a.__noselect),sw(e),o.event(e).end()},!0),u=ts(e,c),m=e.clientX,f=e.clientY;i=(r=e.view).document.documentElement,l=e5(r).on("dragstart.drag",e9,e7),"onselectstart"in i?l.on("selectstart.drag",e9,e7):(i.__noselect=i.style.MozUserSelect,i.style.MozUserSelect="none"),sb(e),o.mouse=[u,this.__zoom.invert(u)],tE(this),o.start()}}function A(e,...t){if(a.apply(this,arguments)){var s=this.__zoom,i=ts(e.changedTouches?e.changedTouches[0]:e,this),l=s.invert(i),c=s.k*(e.shiftKey?.5:2),x=n(g(p(s,c),i,l),r.apply(this,t),d);sw(e),o>0?e5(this).transition().duration(o).call(j,x,i,e):e5(this).call(f.transform,x,i,e)}}function _(s,...r){if(a.apply(this,arguments)){var n,i,l,c,d=s.touches,o=d.length,x=v(this,r,s.changedTouches.length===o).event(s);for(sb(s),i=0;i<o;++i)c=[c=ts(l=d[i],this),this.__zoom.invert(c),l.identifier],x.touch0?x.touch1||x.touch0[2]===c[2]||(x.touch1=c,x.taps=0):(x.touch0=c,n=!0,x.taps=1+!!e);e&&(e=clearTimeout(e)),n&&(x.taps<2&&(t=c[0],e=setTimeout(function(){e=null},500)),tE(this),x.start())}}function k(e,...t){if(this.__zooming){var s,a,r,i,l=v(this,t).event(e),c=e.changedTouches,o=c.length;for(sw(e),s=0;s<o;++s)r=ts(a=c[s],this),l.touch0&&l.touch0[2]===a.identifier?l.touch0[0]=r:l.touch1&&l.touch1[2]===a.identifier&&(l.touch1[0]=r);if(a=l.that.__zoom,l.touch1){var x=l.touch0[0],u=l.touch0[1],h=l.touch1[0],m=l.touch1[1],f=(f=h[0]-x[0])*f+(f=h[1]-x[1])*f,y=(y=m[0]-u[0])*y+(y=m[1]-u[1])*y;a=p(a,Math.sqrt(f/y)),r=[(x[0]+h[0])/2,(x[1]+h[1])/2],i=[(u[0]+m[0])/2,(u[1]+m[1])/2]}else{if(!l.touch0)return;r=l.touch0[0],i=l.touch0[1]}l.zoom("touch",n(g(a,r,i),l.extent,d))}}function E(e,...a){if(this.__zooming){var r,n,i=v(this,a).event(e),l=e.changedTouches,c=l.length;for(sb(e),s&&clearTimeout(s),s=setTimeout(function(){s=null},500),r=0;r<c;++r)n=l[r],i.touch0&&i.touch0[2]===n.identifier?delete i.touch0:i.touch1&&i.touch1[2]===n.identifier&&delete i.touch1;if(i.touch1&&!i.touch0&&(i.touch0=i.touch1,delete i.touch1),i.touch0)i.touch0[1]=this.__zoom.invert(i.touch0[0]);else if(i.end(),2===i.taps&&(n=ts(n,this),Math.hypot(t[0]-n[0],t[1]-n[1])<m)){var d=e5(this).on("dblclick.zoom");d&&d.apply(this,arguments)}}}return f.transform=function(e,t,s,a){var r=e.selection?e.selection():e;r.property("__zoom",sk),e!==r?j(e,t,s,a):r.interrupt().each(function(){v(this,arguments).event(a).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()})},f.scaleBy=function(e,t,s,a){f.scaleTo(e,function(){var e=this.__zoom.k,s="function"==typeof t?t.apply(this,arguments):t;return e*s},s,a)},f.scaleTo=function(e,t,s,a){f.transform(e,function(){var e=r.apply(this,arguments),a=this.__zoom,i=null==s?y(e):"function"==typeof s?s.apply(this,arguments):s,l=a.invert(i),c="function"==typeof t?t.apply(this,arguments):t;return n(g(p(a,c),i,l),e,d)},s,a)},f.translateBy=function(e,t,s,a){f.transform(e,function(){return n(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof s?s.apply(this,arguments):s),r.apply(this,arguments),d)},null,a)},f.translateTo=function(e,t,s,a,i){f.transform(e,function(){var e=r.apply(this,arguments),i=this.__zoom,l=null==a?y(e):"function"==typeof a?a.apply(this,arguments):a;return n(sN.translate(l[0],l[1]).scale(i.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof s?-s.apply(this,arguments):-s),e,d)},a,i)},N.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=e5(this.that).datum();u.call(e,this.that,new sj(e,{sourceEvent:this.sourceEvent,target:f,type:e,transform:this.that.__zoom,dispatch:u}),t)}},f.wheelDelta=function(e){return arguments.length?(i="function"==typeof e?e:sy(+e),f):i},f.filter=function(e){return arguments.length?(a="function"==typeof e?e:sy(!!e),f):a},f.touchable=function(e){return arguments.length?(l="function"==typeof e?e:sy(!!e),f):l},f.extent=function(e){return arguments.length?(r="function"==typeof e?e:sy([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),f):r},f.scaleExtent=function(e){return arguments.length?(c[0]=+e[0],c[1]=+e[1],f):[c[0],c[1]]},f.translateExtent=function(e){return arguments.length?(d[0][0]=+e[0][0],d[1][0]=+e[1][0],d[0][1]=+e[0][1],d[1][1]=+e[1][1],f):[[d[0][0],d[0][1]],[d[1][0],d[1][1]]]},f.constrain=function(e){return arguments.length?(n=e,f):n},f.duration=function(e){return arguments.length?(o=+e,f):o},f.interpolate=function(e){return arguments.length?(x=e,f):x},f.on=function(){var e=u.on.apply(u,arguments);return e===u?f:e},f.clickDistance=function(e){return arguments.length?(h=(e*=1)*e,f):Math.sqrt(h)},f.tapDistance=function(e){return arguments.length?(m=+e,f):m},f})().scaleExtent([.1,3]).on("zoom",e=>{r.attr("transform",e.transform)});s.call(n);let i=(function(){var e=eu,t=1,s=1,a=null;function r(r){var c=function(e){for(var t,s,a,r,n,i=new ef(e,0),l=[i];t=l.pop();)if(a=t._.children)for(t.children=Array(n=a.length),r=n-1;r>=0;--r)l.push(s=t.children[r]=new ef(a[r],r)),s.parent=t;return(i.parent=new ef(null,0)).children=[i],i}(r);if(c.eachAfter(n),c.parent.m=-c.z,c.eachBefore(i),a)r.eachBefore(l);else{var d=r,o=r,x=r;r.eachBefore(function(e){e.x<d.x&&(d=e),e.x>o.x&&(o=e),e.depth>x.depth&&(x=e)});var u=d===o?1:e(d,o)/2,h=u-d.x,m=t/(o.x+u+h),f=s/(x.depth||1);r.eachBefore(function(e){e.x=(e.x+h)*m,e.y=e.depth*f})}return r}function n(t){var s=t.children,a=t.parent.children,r=t.i?a[t.i-1]:null;if(s){!function(e){for(var t,s=0,a=0,r=e.children,n=r.length;--n>=0;)t=r[n],t.z+=s,t.m+=s,s+=t.s+(a+=t.c)}(t);var n=(s[0].z+s[s.length-1].z)/2;r?(t.z=r.z+e(t._,r._),t.m=t.z-n):t.z=n}else r&&(t.z=r.z+e(t._,r._));t.parent.A=function(t,s,a){if(s){for(var r,n,i,l=t,c=t,d=s,o=l.parent.children[0],x=l.m,u=c.m,h=d.m,m=o.m;d=em(d),l=eh(l),d&&l;)o=eh(o),(c=em(c)).a=t,(i=d.z+h-l.z-x+e(d._,l._))>0&&(!function(e,t,s){var a=s/(t.i-e.i);t.c-=a,t.s+=s,e.c+=a,t.z+=s,t.m+=s}((r=d,n=a,r.a.parent===t.parent?r.a:n),t,i),x+=i,u+=i),h+=d.m,x+=l.m,m+=o.m,u+=c.m;d&&!em(c)&&(c.t=d,c.m+=h-u),l&&!eh(o)&&(o.t=l,o.m+=x-m,a=t)}return a}(t,r,t.parent.A||a[0])}function i(e){e._.x=e.z+e.parent.m,e.m+=e.parent.m}function l(e){e.x*=t,e.y=e.depth*s}return r.separation=function(t){return arguments.length?(e=t,r):e},r.size=function(e){return arguments.length?(a=!1,t=+e[0],s=+e[1],r):a?null:[t,s]},r.nodeSize=function(e){return arguments.length?(a=!0,t=+e[0],s=+e[1],r):a?[t,s]:null},r})().size([h,m]).nodeSize([250,200]).separation((e,t)=>e.parent===t.parent?2:3),c=g(t.treeStructure),d=i(c),o=d.descendants(),u=d.links(),f=1/0,p=-1/0;o.forEach(e=>{e.x<f&&(f=e.x),e.x>p&&(p=e.x)});let y=h/2-(f+p)/2;o.forEach(e=>{e.x+=y}),r.append("g").attr("class","links").selectAll(".link").data(u).enter().append("path").attr("class","link").attr("d",e=>{let t=e.source,s=e.target;return`M${t.x},${t.y+60}
                C${t.x},${(t.y+s.y)/2}
                 ${s.x},${(t.y+s.y)/2}
                 ${s.x},${s.y-60}`}).style("fill","none").style("stroke","#94a3b8").style("stroke-width","2px").style("stroke-opacity",.6);let j=r.append("g").attr("class","nodes").selectAll(".node").data(o).enter().append("g").attr("class","node").attr("transform",e=>`translate(${e.x-100},${e.y-60})`).style("cursor","pointer");j.append("rect").attr("width",200).attr("height",120).attr("rx",12).attr("ry",12).style("fill","white").style("stroke",e=>e.data.user.isActive?(e.data.hasLeftChild||e.data.hasRightChild)&&!l.has(e.data.user.id)?"#3b82f6":"#10b981":"#6b7280").style("stroke-width",e=>(e.data.hasLeftChild||e.data.hasRightChild)&&!l.has(e.data.user.id)?3:2).style("filter","drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))"),j.append("circle").attr("cx",100).attr("cy",25).attr("r",18).style("fill",e=>e.data.user.isActive?"#10b981":"#6b7280"),j.append("text").attr("x",100).attr("y",25).attr("dy","0.35em").style("text-anchor","middle").style("fill","white").style("font-weight","bold").style("font-size","12px").text(e=>{let t=e.data.user.firstName||"",s=e.data.user.lastName||"";return`${t[0]||""}${s[0]||""}`.toUpperCase()}),j.append("text").attr("x",100).attr("y",55).style("text-anchor","middle").style("font-weight","600").style("font-size","13px").style("fill","#1f2937").text(e=>`${e.data.user.firstName} ${e.data.user.lastName}`),j.append("rect").attr("x",75).attr("y",65).attr("width",50).attr("height",16).attr("rx",8).style("fill",e=>e.data.user.isActive?"#dcfce7":"#f3f4f6").style("stroke",e=>e.data.user.isActive?"#16a34a":"#6b7280").style("stroke-width",1),j.append("text").attr("x",100).attr("y",73).attr("dy","0.35em").style("text-anchor","middle").style("font-size","10px").style("font-weight","500").style("fill",e=>e.data.user.isActive?"#16a34a":"#6b7280").text(e=>e.data.user.isActive?"Active":"Inactive"),j.filter(e=>e.data.sponsorInfo).append("text").attr("x",100).attr("y",90).style("text-anchor","middle").style("font-size","9px").style("fill","#3b82f6").text(e=>`Sponsor: ${e.data.sponsorInfo.firstName} ${e.data.sponsorInfo.lastName}`),j.append("text").attr("x",100).attr("y",105).style("text-anchor","middle").style("font-size","10px").style("font-weight","500").style("fill","#4b5563").text(e=>`Team: ${e.data.teamCounts.total} (L:${e.data.teamCounts.left} R:${e.data.teamCounts.right})`);let N=j.filter(e=>e.data.hasLeftChild||e.data.hasRightChild);N.append("circle").attr("cx",185).attr("cy",15).attr("r",10).style("fill",e=>l.has(e.data.user.id)?"#ef4444":"#3b82f6").style("stroke","white").style("stroke-width",2).style("cursor","pointer").style("transition","all 0.2s ease").style("transform-origin","center").on("click",function(e,t){e.stopPropagation(),v(t.data.user.id)}),N.append("text").attr("x",185).attr("y",15).attr("dy","0.35em").style("text-anchor","middle").style("font-size","12px").style("font-weight","bold").style("fill","white").style("cursor","pointer").style("pointer-events","none").text(e=>l.has(e.data.user.id)?"−":"+");let b=a.append("g").attr("class","zoom-controls").attr("transform",`translate(${h+x.left-100}, 20)`),w=b.append("g").style("cursor","pointer").on("click",()=>{s.transition().duration(300).call(n.scaleBy,1.5)});w.append("rect").attr("width",30).attr("height",30).attr("rx",4).style("fill","white").style("stroke","#d1d5db").style("stroke-width",1),w.append("text").attr("x",15).attr("y",15).attr("dy","0.35em").style("text-anchor","middle").style("font-size","16px").style("font-weight","bold").text("+");let A=b.append("g").attr("transform","translate(0, 35)").style("cursor","pointer").on("click",()=>{s.transition().duration(300).call(n.scaleBy,.67)});A.append("rect").attr("width",30).attr("height",30).attr("rx",4).style("fill","white").style("stroke","#d1d5db").style("stroke-width",1),A.append("text").attr("x",15).attr("y",15).attr("dy","0.35em").style("text-anchor","middle").style("font-size","16px").style("font-weight","bold").text("−");let _=b.append("g").attr("transform","translate(0, 70)").style("cursor","pointer").on("click",()=>{s.transition().duration(500).call(n.transform,sN)});_.append("rect").attr("width",30).attr("height",30).attr("rx",4).style("fill","white").style("stroke","#d1d5db").style("stroke-width",1),_.append("text").attr("x",15).attr("y",15).attr("dy","0.35em").style("text-anchor","middle").style("font-size","12px").style("font-weight","bold").text("⌂")}catch(e){console.error("Error rendering D3 tree:",e)}},[t,l,d,g]),v=(0,n.useCallback)(e=>{c(t=>{let s=new Set(t);return s.has(e)?s.delete(e):s.add(e),s})},[]),N=async e=>{try{await (0,I.lW)(e)}catch(e){console.error("Failed to copy link:",e)}};return((0,n.useEffect)(()=>{f()},[l]),(0,n.useEffect)(()=>{t&&j()},[t,j,l]),a)?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsx)(V.A,{className:"w-8 h-8 animate-spin text-blue-400"})}):t?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Direct Referrals"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:t.statistics.totalDirectReferrals})]}),(0,r.jsx)(y.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Left Side"}),(0,r.jsxs)("p",{className:"text-lg font-bold text-green-600",children:[t.statistics.leftReferrals||0," users"]}),(0,r.jsxs)("p",{className:"text-sm text-green-500",children:[t.statistics.binaryPoints.leftPoints," points"]})]}),(0,r.jsx)(p.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Right Side"}),(0,r.jsxs)("p",{className:"text-lg font-bold text-orange-600",children:[t.statistics.rightReferrals||0," users"]}),(0,r.jsxs)("p",{className:"text-sm text-orange-500",children:[t.statistics.binaryPoints.rightPoints," points"]})]}),(0,r.jsx)(p.A,{className:"h-8 w-8 text-orange-500"})]})})}),(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Binary Points"}),(0,r.jsxs)("p",{className:"text-lg font-bold text-purple-600",children:[t.statistics.binaryPoints.matchedPoints," matched"]}),(0,r.jsxs)("p",{className:"text-sm text-purple-500",children:[Math.min(t.statistics.binaryPoints.leftPoints,t.statistics.binaryPoints.rightPoints)," available"]})]}),(0,r.jsx)(T,{className:"h-8 w-8 text-purple-500"})]})})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(J,{className:"h-5 w-5 text-blue-500"}),(0,r.jsx)("span",{children:"Referral Links"})]})}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"General Referral Link"}),(0,r.jsxs)("div",{className:"flex mt-1",children:[(0,r.jsx)("input",{type:"text",value:t.referralLinks.general,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"}),(0,r.jsx)(u.$n,{size:"sm",onClick:()=>N(t.referralLinks.general),className:"rounded-l-none",children:(0,r.jsx)(Y,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Left Side Link"}),(0,r.jsxs)("div",{className:"flex mt-1",children:[(0,r.jsx)("input",{type:"text",value:t.referralLinks.left,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"}),(0,r.jsx)(u.$n,{size:"sm",onClick:()=>N(t.referralLinks.left),className:"rounded-l-none",children:(0,r.jsx)(Y,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Right Side Link"}),(0,r.jsxs)("div",{className:"flex mt-1",children:[(0,r.jsx)("input",{type:"text",value:t.referralLinks.right,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"}),(0,r.jsx)(u.$n,{size:"sm",onClick:()=>N(t.referralLinks.right),className:"rounded-l-none",children:(0,r.jsx)(Y,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,r.jsx)("div",{className:"p-3 bg-blue-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-xs text-blue-700",children:[(0,r.jsx)("strong",{children:"Enhanced Placement:"})," New users are automatically placed in your weaker leg for optimal network balance. Use specific side links to target placement."]})}),(0,r.jsx)("div",{className:"p-3 bg-green-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-xs text-green-700",children:[(0,r.jsx)("strong",{children:"Binary Matching:"})," Points are matched weekly on Saturdays at 15:00 UTC. Each $100 investment by your downline = 1 binary point. Maximum 100 points per side."]})})]})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Binary Tree Structure"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(u.$n,{size:"sm",variant:"outline",onClick:()=>o(!d),children:[d?(0,r.jsx)(Q.A,{className:"h-4 w-4"}):(0,r.jsx)(ee.A,{className:"h-4 w-4"}),d?"Hide Inactive":"Show Inactive"]}),(0,r.jsxs)(u.$n,{size:"sm",onClick:f,children:[(0,r.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]})}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg overflow-hidden bg-gradient-to-br from-slate-50 to-blue-50",children:(0,r.jsx)("svg",{ref:e,className:"w-full",style:{height:"700px"}})}),(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600 space-y-1",children:[(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Zoom:"})," Use mouse wheel or zoom controls"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Pan:"})," Click and drag to move around"]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Expand/Collapse:"})," Click the + or - button on nodes"]})]})]})]}),(0,r.jsx)(er,{})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(y.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Tree Data"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Unable to load your binary tree structure."})]})};var sR=s(31261),sT=s.n(sR),sI=s(10022);let sD=(0,R.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var sM=function(e){return e.ID_DOCUMENT="ID_DOCUMENT",e.SELFIE="SELFIE",e}({}),sO=function(e){return e.NATIONAL_ID="NATIONAL_ID",e.PASSPORT="PASSPORT",e.DRIVING_LICENSE="DRIVING_LICENSE",e}({}),s$=function(e){return e.FRONT="FRONT",e.BACK="BACK",e}({});let sF=()=>{let{user:e,refreshUser:t}=(0,o.A)(),[s,a]=(0,n.useState)([]),[i,l]=(0,n.useState)(!0),[c,d]=(0,n.useState)(!1),[x,h]=(0,n.useState)(""),[m,f]=(0,n.useState)(!1),[p,g]=(0,n.useState)(null),[y,v]=(0,n.useState)({}),[N,b]=(0,n.useState)("select");(0,n.useEffect)(()=>{w()},[]);let w=async()=>{try{let e=await fetch("/api/kyc/documents",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&a(t.data)}}catch(e){console.error("Failed to fetch KYC documents:",e)}finally{l(!1)}},A=(e,t)=>t.type.startsWith("image/")?t.size>5242880?void h("File size must be less than 5MB"):void(h(""),v(s=>({...s,[e]:t}))):void h("Please upload an image file"),_=e=>{let t=["selfie"];return e===sO.PASSPORT?t.push("id_front"):t.push("id_front","id_back"),t},k=()=>!!p&&_(p).every(e=>y[e]),E=async()=>{if(!p||!k())return void h("Please complete all required uploads");f(!0),h("");try{for(let e of _(p)){let t=y[e];if(!t)continue;let s=new FormData;s.append("file",t),s.append("idType",p),"selfie"===e?s.append("documentType",sM.SELFIE):(s.append("documentType",sM.ID_DOCUMENT),s.append("documentSide","id_front"===e?s$.FRONT:s$.BACK));let a=await fetch("/api/kyc/upload",{method:"POST",credentials:"include",body:s}),r=await a.json();if(!r.success)throw Error(r.error||"Upload failed")}await w(),await t(),v({}),g(null),b("select")}catch(e){h(e.message||"Submission failed")}finally{f(!1)}},C=e=>{switch(e){case"APPROVED":return(0,r.jsx)(B.A,{className:"h-5 w-5 text-eco-500"});case"REJECTED":return(0,r.jsx)(Z.A,{className:"h-5 w-5 text-red-500"});case"PENDING":return(0,r.jsx)(P.A,{className:"h-5 w-5 text-solar-500"});default:return(0,r.jsx)(S.A,{className:"h-5 w-5 text-gray-500"})}},R=e=>{switch(e){case"APPROVED":return"bg-eco-100 text-eco-700";case"REJECTED":return"bg-red-100 text-red-700";case"PENDING":return"bg-solar-100 text-solar-700";default:return"bg-gray-100 text-gray-700"}},T=e=>{switch(e){case sO.NATIONAL_ID:return"National ID";case sO.PASSPORT:return"Passport";case sO.DRIVING_LICENSE:return"Driving License";default:return""}},I=({documentKey:e,title:t,description:s,required:a})=>{let n=`file-${e}`,i=y[e];return(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-dark-900",children:[t," ",a&&(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:s})]}),i&&(0,r.jsx)(B.A,{className:"h-5 w-5 text-eco-500"})]}),i?(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("img",{src:URL.createObjectURL(i),alt:t,className:"w-16 h-16 object-cover rounded-lg"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-dark-900",children:i.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[(i.size/1024/1024).toFixed(2)," MB"]})]}),(0,r.jsx)(u.$n,{variant:"outline",size:"sm",onClick:()=>{v(t=>{let s={...t};return delete s[e],s})},children:"Remove"})]})}):(0,r.jsxs)("div",{children:[(0,r.jsx)("input",{id:n,type:"file",accept:"image/*",onChange:t=>{let s=t.target.files?.[0];s&&A(e,s)},className:"hidden"}),(0,r.jsx)("label",{htmlFor:n,className:"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[(0,r.jsx)(sD,{className:"h-8 w-8 text-gray-400 mb-2"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[(0,r.jsx)("span",{className:"font-semibold",children:"Click to upload"})," or drag and drop"]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG up to 5MB"})]})})]})]})};return i?(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-solar-500"}),(0,r.jsx)("span",{children:"KYC Verification"})]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded-lg"})]})})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-solar-500"}),(0,r.jsx)("span",{children:"KYC Verification Status"})]})}),(0,r.jsxs)(u.Wu,{children:[e?.kycStatus==="APPROVED"?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center text-center space-y-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)(sT(),{src:"/kyc.png",alt:"KYC Verification",width:80,height:80,className:"object-contain"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-lg font-semibold text-dark-900",children:["Status: ",(0,r.jsx)("span",{className:"text-eco-600",children:"APPROVED"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Your identity has been verified. You can now make withdrawals."})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[C(e?.kycStatus||"PENDING"),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-lg font-semibold text-dark-900",children:["Status: ",(0,r.jsx)("span",{className:`${e?.kycStatus==="REJECTED"?"text-red-600":"text-solar-600"}`,children:e?.kycStatus||"PENDING"})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e?.kycStatus==="PENDING"&&"Your documents are being reviewed. This usually takes 1-3 business days.",e?.kycStatus==="REJECTED"&&"Your verification was rejected. Please re-upload your documents."]})]})]}),e?.kycStatus!=="APPROVED"&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Why do we need KYC verification?"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,r.jsx)("li",{children:"• Comply with international financial regulations"}),(0,r.jsx)("li",{children:"• Protect your account from unauthorized access"}),(0,r.jsx)("li",{children:"• Enable secure withdrawals to your wallet"}),(0,r.jsx)("li",{children:"• Prevent fraud and money laundering"})]})]})]})]}),e?.kycStatus!=="APPROVED"&&(0,r.jsxs)(r.Fragment,{children:[s.length>0&&(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"Current Documents"})}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:s.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,r.jsx)("img",{src:e.filePath,alt:`${e.documentType} document`,className:"w-16 h-16 object-cover rounded-lg"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-dark-900",children:e.documentType===sM.SELFIE?"Selfie Photo":`${T(e.idType)} - ${e.documentSide}`}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[C(e.status),(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${R(e.status)}`,children:e.status})]}),e.rejectionReason&&(0,r.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["Rejection reason: ",e.rejectionReason]})]})]},e.id))})})]}),(e?.kycStatus==="REJECTED"||!(s.length>0))&&(0,r.jsxs)(r.Fragment,{children:["select"===N&&(0,r.jsx)(()=>(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(sI.A,{className:"h-5 w-5 text-solar-500"}),(0,r.jsx)("span",{children:"Select ID Document Type"})]})}),(0,r.jsxs)(u.Wu,{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Choose the type of government-issued ID you want to upload for verification."}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Object.values(sO).map(e=>(0,r.jsx)("button",{onClick:()=>{g(e),b("upload")},className:"p-4 border-2 border-gray-200 rounded-lg hover:border-solar-500 hover:bg-solar-50 transition-colors text-left",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(sI.A,{className:"h-6 w-6 text-solar-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-dark-900",children:T(e)}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e===sO.PASSPORT?"Front side only":"Front and back required"})]})]})},e))})]})]}),{}),"upload"===N&&p&&(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{children:["Upload ",T(p)," Documents"]}),(0,r.jsx)(u.$n,{variant:"outline",size:"sm",onClick:()=>{b("select"),g(null),v({})},children:"Change ID Type"})]})}),(0,r.jsxs)(u.Wu,{children:[x&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:x}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(I,{documentKey:"id_front",title:`${T(p)} - Front`,description:"Upload a clear photo of the front side of your ID",required:!0}),p!==sO.PASSPORT&&(0,r.jsx)(I,{documentKey:"id_back",title:`${T(p)} - Back`,description:"Upload a clear photo of the back side of your ID",required:!0}),(0,r.jsx)(I,{documentKey:"selfie",title:"Selfie with ID",description:"Take a selfie holding your ID document next to your face",required:!0})]}),(0,r.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,r.jsx)(u.$n,{variant:"outline",onClick:()=>{b("select"),g(null),v({})},disabled:m,children:"Cancel"}),(0,r.jsx)(u.$n,{onClick:E,disabled:!k()||m,loading:m,children:"Submit KYC Documents"})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-yellow-900 mb-2",children:"Document Requirements:"}),(0,r.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,r.jsx)("li",{children:"• Documents must be clear and readable"}),(0,r.jsx)("li",{children:"• All four corners of the ID must be visible"}),(0,r.jsx)("li",{children:"• No blurred, cropped, or edited images"}),(0,r.jsx)("li",{children:"• Selfie must clearly show your face and the ID"}),(0,r.jsx)("li",{children:"• File formats: JPG, PNG (max 5MB each)"})]})]})]})]})]})]})]})},sz=()=>{let[e,t]=(0,n.useState)([]),[s,a]=(0,n.useState)(!0);ea(),(0,n.useEffect)(()=>{i()},[]);let i=async()=>{try{let e=await fetch("/api/mining-units",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&t(s.data)}}catch(e){console.error("Failed to fetch mining units:",e)}finally{a(!1)}},l=e=>{let t=5*e.investmentAmount;return Math.min((e.miningEarnings+e.referralEarnings+e.binaryEarnings)/t*100,100)},c=e=>e.miningEarnings+e.referralEarnings+e.binaryEarnings,d=e=>Math.max(0,5*e.investmentAmount-c(e)),o=()=>e.filter(e=>"ACTIVE"===e.status).sort((e,t)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime()).map((e,t)=>({unitId:e.id,order:t+1})),x=e=>"ACTIVE"===e?"bg-eco-100 text-eco-700":"bg-gray-100 text-gray-700";return s?(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.NC,{className:"h-5 w-5 text-solar-500"}),(0,r.jsx)("span",{children:"Mining Units"})]})}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"animate-pulse space-y-4",children:Array.from({length:3}).map((e,t)=>(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg"},t))})})]}):(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.NC,{className:"h-5 w-5 text-solar-500"}),(0,r.jsxs)("span",{children:["Mining Units (",e.length,")"]})]})}),(0,r.jsx)(u.Wu,{children:e.length>0?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"hidden md:block overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Order"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Mining Power"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Investment"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Earnings Breakdown"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Progress to 5x"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Remaining"}),(0,r.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Status"})]})}),(0,r.jsx)("tbody",{children:e.map(e=>{let t=o().find(t=>t.unitId===e.id);return(0,r.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"py-4 px-4",children:"ACTIVE"===e.status&&t?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:["#",t.order]}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Next to expire"})]}):(0,r.jsx)("span",{className:"text-gray-400 text-xs",children:"-"})}),(0,r.jsx)("td",{className:"py-4 px-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.NC,{className:"h-4 w-4 text-solar-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:(0,I.jI)(e.thsAmount)}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,I.ZV)(e.dailyROI,2),"% daily"]})]})]})}),(0,r.jsx)("td",{className:"py-4 px-4",children:(0,r.jsx)("span",{className:"font-medium",children:(0,I.vv)(e.investmentAmount)})}),(0,r.jsx)("td",{className:"py-4 px-4",children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Mining:"}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:(0,I.vv)(e.miningEarnings)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Referral:"}),(0,r.jsx)("span",{className:"font-medium text-blue-600",children:(0,I.vv)(e.referralEarnings)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Binary:"}),(0,r.jsx)("span",{className:"font-medium text-purple-600",children:(0,I.vv)(e.binaryEarnings)})]}),(0,r.jsxs)("div",{className:"border-t pt-1 flex justify-between text-sm font-medium",children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsx)("span",{className:"text-eco-600",children:(0,I.vv)(c(e))})]})]})}),(0,r.jsxs)("td",{className:"py-4 px-4",children:[(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,r.jsx)("div",{className:"bg-eco-500 h-3 rounded-full transition-all duration-300",style:{width:`${l(e)}%`}})}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1 flex justify-between",children:[(0,r.jsxs)("span",{children:[(0,I.ZV)(l(e),1),"%"]}),(0,r.jsxs)("span",{children:[(0,I.vv)(5*e.investmentAmount)," max"]})]})]}),(0,r.jsx)("td",{className:"py-4 px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("span",{className:"font-medium text-gray-700",children:(0,I.vv)(d(e))}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"remaining"})]})}),(0,r.jsx)("td",{className:"py-4 px-4",children:(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${x(e.status)}`,children:e.status})})]},e.id)})})]})}),(0,r.jsx)("div",{className:"md:hidden space-y-4",children:e.map(e=>{let t=o().find(t=>t.unitId===e.id);return(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.NC,{className:"h-5 w-5 text-solar-500"}),(0,r.jsx)("span",{className:"font-semibold",children:(0,I.jI)(e.thsAmount)}),"ACTIVE"===e.status&&t&&(0,r.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:["#",t.order]})]}),(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${x(e.status)}`,children:e.status})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Investment"}),(0,r.jsx)("div",{className:"font-medium",children:(0,I.vv)(e.investmentAmount)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Daily ROI"}),(0,r.jsxs)("div",{className:"font-medium text-eco-600",children:[(0,I.ZV)(e.dailyROI,2),"%"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Remaining"}),(0,r.jsx)("div",{className:"font-medium text-orange-600",children:(0,I.vv)(d(e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Max Earnings"}),(0,r.jsx)("div",{className:"font-medium",children:(0,I.vv)(5*e.investmentAmount)})]})]}),(0,r.jsxs)("div",{className:"mb-3 p-3 bg-white rounded-lg",children:[(0,r.jsx)("div",{className:"text-xs font-medium text-gray-700 mb-2",children:"Earnings Breakdown"}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Mining:"}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:(0,I.vv)(e.miningEarnings)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Referral:"}),(0,r.jsx)("span",{className:"font-medium text-blue-600",children:(0,I.vv)(e.referralEarnings)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Binary:"}),(0,r.jsx)("span",{className:"font-medium text-purple-600",children:(0,I.vv)(e.binaryEarnings)})]}),(0,r.jsxs)("div",{className:"border-t pt-1 flex justify-between text-sm font-medium",children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsx)("span",{className:"text-eco-600",children:(0,I.vv)(c(e))})]})]})]}),(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,r.jsx)("span",{children:"Progress to 5x"}),(0,r.jsxs)("span",{children:[(0,I.ZV)(l(e),1),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,r.jsx)("div",{className:"bg-eco-500 h-3 rounded-full transition-all duration-300",style:{width:`${l(e)}%`}})})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Started: ",(0,I.r6)(e.startDate)," • Expires: ",(0,I.r6)(e.expiryDate)]})]},e.id)})}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-solar-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-dark-900 mb-3",children:"Mining Units Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Mining Power: "}),(0,r.jsx)("span",{className:"font-medium",children:(0,I.jI)(e.reduce((e,t)=>e+t.thsAmount,0))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Investment: "}),(0,r.jsx)("span",{className:"font-medium",children:(0,I.vv)(e.reduce((e,t)=>e+t.investmentAmount,0))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Active Units: "}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:e.filter(e=>"ACTIVE"===e.status).length})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Expired Units: "}),(0,r.jsx)("span",{className:"font-medium text-red-600",children:e.filter(e=>"EXPIRED"===e.status).length})]})]}),(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,r.jsx)("h5",{className:"font-medium text-dark-900 mb-2",children:"Total Earnings Breakdown"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Mining Earnings: "}),(0,r.jsx)("span",{className:"font-medium text-green-600",children:(0,I.vv)(e.reduce((e,t)=>e+t.miningEarnings,0))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Referral Earnings: "}),(0,r.jsx)("span",{className:"font-medium text-blue-600",children:(0,I.vv)(e.reduce((e,t)=>e+t.referralEarnings,0))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Binary Earnings: "}),(0,r.jsx)("span",{className:"font-medium text-purple-600",children:(0,I.vv)(e.reduce((e,t)=>e+t.binaryEarnings,0))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Total Earned: "}),(0,r.jsx)("span",{className:"font-medium text-eco-600",children:(0,I.vv)(e.reduce((e,t)=>e+c(t),0))})]})]})]}),e.filter(e=>"ACTIVE"===e.status).length>1&&(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,r.jsx)("h5",{className:"font-medium text-dark-900 mb-2",children:"FIFO Expiration Order"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:"Mining units expire when they reach 5x their investment amount. Units expire in First-In-First-Out (FIFO) order."}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Next to expire: "}),(0,r.jsxs)("span",{className:"font-medium text-orange-600",children:["Unit #",o()[0]?.order||"N/A","(",(0,I.ZV)(l(e.find(e=>e.id===o()[0]?.unitId)||e[0]),1),"% complete)"]})]})]})]})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(h.NC,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Mining Units"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"You haven't purchased any mining units yet. Start mining to earn daily returns!"})]})})]})};var sU=s(96474),sW=s(27900);let sB=()=>{let[e,t]=(0,n.useState)([]),[s,a]=(0,n.useState)(!0),[i,l]=(0,n.useState)(!1),[c,d]=(0,n.useState)(null),[o,x]=(0,n.useState)({subject:"",message:"",priority:"MEDIUM"}),[h,m]=(0,n.useState)(""),[f,p]=(0,n.useState)(!1);(0,n.useEffect)(()=>{g()},[]);let g=async()=>{try{let e=await fetch("/api/support/tickets",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&t(s.data)}}catch(e){console.error("Failed to fetch tickets:",e)}finally{a(!1)}},y=async e=>{e.preventDefault(),p(!0);try{(await fetch("/api/support/tickets",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(o)})).ok&&(x({subject:"",message:"",priority:"MEDIUM"}),l(!1),g())}catch(e){console.error("Failed to create ticket:",e)}finally{p(!1)}},j=async e=>{if(h.trim())try{if((await fetch(`/api/support/tickets/${e}/responses`,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({message:h})})).ok){m(""),g();let t=await fetch("/api/support/tickets",{credentials:"include"});if(t.ok){let s=(await t.json()).data.find(t=>t.id===e);s&&d(s)}}}catch(e){console.error("Failed to add response:",e)}},N=e=>{switch(e){case"OPEN":return(0,r.jsx)(S.A,{className:"h-4 w-4 text-red-500"});case"IN_PROGRESS":return(0,r.jsx)(P.A,{className:"h-4 w-4 text-solar-500"});case"RESOLVED":case"CLOSED":return(0,r.jsx)(B.A,{className:"h-4 w-4 text-eco-500"});default:return(0,r.jsx)(v.A,{className:"h-4 w-4 text-gray-500"})}},b=e=>{switch(e){case"OPEN":return"bg-red-100 text-red-700";case"IN_PROGRESS":return"bg-solar-100 text-solar-700";case"RESOLVED":case"CLOSED":return"bg-eco-100 text-eco-700";default:return"bg-gray-100 text-gray-700"}},w=e=>{switch(e){case"URGENT":return"bg-red-100 text-red-700";case"HIGH":return"bg-orange-100 text-orange-700";case"MEDIUM":return"bg-solar-100 text-solar-700";default:return"bg-gray-100 text-gray-700"}};return s?(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})}):(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Support Center"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Get help and manage support tickets"})]}),(0,r.jsxs)(u.$n,{onClick:()=>l(!0),className:"flex items-center gap-2",children:[(0,r.jsx)(sU.A,{className:"h-4 w-4"}),"New Ticket"]})]}),(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-5 w-5"}),"Support Tickets"]})}),(0,r.jsx)(u.Wu,{children:e.length>0?(0,r.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,r.jsxs)("div",{onClick:()=>d(e),className:"p-4 border border-gray-200 rounded-lg cursor-pointer",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 truncate flex-1",children:e.subject}),(0,r.jsxs)("div",{className:"flex items-center gap-2 ml-2",children:[N(e.status),(0,r.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${b(e.status)}`,children:e.status})]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2 line-clamp-2",children:e.message}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsx)("span",{className:`px-2 py-1 rounded-full ${w(e.priority)}`,children:e.priority}),(0,r.jsx)("span",{children:(0,I.r6)(e.createdAt)})]})]},e.id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(v.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Support Tickets"}),(0,r.jsx)("p",{className:"text-gray-600",children:"You haven't created any support tickets yet."})]})})]})}),(0,r.jsx)(u.aF,{isOpen:i,onClose:()=>l(!1),title:"Create Support Ticket",children:(0,r.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,r.jsx)(u.pd,{label:"Subject",value:o.subject,onChange:e=>x(t=>({...t,subject:e.target.value})),placeholder:"Brief description of your issue",required:!0}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,r.jsxs)("select",{value:o.priority,onChange:e=>x(t=>({...t,priority:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"LOW",children:"Low"}),(0,r.jsx)("option",{value:"MEDIUM",children:"Medium"}),(0,r.jsx)("option",{value:"HIGH",children:"High"}),(0,r.jsx)("option",{value:"URGENT",children:"Urgent"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),(0,r.jsx)("textarea",{value:o.message,onChange:e=>x(t=>({...t,message:e.target.value})),placeholder:"Describe your issue in detail...",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent",required:!0})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(u.$n,{type:"button",variant:"outline",onClick:()=>l(!1),className:"flex-1",children:"Cancel"}),(0,r.jsx)(u.$n,{type:"submit",loading:f,className:"flex-1",children:"Create Ticket"})]})]})}),c&&(0,r.jsx)(u.aF,{isOpen:!!c,onClose:()=>d(null),title:`Ticket: ${c.subject}`,size:"xl",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[N(c.status),(0,r.jsx)("span",{className:`text-sm px-2 py-1 rounded-full ${b(c.status)}`,children:c.status}),(0,r.jsx)("span",{className:`text-sm px-2 py-1 rounded-full ${w(c.priority)}`,children:c.priority})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Original Message:"}),(0,r.jsx)("p",{className:"text-gray-900",children:c.message}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Created: ",(0,I.r6)(c.createdAt)]})]}),c.responses.length>0&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"Responses:"}),c.responses.map(e=>(0,r.jsxs)("div",{className:`p-3 rounded-lg ${e.isAdmin?"bg-blue-50 border-l-4 border-blue-500":"bg-gray-50"}`,children:[(0,r.jsx)("p",{className:"text-gray-900",children:e.message}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[e.isAdmin?"Support Team":"You"," • ",(0,I.r6)(e.createdAt)]})]},e.id))]}),"CLOSED"!==c.status&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:h,onChange:e=>m(e.target.value),placeholder:"Add a response...",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"}),(0,r.jsxs)(u.$n,{onClick:()=>j(c.id),disabled:!h.trim(),className:"w-full",children:[(0,r.jsx)(sW.A,{className:"h-4 w-4 mr-2"}),"Send Response"]})]})]})})]})};var sZ=s(85778),sL=s(8819);let sV=(0,R.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);var sG=s(41550);let sH=(0,R.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),sY=()=>{let{user:e}=(0,o.A)(),[t,s]=(0,n.useState)("profile"),[a,i]=(0,n.useState)({emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,marketingEmails:!1}),[l,c]=(0,n.useState)({firstName:"",lastName:"",email:"",referralId:""}),[d,x]=(0,n.useState)(!0),[h,m]=(0,n.useState)(!1),[f,p]=(0,n.useState)(!1),[g,y]=(0,n.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[v,b]=(0,n.useState)(""),{showMessage:w,MessageBoxComponent:A}=(0,u.eC)();(0,n.useEffect)(()=>{e&&c({firstName:e.firstName||"",lastName:e.lastName||"",email:e.email||"",referralId:e.referralId||""}),k(),S()},[e]);let k=async()=>{try{let e=await fetch("/api/user/notification-settings",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&i(t.data)}}catch(e){console.error("Failed to fetch notification settings:",e)}finally{x(!1)}},S=async()=>{try{let e=await fetch("/api/user/withdrawal-address",{credentials:"include"}),t=await e.json();t.success&&b(t.data.withdrawalAddress||"")}catch(e){console.error("Failed to fetch user data:",e)}},C=async e=>{try{m(!0),(await fetch("/api/user/notification-settings",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)})).ok&&i(e)}catch(e){console.error("Failed to update notification settings:",e)}finally{m(!1)}},P=async()=>{try{m(!0),console.log("Profile update:",l)}catch(e){console.error("Failed to update profile:",e)}finally{m(!1)}},R=async()=>{if(g.newPassword!==g.confirmPassword)return void w({title:"Password Mismatch",message:"New passwords do not match",variant:"error"});try{m(!0),console.log("Password update"),y({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){console.error("Failed to update password:",e)}finally{m(!1)}};if(d)return(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"})});let T=[{id:"profile",label:"Profile Information",icon:E.A},{id:"security",label:"Security Settings",icon:j.A},{id:"notifications",label:"Notification Settings",icon:_.A},{id:"billing",label:"Billing & Payments",icon:sZ.A}],I=()=>{let t=e?.kycStatus==="PENDING"||e?.kycStatus==="APPROVED";return(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(E.A,{className:"h-5 w-5"}),"Profile Information"]})}),(0,r.jsxs)(u.Wu,{className:"space-y-4",children:[t&&(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 text-blue-600"}),(0,r.jsx)("p",{className:"text-sm text-blue-700 font-medium",children:"Profile names cannot be changed after KYC submission"})]}),(0,r.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:"Your identity has been verified and profile information is now locked for security."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name"}),(0,r.jsx)(u.pd,{type:"text",value:l.firstName,onChange:e=>c(t=>({...t,firstName:e.target.value})),disabled:t,className:t?"bg-gray-100 border-gray-300 text-gray-500":"bg-white border-gray-300 text-gray-900"}),t&&(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Cannot be changed after KYC submission"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name"}),(0,r.jsx)(u.pd,{type:"text",value:l.lastName,onChange:e=>c(t=>({...t,lastName:e.target.value})),disabled:t,className:t?"bg-gray-100 border-gray-300 text-gray-500":"bg-white border-gray-300 text-gray-900"}),t&&(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Cannot be changed after KYC submission"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,r.jsx)(u.pd,{type:"email",value:l.email,disabled:!0,className:"bg-gray-100 border-gray-300 text-gray-500"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Email cannot be changed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Referral ID"}),(0,r.jsx)(u.pd,{type:"text",value:l.referralId,disabled:!0,className:"bg-gray-100 border-gray-300 text-gray-500"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Your unique referral identifier"})]}),(0,r.jsxs)(u.$n,{onClick:P,disabled:h||t,className:"w-full bg-yellow-500 hover:bg-yellow-600 text-white disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)(sL.A,{className:"h-4 w-4 mr-2"}),h?"Saving...":t?"Profile Locked":"Save Profile"]})]})]})},D=()=>(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5"}),"Security Settings"]})}),(0,r.jsxs)(u.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.pd,{type:f?"text":"password",value:g.currentPassword,onChange:e=>y(t=>({...t,currentPassword:e.target.value})),className:"bg-white border-gray-300 text-gray-900 pr-10"}),(0,r.jsx)("button",{type:"button",onClick:()=>p(!f),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:f?(0,r.jsx)(Q.A,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(ee.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,r.jsx)(u.pd,{type:f?"text":"password",value:g.newPassword,onChange:e=>y(t=>({...t,newPassword:e.target.value})),className:"bg-white border-gray-300 text-gray-900"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,r.jsx)(u.pd,{type:f?"text":"password",value:g.confirmPassword,onChange:e=>y(t=>({...t,confirmPassword:e.target.value})),className:"bg-white border-gray-300 text-gray-900"})]}),(0,r.jsxs)(u.$n,{onClick:R,disabled:h||!g.currentPassword||!g.newPassword||!g.confirmPassword,className:"w-full bg-red-500 hover:bg-red-600 text-white",children:[(0,r.jsx)(sV,{className:"h-4 w-4 mr-2"}),h?"Updating...":"Update Password"]})]})]}),M=()=>(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"h-5 w-5"}),"Notification Settings"]})}),(0,r.jsxs)(u.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(sG.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Email Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Receive updates via email"})]})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:a.emailNotifications,onChange:e=>C({...a,emailNotifications:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(_.A,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Push Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Browser notifications"})]})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:a.pushNotifications,onChange:e=>C({...a,pushNotifications:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(sH,{className:"h-5 w-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"SMS Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Text message alerts"})]})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:a.smsNotifications,onChange:e=>C({...a,smsNotifications:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"})]})]})]})]}),O=async()=>{try{if(m(!0),v&&!v.match(/^T[A-Za-z1-9]{33}$/))return void w({title:"Invalid Address",message:'Invalid USDT TRC20 address format. Address must start with "T" and be 34 characters long.',variant:"error"});let e=await fetch("/api/user/withdrawal-address",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({withdrawalAddress:v.trim()})});if(!e.ok){let t=await e.text();throw console.error("Response not OK:",e.status,t),Error(`Server error: ${e.status}`)}let t=await e.json();if(!t.success)throw Error(t.error||"Failed to update withdrawal address");w({title:"Success",message:"Withdrawal address updated successfully!",variant:"success"})}catch(e){console.error("Failed to update withdrawal address:",e),w({title:"Error",message:`Error: ${e.message}`,variant:"error"})}finally{m(!1)}},$=()=>(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(sZ.A,{className:"h-5 w-5"}),"Billing & Payments"]})}),(0,r.jsxs)(u.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Withdrawal Address"}),(0,r.jsx)(u.pd,{type:"text",value:v,onChange:e=>b(e.target.value),placeholder:"Enter your USDT TRC20 address",className:"bg-white border-gray-300 text-gray-900"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"This address will be automatically filled in withdrawal forms. Only USDT TRC20 addresses are supported."})]}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-blue-800 mb-1",children:"Security Notice"}),(0,r.jsx)("p",{className:"text-sm text-blue-700",children:"Your withdrawal address is encrypted and stored securely. You can update it anytime, but make sure to double-check the address as transactions cannot be reversed."})]})]})}),(0,r.jsxs)(u.$n,{onClick:O,disabled:h,className:"w-full bg-yellow-500 hover:bg-yellow-600 text-white",children:[(0,r.jsx)(sL.A,{className:"h-4 w-4 mr-2"}),h?"Saving...":"Save Withdrawal Address"]})]})]});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Profile & Settings"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage your account information and preferences"})]}),(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:T.map(e=>{let a=e.icon,n=t===e.id;return(0,r.jsxs)("button",{onClick:()=>s(e.id),className:`
                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors
                  ${n?"border-yellow-500 text-yellow-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
                `,children:[(0,r.jsx)(a,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.label})]},e.id)})})}),(0,r.jsx)("div",{className:"mt-6",children:(()=>{switch(t){case"profile":default:return I();case"security":return D();case"notifications":return M();case"billing":return $()}})()}),(0,r.jsx)(A,{})]})};function sq(){let[e,t]=(0,n.useState)("overview");return(0,r.jsx)(C,{activeTab:e,onTabChange:t,children:(()=>{switch(e){case"overview":return(0,r.jsx)(D,{onTabChange:t});case"mining":return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)($,{onPurchaseComplete:()=>window.location.reload()}),(0,r.jsx)(sz,{})]});case"earnings":return(0,r.jsx)(z,{});case"wallet":return(0,r.jsx)(K,{});case"referrals":return(0,r.jsx)(sP,{});case"kyc":return(0,r.jsx)(sF,{});case"support":return(0,r.jsx)(sB,{});case"profile":return(0,r.jsx)(sY,{});default:return(0,r.jsx)(D,{})}})()})}function sX(){return(0,r.jsx)(i.q_,{requireAuth:!0,children:(0,r.jsx)(sq,{})})}},79551:e=>{"use strict";e.exports=require("url")},89513:(e,t,s)=>{"use strict";e.exports=s(94041).vendored.contexts.HeadManagerContext}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,1771,7634,4987,5494,6679],()=>s(8772));module.exports=a})();