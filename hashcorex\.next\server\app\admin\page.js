(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},13964:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20666:(e,s,t)=>{"use strict";t.d(s,{q_:()=>c});var a=t(60687),l=t(43210),r=t(16189),i=t(57445),n=t(76276);let c=({children:e,requireAuth:s=!0,requireAdmin:t=!1,redirectTo:c="/login",fallback:d})=>{let{user:x,loading:o,isInitialized:m}=(0,i.A)(),h=(0,r.useRouter)();return((0,l.useEffect)(()=>{if(m&&!o){if(s&&!x)return void h.push(c);if(t&&x&&"ADMIN"!==x.role)return void h.push("/dashboard")}},[x,o,m,s,t,c,h]),!m||o)?d||(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(n.Rh,{size:"lg",text:"Checking authentication..."})}):s&&!x?d||(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(n.Rh,{size:"lg",text:"Redirecting to login..."})}):t&&x&&"ADMIN"!==x.role?d||(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(n.Rh,{size:"lg",text:"Redirecting to dashboard..."})}):(0,a.jsx)(a.Fragment,{children:e})}},28228:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var a=t(65239),l=t(48088),r=t(88170),i=t.n(r),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\admin\\page.tsx"],o={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39902:e=>{"use strict";e.exports=require("path")},61312:(e,s,t)=>{Promise.resolve().then(t.bind(t,66948))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66948:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ek});var a=t(60687),l=t(43210),r=t(20666),i=t(85814),n=t.n(i),c=t(57445),d=t(36679),x=t(71180),o=t(49625),m=t(41312),h=t(99891),u=t(23928),p=t(85778),j=t(33872),g=t(62688);let N=(0,g.A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var b=t(25541),f=t(41550),v=t(84027),y=t(10022),w=t(11860),A=t(28559),C=t(40083),S=t(12941),E=t(97051),k=t(78272),R=t(58869);let T=({children:e,activeTab:s,onTabChange:t})=>{let{user:r,logout:i}=(0,c.A)(),[g,T]=(0,l.useState)(!1),[P,D]=(0,l.useState)(!1),M=(0,l.useRef)(null),I=[{id:"dashboard",label:"Dashboard",icon:o.A},{id:"users",label:"User Management",icon:m.A},{id:"kyc",label:"KYC Review",icon:h.A},{id:"deposits",label:"Deposits",icon:u.A},{id:"withdrawals",label:"Withdrawals",icon:p.A},{id:"support",label:"Support Tickets",icon:j.A},{id:"binary-points",label:"Binary Points",icon:N},{id:"referral-commissions",label:"Referral Commissions",icon:b.A},{id:"email-settings",label:"Email Settings",icon:f.A},{id:"settings",label:"System Settings",icon:v.A},{id:"logs",label:"System Logs",icon:y.A}],U=async()=>{await i()};return(0,l.useEffect)(()=>{let e=e=>{M.current&&!M.current.contains(e.target)&&D(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.jsxs)("div",{className:"min-h-screen bg-slate-900 flex",children:[g&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-75 lg:hidden",onClick:()=>T(!1)}),(0,a.jsx)("aside",{className:`
        fixed inset-y-0 left-0 z-50 w-64 bg-slate-800 shadow-xl border-r border-slate-700
        transform transition-all duration-300 ease-in-out
        ${g?"translate-x-0":"-translate-x-full lg:translate-x-0"}
      `,children:(0,a.jsxs)("div",{className:"flex flex-col h-screen",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-14 px-5 border-b border-slate-700 bg-slate-800 flex-shrink-0",children:[(0,a.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(x.MX,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-lg font-bold text-white",children:"HashCoreX"})]}),(0,a.jsx)("button",{onClick:()=>T(!1),className:"lg:hidden p-1.5 rounded-lg text-slate-400",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"px-3 py-3 bg-red-600 border-b border-slate-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-white",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:"Admin Panel"})]})}),(0,a.jsx)("nav",{className:"flex-1 px-3 py-4 space-y-1 min-h-0",children:I.map(e=>{let l=e.icon,r=s===e.id;return(0,a.jsxs)("button",{onClick:()=>{t(e.id),T(!1)},className:`
                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left group
                    ${r?"bg-blue-600 text-white shadow-md":"text-slate-300"}
                  `,children:[(0,a.jsx)(l,{className:`h-4 w-4 ${r?"text-white":"text-slate-400"}`}),(0,a.jsx)("span",{className:"font-medium text-sm",children:e.label})]},e.id)})}),(0,a.jsx)("div",{className:"px-3 py-3 border-t border-slate-700",children:(0,a.jsxs)(n(),{href:"/dashboard",className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group",children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Back to Dashboard"})]})}),(0,a.jsx)("div",{className:"px-3 py-3 border-t border-slate-700 bg-slate-900 flex-shrink-0",children:(0,a.jsxs)("button",{onClick:U,className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-slate-300 group",children:[(0,a.jsx)(C.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-sm",children:"Logout"})]})})]})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col min-w-0 lg:ml-64",children:[(0,a.jsx)("header",{className:"bg-slate-800 shadow-sm border-b border-slate-700 sticky top-0 z-30",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 xl:px-12",children:(0,a.jsxs)(d.so,{justify:"between",align:"center",className:"h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>T(!0),className:"lg:hidden p-2 rounded-lg text-slate-400",children:(0,a.jsx)(S.A,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white capitalize tracking-wide drop-shadow-lg",children:I.find(e=>e.id===s)?.label||"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-sm text-slate-300 hidden sm:block font-medium",children:"Manage platform operations and user activities"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("button",{className:"relative p-2 rounded-lg text-slate-400",children:[(0,a.jsx)(E.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"absolute top-1 right-1 h-2 w-2 bg-orange-500 rounded-full"})]}),(0,a.jsx)("div",{className:"px-3 py-1.5 rounded-lg text-xs font-semibold border bg-red-600 text-white border-red-500",children:"ADMIN"}),(0,a.jsxs)("div",{className:"relative",ref:M,children:[(0,a.jsxs)("button",{onClick:()=>D(!P),className:"flex items-center space-x-2 p-1 rounded-lg",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-semibold text-sm",children:r?.firstName?.charAt(0).toUpperCase()||r?.email.charAt(0).toUpperCase()})}),(0,a.jsx)(k.A,{className:`h-4 w-4 text-slate-400 transition-transform ${P?"rotate-180":""}`})]}),P&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-slate-800 rounded-xl shadow-lg border border-slate-700 py-2 z-50",children:[(0,a.jsx)("div",{className:"px-4 py-3 border-b border-slate-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold",children:r?.firstName?.charAt(0).toUpperCase()||r?.email.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-semibold text-white truncate",children:r?.firstName&&r?.lastName?`${r.firstName} ${r.lastName}`:r?.email.split("@")[0]}),(0,a.jsxs)("p",{className:"text-xs text-slate-400",children:["ID: ",r?.referralId]}),(0,a.jsx)("p",{className:"text-xs text-red-400 font-medium",children:"Administrator"})]})]})}),(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)(n(),{href:"/dashboard",onClick:()=>D(!1),className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-slate-300",children:[(0,a.jsx)(R.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"User Dashboard"})]}),(0,a.jsx)("div",{className:"border-t border-slate-700 my-1"}),(0,a.jsxs)("button",{onClick:()=>{D(!1),U()},className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-400",children:[(0,a.jsx)(C.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Sign Out"})]})]})]})]})]})]})})}),(0,a.jsxs)("main",{className:"flex-1 bg-slate-900 overflow-y-auto relative",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-cover bg-center bg-no-repeat opacity-10 pointer-events-none",style:{backgroundImage:"url(/admin_background.jpg)",backgroundAttachment:"fixed",zIndex:0}}),(0,a.jsx)("div",{className:"relative z-10 px-4 sm:px-6 lg:px-8 xl:px-12 py-6",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:e})})]})]})]})};var P=t(76276),D=t(5336),M=t(45583),I=t(43649),U=t(97840),L=t(4780);let $=({onTabChange:e})=>{let[s,t]=(0,l.useState)(null),[r,i]=(0,l.useState)(!0),[n,c]=(0,l.useState)(!1),{showConfirm:x,ConfirmDialog:o}=(0,P.G_)();(0,l.useEffect)(()=>{j()},[]);let j=async()=>{try{let e=await fetch("/api/admin/stats",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&t(s.data)}}catch(e){console.error("Failed to fetch admin stats:",e)}finally{i(!1)}};return r?(0,a.jsx)("div",{className:"space-y-6",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-slate-700 rounded-xl"})},s))}):s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"User Management"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2,lg:4},gap:6,children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,L.ZV)(s.totalUsers,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Active Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,L.ZV)(s.activeUsers,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(D.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Pending KYC"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-400",children:(0,L.ZV)(s.pendingKYC,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Approved KYC"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,L.ZV)(s.approvedKYC,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Financial Overview"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2,lg:4},gap:6,children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Investments"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,L.vv)(s.totalInvestments)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Earnings Distributed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,L.vv)(s.totalEarningsDistributed)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(b.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Mining Operations"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2},gap:6,children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total TH/s Sold"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-400",children:(0,L.jI)(s.totalTHSSold)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(M.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Active TH/s"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,L.jI)(s.activeTHS)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(M.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Withdrawal Management"}),(0,a.jsxs)(d.xA,{cols:{default:1,md:2},gap:6,children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Pending Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-400",children:(0,L.ZV)(s.pendingWithdrawals,0)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-red-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(I.A,{className:"h-6 w-6 text-white"})})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-400",children:"Total Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:(0,L.vv)(s.totalWithdrawals)})]}),(0,a.jsx)("div",{className:"h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-6 w-6 text-white"})})]})})})]})]}),(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsx)(P.ZB,{className:"text-white",children:"Quick Actions"})}),(0,a.jsx)(P.Wu,{children:(0,a.jsxs)(d.xA,{cols:{default:1,md:3},gap:4,children:[(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg cursor-pointer",onClick:()=>e?.("kyc"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-orange-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Review KYC"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[s.pendingKYC," pending reviews"]})]})]})}),(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg cursor-pointer",onClick:()=>e?.("withdrawals"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 text-red-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Process Withdrawals"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[s.pendingWithdrawals," pending"]})]})]})}),(0,a.jsx)("div",{className:"p-4 border border-slate-600 rounded-lg cursor-pointer",onClick:()=>e?.("users"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:"Manage Users"}),(0,a.jsxs)("p",{className:"text-sm text-slate-400",children:[s.totalUsers," total users"]})]})]})}),(0,a.jsx)("div",{className:`p-4 border border-slate-600 rounded-lg cursor-pointer ${n?"opacity-50 cursor-not-allowed":""}`,onClick:n?void 0:()=>{x({title:"Manual Binary Matching",message:"Are you sure you want to manually trigger binary matching? This will process all users with binary points and distribute earnings.",variant:"warning",confirmText:"Process Matching",darkMode:!0,onConfirm:async()=>{c(!0);try{let e=await fetch("/api/admin/binary-matching/manual",{method:"POST",credentials:"include"}),s=await e.json();if(s.success){let e=s.data.matchingResults||[],t=s.data.usersProcessed||0,a=s.data.totalPayouts||0,l=`Binary matching completed successfully!

`;l+=`📊 SUMMARY:
• Users processed: ${t}
• Total payouts: $${a.toFixed(2)}
• Total matched points: ${e.reduce((e,s)=>e+s.matchedPoints,0).toFixed(2)}

`,e.length>0&&(l+=`💰 DETAILED RESULTS:
`,e.slice(0,10).forEach((e,s)=>{l+=`${s+1}. User ${e.userId.substring(0,8)}...
   • Matched: ${e.matchedPoints.toFixed(2)} points
   • Payout: $${e.payout.toFixed(2)}
   • Remaining: L:${e.remainingLeftPoints.toFixed(2)} | R:${e.remainingRightPoints.toFixed(2)}

`}),e.length>10&&(l+=`... and ${e.length-10} more users

`)),l+=`✅ All earnings have been credited to user wallets.`,x({title:"Binary Matching Results",message:l,variant:"success",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}}),j()}else x({title:"Error",message:`Error: ${s.error}`,variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}catch(e){console.error("Manual binary matching error:",e),x({title:"Error",message:"Failed to process binary matching. Please try again.",variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}finally{c(!1)}}})},children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(U.A,{className:"h-8 w-8 text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-white",children:n?"Processing...":"Manual Binary Matching"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:n?"Please wait...":"Trigger binary matching manually"})]})]})})]})})]}),(0,a.jsx)(o,{})]}):(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-slate-400",children:"Failed to load admin statistics"})})})},F=(0,g.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),O=(0,g.A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),_=(0,g.A)("shield-x",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m14.5 9.5-5 5",key:"17q4r4"}],["path",{d:"m9.5 9.5 5 5",key:"18nt4w"}]]);var W=t(99270),B=t(35583);let Z=(0,g.A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]),H=(0,g.A)("user-x",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var G=t(93508),V=t(96474);let z=(0,g.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var Y=t(13861);let K=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)(""),[x,o]=(0,l.useState)("all"),[p,j]=(0,l.useState)(1),[g,b]=(0,l.useState)(1),[f,v]=(0,l.useState)(null),[y,w]=(0,l.useState)(null),[A,C]=(0,l.useState)("desc"),[S,E]=(0,l.useState)(!1),[R,T]=(0,l.useState)({userId:"",userName:"",userEmail:"",amount:"",type:"CREDIT",reason:"",description:""}),[D,M]=(0,l.useState)(!1),[I,U]=(0,l.useState)(""),[$,K]=(0,l.useState)(""),[q,J]=(0,l.useState)(!1),[X,Q]=(0,l.useState)(null),[ee,es]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=setTimeout(()=>{d(i)},300);return()=>clearTimeout(e)},[i]),(0,l.useEffect)(()=>{c!==i&&j(1)},[c]),(0,l.useEffect)(()=>{el()},[p,c,x,y,A]),(0,l.useEffect)(()=>{let e=e=>{f&&!e.target.closest(".relative")&&v(null)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[f]);let et=e=>{y===e?C("asc"===A?"desc":"asc"):(w(e),C("desc")),j(1)},ea=e=>y!==e?(0,a.jsx)(N,{className:"h-4 w-4 text-slate-400"}):"asc"===A?(0,a.jsx)(F,{className:"h-4 w-4 text-blue-400"}):(0,a.jsx)(k.A,{className:"h-4 w-4 text-blue-400"}),el=async()=>{try{r(!0);let e=new URLSearchParams({page:p.toString(),limit:"20",search:c,status:x});y&&(e.append("sortBy",y),e.append("sortOrder",A));let t=await fetch(`/api/admin/users?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(s(e.data.users),b(e.data.totalPages))}}catch(e){console.error("Failed to fetch users:",e)}finally{r(!1)}},er=async(e,s)=>{try{(await fetch("/api/admin/users/action",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({userId:e,action:s})})).ok&&el()}catch(e){console.error("Failed to perform user action:",e)}},ei=(e,s)=>{T({userId:e.id,userName:`${e.firstName} ${e.lastName}`,userEmail:e.email,amount:"",type:s,reason:"",description:""}),U(""),K(""),E(!0)},en=async()=>{if(!R.amount||!R.reason)return void U("Amount and reason are required");try{M(!0),U(""),K("");let e=await fetch("/api/admin/wallet/adjust",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({userId:R.userId,amount:parseFloat(R.amount),type:R.type,reason:R.reason,description:R.description})}),s=await e.json();s.success?(K(`Wallet ${R.type.toLowerCase()} completed successfully`),setTimeout(()=>{E(!1),el()},2e3)):U(s.error||"Failed to adjust wallet balance")}catch(e){U("An error occurred while adjusting wallet balance")}finally{M(!1)}},ec=async e=>{try{es(!0);let s=await fetch(`/api/admin/users/${e}/details`,{credentials:"include"});if(s.ok){let e=await s.json();e.success&&(Q(e.data),J(!0))}}catch(e){console.error("Failed to fetch user details:",e)}finally{es(!1)}},ed=e=>{switch(e){case"APPROVED":return(0,a.jsx)(O,{className:"h-4 w-4 text-green-400"});case"REJECTED":return(0,a.jsx)(_,{className:"h-4 w-4 text-red-400"});case"PENDING":return(0,a.jsx)(h.A,{className:"h-4 w-4 text-yellow-400"});default:return(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-400"})}},ex=e=>(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e?"bg-blue-600 text-white":"bg-red-600 text-white"}`,children:e?"Active":"Inactive"}),eo=e=>(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"ADMIN"===e?"bg-red-600 text-white":"bg-blue-600 text-white"}`,children:e});return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{placeholder:"Search users by email or name...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:x,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Users"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"}),(0,a.jsx)("option",{value:"pending_kyc",children:"Pending KYC"})]})})]})})}),(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Users (",e.length,")"]})}),(0,a.jsxs)(P.Wu,{children:[(0,a.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-600",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"User"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Role"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"KYC Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white cursor-pointer hover:bg-slate-700 select-none",onClick:()=>et("activeTHS"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["Mining Power",ea("activeTHS")]})}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white cursor-pointer hover:bg-slate-700 select-none",onClick:()=>et("walletBalance"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["Wallet Balance",ea("walletBalance")]})}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white cursor-pointer hover:bg-slate-700 select-none",onClick:()=>et("createdAt"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["Joined",ea("createdAt")]})}),(0,a.jsx)("th",{className:"text-left py-3 px-4 font-medium text-white",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b border-slate-700 hover:bg-slate-700",children:[(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium text-white",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:e.email}),(0,a.jsxs)("div",{className:"text-xs text-slate-500",children:["ID: ",e.referralId]})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:eo(e.role)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[ed(e.kycStatus),(0,a.jsx)("span",{className:"text-sm text-slate-300",children:"NOT_SUBMITTED"===e.kycStatus?"Not Submitted":e.kycStatus})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:ex(e.isActive)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"h-4 w-4 bg-orange-500 rounded-sm flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs font-bold text-white",children:"TH"})}),(0,a.jsxs)("span",{className:"text-sm font-medium text-orange-400",children:[e.activeTHS.toFixed(2)," TH/s"]})]})}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(B.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-400",children:(0,L.vv)(e.walletBalance?.availableBalance||0)})]})}),(0,a.jsx)("td",{className:"py-4 px-4 text-sm text-slate-400",children:(0,L.r6)(e.createdAt)}),(0,a.jsx)("td",{className:"py-4 px-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>v(f===e.id?null:e.id),className:"border-slate-600 text-slate-300 flex items-center gap-2",children:[(0,a.jsx)(Z,{className:"h-4 w-4"}),(0,a.jsx)(k.A,{className:"h-3 w-3"})]}),f===e.id&&(0,a.jsx)("div",{className:"absolute right-0 top-full mt-1 w-48 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-10",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("button",{onClick:()=>{er(e.id,e.isActive?"deactivate":"activate"),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",children:[e.isActive?(0,a.jsx)(H,{className:"h-4 w-4"}):(0,a.jsx)(G.A,{className:"h-4 w-4"}),e.isActive?"Deactivate User":"Activate User"]}),"USER"===e.role&&(0,a.jsxs)("button",{onClick:()=>{er(e.id,"promote"),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Promote to Admin"]}),(0,a.jsx)("div",{className:"border-t border-slate-600 my-1"}),(0,a.jsxs)("button",{onClick:()=>{ei(e,"CREDIT"),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-green-300 hover:bg-slate-700",children:[(0,a.jsx)(V.A,{className:"h-4 w-4"}),"Credit Wallet"]}),(0,a.jsxs)("button",{onClick:()=>{ei(e,"DEBIT"),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-red-300 hover:bg-slate-700",children:[(0,a.jsx)(z,{className:"h-4 w-4"}),"Debit Wallet"]}),(0,a.jsx)("div",{className:"border-t border-slate-600 my-1"}),(0,a.jsxs)("button",{onClick:()=>{ec(e.id),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",disabled:ee,children:[(0,a.jsx)(Y.A,{className:"h-4 w-4"}),ee?"Loading...":"View Details"]})]})})]})})]},e.id))})]})}),(0,a.jsx)("div",{className:"lg:hidden space-y-4",children:e.map(e=>(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-4 border border-slate-600",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-semibold text-sm",children:e.firstName.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium text-white",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:e.email}),(0,a.jsxs)("div",{className:"text-xs text-slate-500",children:["ID: ",e.referralId]})]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(P.$n,{variant:"outline",size:"sm",onClick:()=>v(f===e.id?null:e.id),className:"border-slate-600 text-slate-300",children:(0,a.jsx)(Z,{className:"h-4 w-4"})}),f===e.id&&(0,a.jsx)("div",{className:"absolute right-0 top-full mt-1 w-48 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-10",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("button",{onClick:()=>{er(e.id,e.isActive?"deactivate":"activate"),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",children:[e.isActive?(0,a.jsx)(H,{className:"h-4 w-4"}):(0,a.jsx)(G.A,{className:"h-4 w-4"}),e.isActive?"Deactivate User":"Activate User"]}),"USER"===e.role&&(0,a.jsxs)("button",{onClick:()=>{er(e.id,"promote"),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Promote to Admin"]}),(0,a.jsx)("div",{className:"border-t border-slate-600 my-1"}),(0,a.jsxs)("button",{onClick:()=>{ei(e,"CREDIT"),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-green-300 hover:bg-slate-700",children:[(0,a.jsx)(V.A,{className:"h-4 w-4"}),"Credit Wallet"]}),(0,a.jsxs)("button",{onClick:()=>{ei(e,"DEBIT"),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-red-300 hover:bg-slate-700",children:[(0,a.jsx)(z,{className:"h-4 w-4"}),"Debit Wallet"]}),(0,a.jsx)("div",{className:"border-t border-slate-600 my-1"}),(0,a.jsxs)("button",{onClick:()=>{ec(e.id),v(null)},className:"flex items-center gap-2 w-full px-4 py-2 text-sm text-slate-300 hover:bg-slate-700",disabled:ee,children:[(0,a.jsx)(Y.A,{className:"h-4 w-4"}),ee?"Loading...":"View Details"]})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Role"}),eo(e.role)]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Status"}),ex(e.isActive)]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"KYC Status"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[ed(e.kycStatus),(0,a.jsx)("span",{className:"text-slate-300 text-xs",children:"NOT_SUBMITTED"===e.kycStatus?"Not Submitted":e.kycStatus})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Mining Power"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"h-3 w-3 bg-orange-500 rounded-sm flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs font-bold text-white",children:"T"})}),(0,a.jsxs)("span",{className:"text-orange-400 font-medium text-xs",children:[e.activeTHS.toFixed(2)," TH/s"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Wallet Balance"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(B.A,{className:"h-3 w-3 text-green-400"}),(0,a.jsx)("span",{className:"text-green-400 font-medium text-xs",children:(0,L.vv)(e.walletBalance?.availableBalance||0)})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-slate-400 text-xs mb-1",children:"Joined"}),(0,a.jsx)("span",{className:"text-slate-300 text-xs",children:(0,L.r6)(e.createdAt)})]})]})]},e.id))}),g>1&&(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-between mt-6 gap-4",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:["Page ",p," of ",g]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>j(e=>Math.max(1,e-1)),disabled:1===p,className:"border-slate-600 text-slate-300 hover:bg-slate-700 px-3 py-2",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Previous"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Prev"})]}),(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>j(e=>Math.min(g,e+1)),disabled:p===g,className:"border-slate-600 text-slate-300 hover:bg-slate-700 px-3 py-2",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:"Next"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Next"})]})]})]})]})]}),S&&(0,a.jsx)(P.aF,{isOpen:S,onClose:()=>E(!1),title:`${"CREDIT"===R.type?"Credit":"Debit"} User Wallet`,darkMode:!0,children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-white mb-2",children:"User Information"}),(0,a.jsxs)("p",{className:"text-slate-300 text-sm",children:["Name: ",R.userName]}),(0,a.jsxs)("p",{className:"text-slate-300 text-sm",children:["Email: ",R.userEmail]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Amount (USDT)"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",min:"0",value:R.amount,onChange:e=>T(s=>({...s,amount:e.target.value})),placeholder:"Enter amount",className:"bg-slate-700 border-slate-600 text-white",disabled:D})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Reason *"}),(0,a.jsxs)("select",{value:R.reason,onChange:e=>T(s=>({...s,reason:e.target.value})),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg",disabled:D,children:[(0,a.jsx)("option",{value:"",children:"Select reason"}),(0,a.jsx)("option",{value:"Manual Adjustment",children:"Manual Adjustment"}),(0,a.jsx)("option",{value:"Bonus Payment",children:"Bonus Payment"}),(0,a.jsx)("option",{value:"Refund",children:"Refund"}),(0,a.jsx)("option",{value:"Correction",children:"Balance Correction"}),(0,a.jsx)("option",{value:"Penalty",children:"Penalty"}),(0,a.jsx)("option",{value:"Promotion",children:"Promotional Credit"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Description (Optional)"}),(0,a.jsx)("textarea",{value:R.description,onChange:e=>T(s=>({...s,description:e.target.value})),placeholder:"Additional details about this adjustment...",rows:3,className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg resize-none",disabled:D})]}),I&&(0,a.jsx)("div",{className:"bg-red-900/20 border border-red-500 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-red-400 text-sm",children:I})}),$&&(0,a.jsx)("div",{className:"bg-green-900/20 border border-green-500 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-green-400 text-sm",children:$})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(P.$n,{variant:"outline",onClick:()=>E(!1),disabled:D,className:"border-slate-600 text-slate-300 hover:bg-slate-700",children:"Cancel"}),(0,a.jsx)(P.$n,{onClick:en,disabled:D||!R.amount||!R.reason,className:`${"CREDIT"===R.type?"bg-green-600 hover:bg-green-700":"bg-red-600 hover:bg-red-700"} text-white`,children:D?"Processing...":`${"CREDIT"===R.type?"Credit":"Debit"} Wallet`})]})]})}),q&&X&&(0,a.jsx)(P.aF,{isOpen:q,onClose:()=>J(!1),title:"User Details",darkMode:!0,size:"xl",children:(0,a.jsxs)("div",{className:"space-y-6 max-h-[80vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"Basic Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Full Name"}),(0,a.jsxs)("p",{className:"text-white",children:[X.firstName," ",X.lastName]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Email"}),(0,a.jsx)("p",{className:"text-white",children:X.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Referral ID"}),(0,a.jsx)("p",{className:"text-white font-mono",children:X.referralId})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Role"}),(0,a.jsx)("p",{className:"text-white",children:eo(X.role)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Status"}),(0,a.jsx)("p",{className:"text-white",children:ex(X.isActive)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"KYC Status"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[ed(X.kycStatus),(0,a.jsx)("span",{className:"text-white",children:"NOT_SUBMITTED"===X.kycStatus?"Not Submitted":X.kycStatus})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Joined Date"}),(0,a.jsx)("p",{className:"text-white",children:(0,L.r6)(X.createdAt)})]}),X.referrerId&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Referred By"}),(0,a.jsx)("p",{className:"text-white font-mono",children:X.referrerId})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(B.A,{className:"h-4 w-4"}),"Wallet Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Available Balance"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:(0,L.vv)(X.walletBalance.availableBalance)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Earnings"}),(0,a.jsx)("p",{className:"text-green-400 text-lg font-semibold",children:(0,L.vv)(X.walletBalance.totalEarnings)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Deposits"}),(0,a.jsx)("p",{className:"text-blue-400",children:(0,L.vv)(X.walletBalance.totalDeposits)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Withdrawals"}),(0,a.jsx)("p",{className:"text-red-400",children:(0,L.vv)(X.walletBalance.totalWithdrawals)})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Mining Units"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Units"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.miningUnits.totalUnits})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Investment"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:(0,L.vv)(X.miningUnits.totalInvestment)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total TH/s"}),(0,a.jsxs)("p",{className:"text-blue-400",children:[X.miningUnits.totalTHS.toFixed(2)," TH/s"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Active TH/s"}),(0,a.jsxs)("p",{className:"text-green-400",children:[X.miningUnits.activeTHS.toFixed(2)," TH/s"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Binary Points"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Left Points"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.binaryPoints.leftPoints})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Right Points"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.binaryPoints.rightPoints})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Matched"}),(0,a.jsx)("p",{className:"text-green-400",children:X.binaryPoints.totalMatched})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Last Match Date"}),(0,a.jsx)("p",{className:"text-white",children:X.binaryPoints.lastMatchDate?(0,L.r6)(X.binaryPoints.lastMatchDate):"Never"})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"Referral Statistics"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Direct Referrals"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.referralStats.directReferrals})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Total Team"}),(0,a.jsx)("p",{className:"text-white text-lg font-semibold",children:X.referralStats.totalTeam})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Left Team"}),(0,a.jsx)("p",{className:"text-blue-400",children:X.referralStats.leftTeam})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-xs text-slate-400",children:"Right Team"}),(0,a.jsx)("p",{className:"text-green-400",children:X.referralStats.rightTeam})]})]})]}),X.recentActivity.length>0&&(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-white mb-3 flex items-center gap-2",children:[(0,a.jsx)(Y.A,{className:"h-4 w-4"}),"Recent Activity"]}),(0,a.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:X.recentActivity.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-2 bg-slate-600 rounded",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white text-sm",children:e.description}),(0,a.jsx)("p",{className:"text-slate-400 text-xs",children:(0,L.r6)(e.date)})]}),e.amount&&(0,a.jsx)("p",{className:"text-green-400 font-semibold",children:(0,L.vv)(e.amount)})]},s))})]})]})})]})};var q=t(78122),J=t(40228);let X=(0,g.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var Q=t(13964);let ee=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)([]),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(null),[x,o]=(0,l.useState)(null),[m,u]=(0,l.useState)(null),[p,j]=(0,l.useState)(""),[g,N]=(0,l.useState)(!1),[b,f]=(0,l.useState)(!1),[v,A]=(0,l.useState)(""),[C,S]=(0,l.useState)("ALL");(0,l.useEffect)(()=>{E()},[]),(0,l.useEffect)(()=>{k()},[e,v,C]);let E=async()=>{try{n(!0);let e=await fetch("/api/admin/kyc/all",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch KYC data:",e)}finally{n(!1)}},k=()=>{let s=e;v&&(s=s.filter(e=>e.user.firstName.toLowerCase().includes(v.toLowerCase())||e.user.lastName.toLowerCase().includes(v.toLowerCase())||e.user.email.toLowerCase().includes(v.toLowerCase())||e.user.referralId.toLowerCase().includes(v.toLowerCase()))),"ALL"!==C&&(s=s.filter(e=>e.status===C)),r(s)},T=async(e,s,t)=>{try{N(!0),(await fetch("/api/admin/kyc/review",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({userId:e,action:s.toUpperCase(),rejectionReason:t})})).ok&&(E(),d(null),o(null),u(null),j(""))}catch(e){console.error("Failed to review KYC document:",e)}finally{N(!1)}},D=e=>(0,a.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{PENDING:"bg-yellow-900 text-yellow-300 border border-yellow-700",APPROVED:"bg-green-900 text-green-300 border border-green-700",REJECTED:"bg-red-900 text-red-300 border border-red-700"}[e]}`,children:e});return i?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-end gap-4",children:[(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:E,disabled:i,className:"border-slate-600 text-slate-300",children:[(0,a.jsx)(q.A,{className:`h-4 w-4 mr-2 ${i?"animate-spin":""}`}),"Refresh"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-slate-400",children:[(0,a.jsx)(I.A,{className:"h-4 w-4 text-orange-400"}),t.filter(e=>"PENDING"===e.status).length," pending reviews"]})]}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsxs)(P.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)(P.pd,{placeholder:"Search by name, email, or user ID...",value:v,onChange:e=>A(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsx)("div",{className:"md:w-48",children:(0,a.jsxs)("select",{value:C,onChange:e=>S(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"ALL",children:"All Status"}),(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"APPROVED",children:"Approved"}),(0,a.jsx)("option",{value:"REJECTED",children:"Rejected"})]})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center gap-4 text-sm text-slate-400",children:[(0,a.jsxs)("span",{children:["Showing ",t.length," of ",e.length," users"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[t.filter(e=>"PENDING"===e.status).length," pending"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[t.filter(e=>"APPROVED"===e.status).length," approved"]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[t.filter(e=>"REJECTED"===e.status).length," rejected"]})]})]})}),(0,a.jsx)("div",{className:"grid gap-6",children:0===t.length?(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsxs)(P.Wu,{className:"p-12 text-center",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:0===e.length?"No KYC Submissions":"No Results Found"}),(0,a.jsx)("p",{className:"text-slate-400",children:0===e.length?"No users have submitted KYC documents yet.":"Try adjusting your search or filter criteria."})]})}):t.map(e=>(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(R.A,{className:"h-5 w-5 text-slate-400"}),(0,a.jsxs)("span",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]})]}),D(e.status)]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-400 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Email:"})," ",e.user.email]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"User ID:"})," ",e.user.referralId]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(J.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Submitted:"})," ",(0,L.Yq)(e.submittedAt)]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-slate-400 mb-4",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Documents:"})," ",e.documents.length," uploaded"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4",children:e.documents.map(e=>(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-slate-300",children:"SELFIE"===e.documentType?"Selfie":`${e.idType} - ${e.documentSide}`}),D(e.status)]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>{o(e),f(!0)},className:"border-slate-600 text-slate-300 hover:bg-slate-600 hover:text-white text-xs px-2 py-1",children:[(0,a.jsx)(X,{className:"h-3 w-3 mr-1"}),"View"]})}),e.rejectionReason&&(0,a.jsx)("p",{className:"text-xs text-red-400 mt-2 truncate",title:e.rejectionReason,children:e.rejectionReason})]},e.id))})]}),(0,a.jsx)("div",{className:"flex flex-col gap-2 ml-4",children:"PENDING"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>{d(e),u("approve")},className:"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20",children:[(0,a.jsx)(Q.A,{className:"h-4 w-4 mr-1"}),"Approve"]}),(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>{d(e),u("reject")},className:"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"Reject"]})]})})]})})},e.userId))}),b&&x&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-slate-700",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Document Viewer - ","SELFIE"===x.documentType?"Selfie":`${x.idType} ${x.documentSide}`]}),(0,a.jsx)(P.$n,{variant:"outline",size:"sm",onClick:()=>{f(!1),o(null)},className:"border-slate-600 text-slate-300",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"p-4 max-h-[calc(90vh-120px)] overflow-auto",children:(0,a.jsx)("img",{src:x.documentUrl,alt:"KYC Document",className:"w-full h-auto max-h-[70vh] object-contain rounded-lg"})})]})}),c&&m&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4 text-white",children:"approve"===m?"Approve KYC Submission":"Reject KYC Submission"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("p",{className:"text-slate-400 mb-2",children:["User: ",(0,a.jsxs)("span",{className:"font-medium text-white",children:[c.user.firstName," ",c.user.lastName]})]}),(0,a.jsxs)("p",{className:"text-slate-400 mb-2",children:["Email: ",(0,a.jsx)("span",{className:"font-medium text-white",children:c.user.email})]}),(0,a.jsxs)("p",{className:"text-slate-400",children:["Documents: ",(0,a.jsxs)("span",{className:"font-medium text-white",children:[c.documents.length," uploaded"]})]})]}),"reject"===m&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Rejection Reason *"}),(0,a.jsx)("textarea",{value:p,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",rows:3,placeholder:"Please provide a reason for rejection...",required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(P.$n,{variant:"outline",onClick:()=>{d(null),u(null),j("")},disabled:g,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:"Cancel"}),(0,a.jsx)(P.$n,{onClick:()=>T(c.userId,m,"reject"===m?p:void 0),disabled:g||"reject"===m&&!p.trim(),loading:g,className:"approve"===m?"bg-green-600 hover:bg-green-700 text-white":"bg-red-600 hover:bg-red-700 text-white",children:"approve"===m?"Approve":"Reject"})]})]})})]})};var es=t(48730),et=t(35071),ea=t(80462),el=t(96882);let er=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)({totalDeposits:0,totalAmount:0,pendingDeposits:0}),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(null),[x,o]=(0,l.useState)("ALL"),[m,h]=(0,l.useState)(null),[p,j]=(0,l.useState)(null),g=async()=>{try{n(!0);let e=new URLSearchParams;"ALL"!==x&&e.append("status",x),e.append("limit","50");let t=await fetch(`/api/admin/deposits?${e}`,{credentials:"include"});if(!t.ok)throw Error("Failed to fetch deposits");let a=await t.json();if(a.success)s(a.data.deposits),r(a.data.stats);else throw Error(a.error||"Failed to fetch deposits")}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}};(0,l.useEffect)(()=>{g()},[x]);let N=e=>{switch(e){case"COMPLETED":return(0,a.jsx)(D.A,{className:"w-4 h-4 text-green-400"});case"PENDING":return(0,a.jsx)(es.A,{className:"w-4 h-4 text-yellow-400"});case"FAILED":case"REJECTED":return(0,a.jsx)(et.A,{className:"w-4 h-4 text-red-400"});case"VERIFYING":return(0,a.jsx)(I.A,{className:"w-4 h-4 text-blue-400"});default:return(0,a.jsx)(es.A,{className:"w-4 h-4 text-gray-400"})}},b=e=>{switch(e){case"COMPLETED":return"text-green-400 bg-green-400/10";case"PENDING":return"text-yellow-400 bg-yellow-400/10";case"FAILED":case"REJECTED":return"text-red-400 bg-red-400/10";case"VERIFYING":return"text-blue-400 bg-blue-400/10";default:return"text-gray-400 bg-gray-400/10"}};return i?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)(q.A,{className:"w-8 h-8 animate-spin text-blue-400"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{onClick:g,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[(0,a.jsx)(q.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Refresh"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)(P.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:t.totalDeposits})]}),(0,a.jsx)(u.A,{className:"w-8 h-8 text-green-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Amount"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.vv)(t.totalAmount)})]}),(0,a.jsx)(u.A,{className:"w-8 h-8 text-blue-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Pending Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:t.pendingDeposits})]}),(0,a.jsx)(es.A,{className:"w-8 h-8 text-yellow-400"})]})})})]}),(0,a.jsx)(P.Zp,{className:"bg-gray-800 border-gray-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ea.A,{className:"w-5 h-5 text-gray-400"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm font-medium",children:"Filter by Status:"})]}),(0,a.jsxs)("select",{value:x,onChange:e=>o(e.target.value),className:"w-full sm:w-auto bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"ALL",children:"All Status"}),(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"VERIFYING",children:"Verifying"}),(0,a.jsx)("option",{value:"CONFIRMED",children:"Confirmed"}),(0,a.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,a.jsx)("option",{value:"FAILED",children:"Failed"}),(0,a.jsx)("option",{value:"REJECTED",children:"Rejected"})]})]})})}),(0,a.jsxs)(P.Zp,{className:"bg-gray-800 border-gray-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsx)(P.ZB,{className:"text-white",children:"Recent Deposits"})}),(0,a.jsxs)(P.Wu,{children:[c&&(0,a.jsx)("div",{className:"mb-4 p-4 bg-red-900/20 border border-red-500 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-400",children:c})}),(0,a.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-700",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"User"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Amount"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Transaction ID"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-gray-400 font-medium",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b border-gray-700/50 hover:bg-gray-700/30",children:[(0,a.jsxs)("td",{className:"py-3 px-4",children:[(0,a.jsx)("div",{className:"text-white",children:e.user?`${e.user.firstName} ${e.user.lastName}`:"Unknown"}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:e.user?.email||"No email"})]}),(0,a.jsxs)("td",{className:"py-3 px-4",children:[(0,a.jsxs)("div",{className:"text-white font-medium",children:[(0,L.vv)(e.usdtAmount)," USDT"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:[e.confirmations," confirmations"]})]}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${b(e.status)}`,children:[N(e.status),(0,a.jsx)("span",{children:e.status})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"text-white font-mono text-sm",children:[e.transactionId.slice(0,8),"...",e.transactionId.slice(-8)]})}),(0,a.jsx)("td",{className:"py-3 px-4 text-gray-300",children:(0,L.r6)(e.createdAt)}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("button",{onClick:()=>h(e),className:"flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors",children:[(0,a.jsx)(Y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View"})]})})]},e.id))})]})}),(0,a.jsx)("div",{className:"lg:hidden space-y-4",children:e.map(e=>(0,a.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4 border border-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-white font-medium",children:e.user?`${e.user.firstName} ${e.user.lastName}`:"Unknown"}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:e.user?.email||"No email"})]}),(0,a.jsxs)("div",{className:`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${b(e.status)}`,children:[N(e.status),(0,a.jsx)("span",{children:e.status})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-gray-400 text-xs mb-1",children:"Amount"}),(0,a.jsxs)("div",{className:"text-white font-medium",children:[(0,L.vv)(e.usdtAmount)," USDT"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[e.confirmations," confirmations"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-gray-400 text-xs mb-1",children:"Date"}),(0,a.jsx)("div",{className:"text-gray-300 text-xs",children:(0,L.r6)(e.createdAt)})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("div",{className:"text-gray-400 text-xs mb-1",children:"Transaction ID"}),(0,a.jsxs)("div",{className:"text-white font-mono text-xs bg-gray-800 p-2 rounded border",children:[e.transactionId.slice(0,8),"...",e.transactionId.slice(-8)]})]}),(0,a.jsxs)("button",{onClick:()=>h(e),className:"w-full flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors",children:[(0,a.jsx)(Y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"View Details"})]})]},e.id))}),0===e.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-400",children:"No deposits found"})]})]}),m&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"Deposit Details"}),(0,a.jsx)("button",{onClick:()=>h(null),className:"text-gray-400 hover:text-white",children:(0,a.jsx)(et.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"User"}),(0,a.jsx)("p",{className:"text-white",children:m.user?`${m.user.firstName} ${m.user.lastName}`:"Unknown"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:m.user?.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Amount"}),(0,a.jsxs)("p",{className:"text-white font-medium",children:[(0,L.vv)(m.usdtAmount)," USDT"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Status"}),(0,a.jsxs)("div",{className:`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${b(m.status)}`,children:[N(m.status),(0,a.jsx)("span",{children:m.status})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Confirmations"}),(0,a.jsx)("p",{className:"text-white",children:m.confirmations})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Transaction ID"}),(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all",children:m.transactionId})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Sender Address"}),(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all",children:m.senderAddress||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Deposit Address"}),(0,a.jsx)("p",{className:"text-white font-mono text-sm break-all",children:m.tronAddress})]})]}),m.failureReason&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Failure Reason"}),(0,a.jsx)("p",{className:"text-red-400",children:m.failureReason})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Created At"}),(0,a.jsx)("p",{className:"text-white",children:(0,L.r6)(m.createdAt)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-400 text-sm",children:"Processed At"}),(0,a.jsx)("p",{className:"text-white",children:m.processedAt?(0,L.r6)(m.processedAt):"Not processed"})]})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-700",children:(0,a.jsx)("div",{className:"bg-blue-900/50 border border-blue-700 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(el.A,{className:"w-5 h-5 text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-blue-300 font-medium",children:"Automated Processing"}),(0,a.jsx)("p",{className:"text-blue-200 text-sm mt-1",children:"Deposits are now processed automatically. The system verifies transactions and credits wallets once confirmations are met."})]})]})})})]})})]})},ei=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)("all"),[x,o]=(0,l.useState)(null),[m,h]=(0,l.useState)(null),[j,g]=(0,l.useState)(""),[N,b]=(0,l.useState)(""),[f,v]=(0,l.useState)(!1);(0,l.useEffect)(()=>{y()},[i,c]);let y=async()=>{try{r(!0);let e=new URLSearchParams({search:i,status:c}),t=await fetch(`/api/admin/withdrawals?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&s(e.data)}}catch(e){console.error("Failed to fetch withdrawals:",e)}finally{r(!1)}},A=async(e,s,t)=>{try{v(!0),(await fetch("/api/admin/withdrawals/action",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({withdrawalId:e,action:s.toUpperCase(),...t})})).ok&&(y(),o(null),h(null),g(""),b(""))}catch(e){console.error("Failed to process withdrawal action:",e)}finally{v(!1)}},C=e=>{let s={PENDING:{color:"bg-yellow-900 text-yellow-300 border border-yellow-700",icon:es.A},APPROVED:{color:"bg-blue-900 text-blue-300 border border-blue-700",icon:D.A},REJECTED:{color:"bg-red-900 text-red-300 border border-red-700",icon:et.A},COMPLETED:{color:"bg-green-900 text-green-300 border border-green-700",icon:D.A}}[e],t=s.icon;return(0,a.jsxs)("span",{className:`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${s.color}`,children:[(0,a.jsx)(t,{className:"h-3 w-3"}),e]})};return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm text-slate-400",children:"Pending Amount"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:(0,L.vv)(e.filter(e=>"PENDING"===e.status).reduce((e,s)=>e+s.amount,0))})]})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{placeholder:"Search by user email or wallet address...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Withdrawals"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"approved",children:"Approved"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"rejected",children:"Rejected"})]})})]})})}),(0,a.jsx)("div",{className:"grid gap-4",children:0===e.length?(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsxs)(P.Wu,{className:"p-12 text-center",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Withdrawal Requests"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No withdrawal requests match your current filters."})]})}):e.map(e=>(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(R.A,{className:"h-5 w-5 text-slate-400"}),(0,a.jsxs)("span",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]})]}),C(e.status)]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-slate-400 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Email:"})," ",e.user.email]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"User ID:"})," ",e.user.referralId]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Amount:"})," ",(0,L.vv)(e.amount)]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(J.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Requested:"})," ",(0,L.r6)(e.requestedAt)]})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-400 mb-4",children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Wallet Address:"}),(0,a.jsx)("div",{className:"font-mono text-xs bg-slate-700 border border-slate-600 p-2 rounded mt-1 break-all text-slate-300",children:e.walletAddress})]}),e.transactionHash&&(0,a.jsxs)("div",{className:"text-sm text-slate-400 mb-4",children:[(0,a.jsx)("span",{className:"font-medium text-slate-300",children:"Transaction Hash:"}),(0,a.jsx)("div",{className:"font-mono text-xs bg-green-900/20 border border-green-700 p-2 rounded mt-1 break-all text-green-300",children:e.transactionHash})]}),e.rejectionReason&&(0,a.jsxs)("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-3 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-300 text-sm font-medium mb-1",children:[(0,a.jsx)(w.A,{className:"h-4 w-4"}),"Rejection Reason"]}),(0,a.jsx)("p",{className:"text-red-400 text-sm",children:e.rejectionReason})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:["PENDING"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>{o(e),h("approve")},className:"border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20",children:[(0,a.jsx)(Q.A,{className:"h-4 w-4 mr-1"}),"Approve"]}),(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>{o(e),h("reject")},className:"border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"Reject"]})]}),"APPROVED"===e.status&&(0,a.jsxs)(P.$n,{variant:"outline",size:"sm",onClick:()=>{o(e),h("complete")},className:"border-blue-600 text-blue-400 hover:text-blue-300 hover:bg-blue-900/20",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 mr-1"}),"Mark Complete"]})]})]})})},e.id))}),x&&m&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4 text-white",children:["approve"===m&&"Approve Withdrawal","reject"===m&&"Reject Withdrawal","complete"===m&&"Complete Withdrawal"]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("p",{className:"text-slate-400 mb-2",children:["User: ",(0,a.jsxs)("span",{className:"font-medium text-white",children:[x.user.firstName," ",x.user.lastName]})]}),(0,a.jsxs)("p",{className:"text-slate-400",children:["Amount: ",(0,a.jsx)("span",{className:"font-medium text-white",children:(0,L.vv)(x.amount)})]})]}),"reject"===m&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Rejection Reason *"}),(0,a.jsx)("textarea",{value:j,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent",rows:3,placeholder:"Please provide a reason for rejection...",required:!0})]}),"complete"===m&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Transaction Hash *"}),(0,a.jsx)(P.pd,{value:N,onChange:e=>b(e.target.value),placeholder:"Enter blockchain transaction hash...",className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(P.$n,{variant:"outline",onClick:()=>{o(null),h(null),g(""),b("")},disabled:f,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:"Cancel"}),(0,a.jsxs)(P.$n,{onClick:()=>{let e={};"reject"===m&&(e.rejectionReason=j),"complete"===m&&(e.transactionHash=N),A(x.id,m,e)},disabled:f||"reject"===m&&!j.trim()||"complete"===m&&!N.trim(),loading:f,className:"reject"===m?"bg-red-600 hover:bg-red-700 text-white":"approve"===m?"bg-green-600 hover:bg-green-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white",children:["approve"===m&&"Approve","reject"===m&&"Reject","complete"===m&&"Mark Complete"]})]})]})})]})};var en=t(93613),ec=t(27900);let ed=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(null),[c,d]=(0,l.useState)(""),[x,o]=(0,l.useState)(!1),[m,h]=(0,l.useState)({status:"",priority:"",search:""});(0,l.useEffect)(()=>{u()},[]);let u=async()=>{try{let e=await fetch("/api/admin/support/tickets",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch tickets:",e)}finally{r(!1)}},p=async(e,s)=>{try{(await fetch(`/api/admin/support/tickets/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({status:s})})).ok&&(u(),i&&i.id===e&&n({...i,status:s}))}catch(e){console.error("Failed to update ticket status:",e)}},g=async e=>{if(c.trim()){o(!0);try{if((await fetch(`/api/admin/support/tickets/${e}/responses`,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({message:c})})).ok){d(""),u();let s=await fetch("/api/admin/support/tickets",{credentials:"include"});if(s.ok){let t=(await s.json()).data.find(s=>s.id===e);t&&n(t)}}}catch(e){console.error("Failed to add response:",e)}finally{o(!1)}}},N=e=>{switch(e){case"OPEN":return(0,a.jsx)(en.A,{className:"h-4 w-4 text-red-500"});case"IN_PROGRESS":return(0,a.jsx)(es.A,{className:"h-4 w-4 text-yellow-500"});case"RESOLVED":return(0,a.jsx)(D.A,{className:"h-4 w-4 text-green-500"});case"CLOSED":return(0,a.jsx)(D.A,{className:"h-4 w-4 text-gray-500"});default:return(0,a.jsx)(en.A,{className:"h-4 w-4 text-gray-500"})}},b=e=>{switch(e){case"OPEN":return"bg-red-100 text-red-700";case"IN_PROGRESS":return"bg-yellow-100 text-yellow-700";case"RESOLVED":return"bg-green-100 text-green-700";default:return"bg-gray-100 text-gray-700"}},f=e=>{switch(e){case"LOW":return"bg-blue-100 text-blue-700";case"MEDIUM":return"bg-yellow-100 text-yellow-700";case"HIGH":return"bg-orange-100 text-orange-700";case"URGENT":return"bg-red-100 text-red-700";default:return"bg-gray-100 text-gray-700"}},v=e.filter(e=>{let s=!m.status||e.status===m.status,t=!m.priority||e.priority===m.priority,a=!m.search||e.subject.toLowerCase().includes(m.search.toLowerCase())||e.user.email.toLowerCase().includes(m.search.toLowerCase())||`${e.user.firstName} ${e.user.lastName}`.toLowerCase().includes(m.search.toLowerCase());return s&&t&&a});return t?(0,a.jsx)("div",{className:"space-y-6",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-32 bg-slate-700 rounded-xl"})},s))}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)("div",{className:"flex-1 min-w-64",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search tickets, users...",value:m.search,onChange:e=>h(s=>({...s,search:e.target.value})),className:"w-full pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,a.jsxs)("select",{value:m.status,onChange:e=>h(s=>({...s,status:e.target.value})),className:"px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"OPEN",children:"Open"}),(0,a.jsx)("option",{value:"IN_PROGRESS",children:"In Progress"}),(0,a.jsx)("option",{value:"RESOLVED",children:"Resolved"}),(0,a.jsx)("option",{value:"CLOSED",children:"Closed"})]}),(0,a.jsxs)("select",{value:m.priority,onChange:e=>h(s=>({...s,priority:e.target.value})),className:"px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"All Priority"}),(0,a.jsx)("option",{value:"LOW",children:"Low"}),(0,a.jsx)("option",{value:"MEDIUM",children:"Medium"}),(0,a.jsx)("option",{value:"HIGH",children:"High"}),(0,a.jsx)("option",{value:"URGENT",children:"Urgent"})]})]})})}),(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(j.A,{className:"h-5 w-5"}),"Support Tickets (",v.length,")"]})}),(0,a.jsx)(P.Wu,{children:v.length>0?(0,a.jsx)("div",{className:"space-y-4",children:v.map(e=>(0,a.jsxs)("div",{onClick:()=>n(e),className:"p-4 bg-slate-700 border border-slate-600 rounded-lg hover:bg-slate-600 cursor-pointer transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-white mb-1",children:e.subject}),(0,a.jsxs)("div",{className:"flex items-center gap-3 text-sm text-slate-400",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(R.A,{className:"h-3 w-3"}),e.user.firstName," ",e.user.lastName," (",e.user.email,")"]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(J.A,{className:"h-3 w-3"}),(0,L.r6)(e.createdAt)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[N(e.status),(0,a.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${b(e.status)}`,children:e.status}),(0,a.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${f(e.priority)}`,children:e.priority})]})]}),(0,a.jsx)("p",{className:"text-sm text-slate-300 line-clamp-2",children:e.message}),e.responses.length>0&&(0,a.jsxs)("p",{className:"text-xs text-slate-400 mt-2",children:[e.responses.length," response",1!==e.responses.length?"s":""]})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(j.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Support Tickets"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No tickets match your current filters."})]})})]}),i&&(0,a.jsx)(P.aF,{isOpen:!!i,onClose:()=>n(null),title:`Ticket: ${i.subject}`,size:"xl",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[N(i.status),(0,a.jsx)("span",{className:`text-sm px-2 py-1 rounded-full ${b(i.status)}`,children:i.status}),(0,a.jsx)("span",{className:`text-sm px-2 py-1 rounded-full ${f(i.priority)}`,children:i.priority})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)("select",{value:i.status,onChange:e=>p(i.id,e.target.value),className:"px-3 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"OPEN",children:"Open"}),(0,a.jsx)("option",{value:"IN_PROGRESS",children:"In Progress"}),(0,a.jsx)("option",{value:"RESOLVED",children:"Resolved"}),(0,a.jsx)("option",{value:"CLOSED",children:"Closed"})]})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsxs)("span",{className:"font-medium text-gray-900",children:[i.user.firstName," ",i.user.lastName]}),(0,a.jsxs)("span",{className:"text-gray-500",children:["(",i.user.email,")"]})]}),(0,a.jsx)("p",{className:"text-gray-900",children:i.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:(0,L.r6)(i.createdAt)})]}),i.responses.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Responses:"}),i.responses.map(e=>(0,a.jsxs)("div",{className:`p-3 rounded-lg ${e.isAdmin?"bg-blue-50 border-l-4 border-blue-500":"bg-gray-50"}`,children:[(0,a.jsx)("p",{className:"text-gray-900",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[e.isAdmin?"Admin":"User"," • ",(0,L.r6)(e.createdAt)]})]},e.id))]}),"CLOSED"!==i.status&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:c,onChange:e=>d(e.target.value),placeholder:"Add admin response...",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsxs)(P.$n,{onClick:()=>g(i.id),disabled:!c.trim()||x,loading:x,className:"w-full",children:[(0,a.jsx)(ec.A,{className:"h-4 w-4 mr-2"}),"Send Admin Response"]})]})]})})]})},ex=(0,g.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]),eo=(0,g.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var em=t(53411);let eh=(0,g.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),eu=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)([]),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(""),[x,o]=(0,l.useState)("all"),[h,p]=(0,l.useState)(null),[j,g]=(0,l.useState)(null),[f,v]=(0,l.useState)([]),[y,w]=(0,l.useState)(!1),[A,C]=(0,l.useState)(!1),[S,E]=(0,l.useState)({totalUsers:0,totalLeftPoints:0,totalRightPoints:0,totalMatchedPoints:0,totalPayouts:0}),[k,R]=(0,l.useState)(10),[T,D]=(0,l.useState)("10"),[M,U]=(0,l.useState)(!1),{showConfirm:$,ConfirmDialog:F}=(0,P.G_)(),{showMessage:O,MessageBoxComponent:_}=(0,P.eC)();(0,l.useEffect)(()=>{Z(),H(),B()},[]);let B=async()=>{try{let e=await fetch("/api/admin/settings",{credentials:"include"});if(e.ok){let s=await e.json();if(s.success){let e=s.data.maxBinaryPointsPerSide||10;R(e),D(e.toString()),console.log(`Current binary points limit: ${e}`)}}}catch(e){console.error("Failed to fetch current limit:",e)}},Z=async()=>{try{let e=await fetch("/api/admin/binary-points",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&(s(t.data),G(t.data))}}catch(e){console.error("Failed to fetch binary points data:",e)}finally{n(!1)}},H=async()=>{try{let e=await fetch("/api/admin/binary-points/history",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&r(s.data)}}catch(e){console.error("Failed to fetch match history:",e)}},G=e=>{E(e.reduce((e,s)=>({totalUsers:e.totalUsers+1,totalLeftPoints:e.totalLeftPoints+s.leftPoints,totalRightPoints:e.totalRightPoints+s.rightPoints,totalMatchedPoints:e.totalMatchedPoints+s.matchedPoints,totalPayouts:e.totalPayouts+10*s.totalMatched}),{totalUsers:0,totalLeftPoints:0,totalRightPoints:0,totalMatchedPoints:0,totalPayouts:0}))},V=e.filter(e=>{let s=e.user.email.toLowerCase().includes(c.toLowerCase())||e.user.firstName.toLowerCase().includes(c.toLowerCase())||e.user.lastName.toLowerCase().includes(c.toLowerCase()),t="all"===x||"active"===x&&(e.leftPoints>0||e.rightPoints>0)||"inactive"===x&&0===e.leftPoints&&0===e.rightPoints;return s&&t}),z=async s=>{p(s),C(!0),w(!0),g(e.find(e=>e.userId===s)||null);try{let e=await fetch(`/api/admin/binary-points/user-history/${s}`,{credentials:"include"});if(e.ok){let s=await e.json();s.success&&v(s.data)}}catch(e){console.error("Failed to fetch user match history:",e)}finally{C(!1)}},K=async()=>{let e=parseFloat(T);if(isNaN(e)||e<=0)return void O({title:"Invalid Input",message:"Please enter a valid positive number for the limit",variant:"error",darkMode:!0});if(await $({title:"Update Binary Points Limit",message:`Are you sure you want to update the maximum points per side to ${e}? This will affect all future binary point additions.`,confirmText:"Update Limit",cancelText:"Cancel"})){U(!0);try{let s=await fetch("/api/admin/binary-points/update-limit",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({maxPointsPerSide:e})}),t=await s.json();t.success?(R(e),O({title:"Success",message:`Successfully updated binary points limit to ${e}`,variant:"success",darkMode:!0})):O({title:"Update Failed",message:`Failed to update limit: ${t.error}`,variant:"error",darkMode:!0})}catch(e){console.error("Update limit error:",e),O({title:"Error",message:"Failed to update binary points limit",variant:"error",darkMode:!0})}finally{U(!1)}}};return i?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(I.A,{className:"h-5 w-5 text-orange-400"}),"Binary Points Limit Configuration"]})}),(0,a.jsxs)(P.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-end",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Maximum Points Per Side"}),(0,a.jsx)(P.pd,{type:"number",value:T,onChange:e=>D(e.target.value),className:"bg-slate-700 border-slate-600 text-white",placeholder:"Enter limit",min:"1",step:"0.1"})]}),(0,a.jsx)(P.$n,{onClick:K,disabled:M||T===k.toString(),className:"bg-orange-600 text-white",children:M?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Updating..."]}):"Update Limit"})]}),(0,a.jsxs)("p",{className:"text-sm text-slate-400 mt-2",children:["Current limit: ",(0,a.jsxs)("span",{className:"text-orange-400 font-medium",children:[k," points per side"]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.ZV)(S.totalUsers)})]}),(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Left Points"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.ZV)(S.totalLeftPoints)})]}),(0,a.jsx)(b.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Right Points"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.ZV)(S.totalRightPoints)})]}),(0,a.jsx)(ex,{className:"h-8 w-8 text-red-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Matched"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.ZV)(S.totalMatchedPoints)})]}),(0,a.jsx)(N,{className:"h-8 w-8 text-purple-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Payouts"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.vv)(S.totalPayouts)})]}),(0,a.jsx)(u.A,{className:"h-8 w-8 text-yellow-400"})]})})})]}),(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(N,{className:"h-5 w-5 text-purple-400"}),"Binary Points Management"]})}),(0,a.jsxs)(P.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"text",placeholder:"Search by email or name...",value:c,onChange:e=>d(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:x,onChange:e=>o(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Users"}),(0,a.jsx)("option",{value:"active",children:"Active Points"}),(0,a.jsx)("option",{value:"inactive",children:"No Points"})]}),(0,a.jsxs)(P.$n,{onClick:()=>{let e=new Blob([["User Email,Name,Left Points,Right Points,Matched Points,Total Matched,Last Match Date",...V.map(e=>[e.user.email,`${e.user.firstName} ${e.user.lastName}`,e.leftPoints,e.rightPoints,e.matchedPoints,e.totalMatched,e.lastMatchDate?(0,L.r6)(e.lastMatchDate):"Never"].join(","))].join("\n")],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`binary-points-${new Date().toISOString().split("T")[0]}.csv`,t.click(),window.URL.revokeObjectURL(s)},className:"bg-green-600 text-white",children:[(0,a.jsx)(eo,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-600",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"User"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Left Points"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Right Points"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Matchable"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Total Matched"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Last Match"}),(0,a.jsx)("th",{className:"text-center py-3 px-4 text-slate-300",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:V.map(e=>{let s=Math.min(e.leftPoints,e.rightPoints);return(0,a.jsxs)("tr",{className:"border-b border-slate-700",children:[(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:e.user.email})]})}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-white",children:(0,L.ZV)(e.leftPoints)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-white",children:(0,L.ZV)(e.rightPoints)}),(0,a.jsx)("td",{className:"text-right py-3 px-4",children:(0,a.jsx)("span",{className:`font-medium ${s>0?"text-green-400":"text-slate-400"}`,children:(0,L.ZV)(s)})}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-white",children:(0,L.ZV)(e.totalMatched)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-slate-300",children:e.lastMatchDate?(0,L.r6)(e.lastMatchDate):"Never"}),(0,a.jsx)("td",{className:"text-center py-3 px-4",children:(0,a.jsxs)(P.$n,{onClick:()=>z(e.userId),className:"bg-blue-600 text-white text-xs px-2 py-1",children:[(0,a.jsx)(Y.A,{className:"h-3 w-3 mr-1"}),"View"]})})]},e.id)})})]})}),0===V.length&&(0,a.jsx)("div",{className:"text-center py-8 text-slate-400",children:"No binary points data found matching your criteria."})]})]}),y&&j&&(0,a.jsx)(P.aF,{isOpen:y,onClose:()=>{w(!1),p(null),g(null),v([])},title:`Binary Points Details - ${j.user.firstName} ${j.user.lastName}`,size:"xl",darkMode:!0,children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-slate-700 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"User Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Name:"}),(0,a.jsxs)("span",{className:"text-white",children:[j.user.firstName," ",j.user.lastName]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Email:"}),(0,a.jsx)("span",{className:"text-white",children:j.user.email})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Joined:"}),(0,a.jsx)("span",{className:"text-white",children:(0,L.r6)(j.user.createdAt)})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Current Status"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Left Points:"}),(0,a.jsx)("span",{className:"text-green-400 font-semibold",children:(0,L.ZV)(j.leftPoints)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Right Points:"}),(0,a.jsx)("span",{className:"text-orange-400 font-semibold",children:(0,L.ZV)(j.rightPoints)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Total Matched:"}),(0,a.jsx)("span",{className:"text-purple-400 font-semibold",children:(0,L.ZV)(j.totalMatched)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-slate-300",children:"Last Match:"}),(0,a.jsx)("span",{className:"text-white",children:j.lastMatchDate?(0,L.r6)(j.lastMatchDate):"Never"})]})]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Matchable Points"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.ZV)(Math.min(j.leftPoints,j.rightPoints))})]}),(0,a.jsx)(em.A,{className:"h-8 w-8 text-purple-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Potential Payout"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.vv)(10*Math.min(j.leftPoints,j.rightPoints))})]}),(0,a.jsx)(u.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Earned"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.vv)(10*j.totalMatched)})]}),(0,a.jsx)(eh,{className:"h-8 w-8 text-blue-400"})]})})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center gap-2",children:[(0,a.jsx)(J.A,{className:"h-5 w-5 text-blue-400"}),"Binary Matching History"]}),A?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto"}),(0,a.jsx)("p",{className:"text-slate-400 mt-2",children:"Loading match history..."})]}):f.length>0?(0,a.jsx)("div",{className:"bg-slate-800 rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{className:"bg-slate-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"Date"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Matched Points"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Payout"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Left Before"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Right Before"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Left After"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Right After"}),(0,a.jsx)("th",{className:"text-center py-3 px-4 text-slate-300",children:"Type"})]})}),(0,a.jsx)("tbody",{children:f.map((e,s)=>(0,a.jsxs)("tr",{className:s%2==0?"bg-slate-800":"bg-slate-750",children:[(0,a.jsx)("td",{className:"py-3 px-4 text-white",children:(0,L.r6)(e.matchDate)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-purple-400 font-semibold",children:(0,L.ZV)(e.matchedPoints)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-green-400 font-semibold",children:(0,L.vv)(e.payout)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-slate-300",children:(0,L.ZV)(e.leftPointsBefore)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-slate-300",children:(0,L.ZV)(e.rightPointsBefore)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-green-400",children:(0,L.ZV)(e.leftPointsAfter)}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-orange-400",children:(0,L.ZV)(e.rightPointsAfter)}),(0,a.jsx)("td",{className:"text-center py-3 px-4",children:(0,a.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"WEEKLY"===e.type?"bg-blue-600 text-white":"bg-orange-600 text-white"}`,children:e.type})})]},e.id))})]})})}):(0,a.jsxs)("div",{className:"text-center py-8 bg-slate-800 rounded-lg",children:[(0,a.jsx)(J.A,{className:"h-12 w-12 text-slate-600 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No binary matching history found for this user."})]})]}),(0,a.jsx)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-slate-700",children:(0,a.jsx)(P.$n,{variant:"outline",onClick:()=>{w(!1),p(null),g(null),v([])},className:"bg-slate-700 border-slate-600 text-white hover:bg-slate-600",children:"Close"})})]})}),(0,a.jsx)(F,{}),(0,a.jsx)(_,{})]})};var ep=t(70334);let ej=(0,g.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),eg=(0,g.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),eN=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(null),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(""),[x,o]=(0,l.useState)("all"),[h,p]=(0,l.useState)("all"),[j,g]=(0,l.useState)("30d"),[N,f]=(0,l.useState)(null),[v,y]=(0,l.useState)(1),[w,A]=(0,l.useState)(1),[C,S]=(0,l.useState)(0);(0,l.useEffect)(()=>{E(),k()},[j,x,h,v]);let E=async()=>{try{let e=new URLSearchParams({dateRange:j,type:x,status:h,page:v.toString(),limit:"20"}),t=await fetch(`/api/admin/referral-commissions?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(s(e.data),e.pagination&&(A(Math.ceil(e.pagination.total/20)),S(e.pagination.total)))}}catch(e){console.error("Failed to fetch referral commissions:",e)}finally{n(!1)}},k=async()=>{try{let e=new URLSearchParams({dateRange:j}),s=await fetch(`/api/admin/referral-commissions/stats?${e}`,{credentials:"include"});if(s.ok){let e=await s.json();e.success&&r(e.data)}}catch(e){console.error("Failed to fetch commission stats:",e)}};return((0,l.useEffect)(()=>{y(1)},[c,x,h,j]),i)?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[t&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Commissions"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.ZV)(t.totalCommissions)})]}),(0,a.jsx)(m.A,{className:"h-8 w-8 text-blue-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Total Amount"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.vv)(t.totalAmount)})]}),(0,a.jsx)(u.A,{className:"h-8 w-8 text-green-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Average Commission"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.vv)(t.averageCommission)})]}),(0,a.jsx)(b.A,{className:"h-8 w-8 text-orange-400"})]})})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Recent Activity"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,L.ZV)(t.recentActivity)})]}),(0,a.jsx)(J.A,{className:"h-8 w-8 text-purple-400"})]})})})]}),t?.topEarners&&t.topEarners.length>0&&(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-orange-400"}),"Top Commission Earners"]})}),(0,a.jsx)(P.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:t.topEarners.slice(0,6).map((e,s)=>(0,a.jsxs)("div",{className:"p-4 bg-slate-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-slate-300",children:["#",s+1]}),(0,a.jsx)("span",{className:"text-lg font-bold text-green-400",children:(0,L.vv)(e.totalEarned)})]}),(0,a.jsxs)("div",{className:"text-sm text-white",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsx)("div",{className:"text-xs text-slate-400",children:e.user.email}),(0,a.jsxs)("div",{className:"text-xs text-slate-400 mt-1",children:[e.commissionCount," commissions"]})]},e.userId))})})]}),(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-blue-400"}),"Referral Commission Tracking"]})}),(0,a.jsxs)(P.Wu,{children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"text",placeholder:"Search by user email or name...",value:c,onChange:e=>d(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:j,onChange:e=>g(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",children:[(0,a.jsx)("option",{value:"7d",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30d",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90d",children:"Last 90 days"}),(0,a.jsx)("option",{value:"all",children:"All time"})]}),(0,a.jsxs)("select",{value:x,onChange:e=>o(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"DIRECT_REFERRAL",children:"Direct Referral"}),(0,a.jsx)("option",{value:"MINING_PURCHASE",children:"Mining Purchase"})]}),(0,a.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"COMPLETED",children:"Completed"}),(0,a.jsx)("option",{value:"PENDING",children:"Pending"}),(0,a.jsx)("option",{value:"FAILED",children:"Failed"})]}),(0,a.jsxs)(P.$n,{onClick:()=>{let e=new Blob([["Date,From User,To User,Type,Original Amount,Commission Rate,Commission Amount,Status",...filteredCommissions.map(e=>[(0,L.r6)(e.createdAt),`${e.fromUser.firstName} ${e.fromUser.lastName} (${e.fromUser.email})`,`${e.toUser.firstName} ${e.toUser.lastName} (${e.toUser.email})`,e.type,e.originalAmount,`${e.commissionRate}%`,e.amount,e.status].join(","))].join("\n")],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`referral-commissions-${new Date().toISOString().split("T")[0]}.csv`,t.click(),window.URL.revokeObjectURL(s)},className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,a.jsx)(eo,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-slate-600",children:[(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"From → To"}),(0,a.jsx)("th",{className:"text-left py-3 px-4 text-slate-300",children:"Type"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Original Amount"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Rate"}),(0,a.jsx)("th",{className:"text-right py-3 px-4 text-slate-300",children:"Commission"}),(0,a.jsx)("th",{className:"text-center py-3 px-4 text-slate-300",children:"Status"})]})}),(0,a.jsx)("tbody",{children:e.map(e=>(0,a.jsxs)("tr",{className:"border-b border-slate-700 hover:bg-slate-700/50",children:[(0,a.jsx)("td",{className:"py-3 px-4 text-slate-300",children:(0,L.r6)(e.createdAt)}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsxs)("div",{className:"text-white",children:[e.fromUser.firstName," ",e.fromUser.lastName]}),(0,a.jsx)("div",{className:"text-slate-400",children:e.fromUser.email})]}),(0,a.jsx)(ep.A,{className:"h-3 w-3 text-slate-400"}),(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsxs)("div",{className:"text-white",children:[e.toUser.firstName," ",e.toUser.lastName]}),(0,a.jsx)("div",{className:"text-slate-400",children:e.toUser.email})]})]})}),(0,a.jsx)("td",{className:"py-3 px-4",children:(0,a.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"DIRECT_REFERRAL"===e.type?"bg-blue-600/20 text-blue-400":"bg-green-600/20 text-green-400"}`,children:"DIRECT_REFERRAL"===e.type?"Referral":"Mining"})}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-white",children:(0,L.vv)(e.originalAmount)}),(0,a.jsxs)("td",{className:"text-right py-3 px-4 text-slate-300",children:[e.commissionRate,"%"]}),(0,a.jsx)("td",{className:"text-right py-3 px-4 text-green-400 font-medium",children:(0,L.vv)(e.amount)}),(0,a.jsx)("td",{className:"text-center py-3 px-4",children:(0,a.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"COMPLETED"===e.status?"bg-green-600/20 text-green-400":"PENDING"===e.status?"bg-yellow-600/20 text-yellow-400":"bg-red-600/20 text-red-400"}`,children:e.status})})]},e.id))})]})}),0===e.length&&!i&&(0,a.jsx)("div",{className:"text-center py-8 text-slate-400",children:"No referral commissions found matching your criteria."}),w>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-4 border-t border-slate-600",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:["Showing ",(v-1)*20+1," to ",Math.min(20*v,C)," of ",C," results"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(P.$n,{onClick:()=>y(e=>Math.max(e-1,1)),disabled:1===v,className:"bg-slate-700 hover:bg-slate-600 text-white border-slate-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(ej,{className:"h-4 w-4"}),"Previous"]}),(0,a.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,w)},(e,s)=>{let t=Math.max(1,Math.min(w-4,v-2))+s;return t>w?null:(0,a.jsx)(P.$n,{onClick:()=>y(t),className:`w-10 h-10 ${v===t?"bg-blue-600 hover:bg-blue-700 text-white":"bg-slate-700 hover:bg-slate-600 text-slate-300 border-slate-600"}`,children:t},t)})}),(0,a.jsxs)(P.$n,{onClick:()=>y(e=>Math.min(e+1,w)),disabled:v===w,className:"bg-slate-700 hover:bg-slate-600 text-white border-slate-600 disabled:opacity-50 disabled:cursor-not-allowed",children:["Next",(0,a.jsx)(eg,{className:"h-4 w-4"})]})]})]})]})]})]})};var eb=t(2643),ef=t(12597);let ev=()=>{let[e,s]=(0,l.useState)({host:"",port:587,secure:!1,username:"",password:"",senderName:"",senderEmail:"",isActive:!0}),[t,r]=(0,l.useState)({totalSent:0,totalFailed:0,recentEmails:[]}),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(!1),[x,o]=(0,l.useState)(!1),[m,h]=(0,l.useState)(!1),[u,p]=(0,l.useState)(null);(0,l.useEffect)(()=>{j(),g()},[]);let j=async()=>{try{let e=await fetch("/api/admin/email/smtp",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&t.data&&s(t.data)}}catch(e){console.error("Error fetching SMTP config:",e)}finally{n(!1)}},g=async()=>{try{let e=await fetch("/api/admin/email/stats",{credentials:"include"});if(e.ok){let s=await e.json();s.success&&r(s.data)}}catch(e){console.error("Error fetching email stats:",e)}},N=async()=>{d(!0),p(null);try{let s=await fetch("/api/admin/email/smtp",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),t=await s.json();t.success?(p({success:!0,message:"SMTP configuration saved successfully!"}),await j()):p({success:!1,message:t.error||"Failed to save configuration"})}catch(e){p({success:!1,message:"Failed to save configuration"})}finally{d(!1)}},b=async()=>{o(!0),p(null);try{let s=await fetch("/api/admin/email/test",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)}),t=await s.json();p({success:t.success,message:t.success?"Connection test successful!":t.error||"Connection test failed"})}catch(e){p({success:!1,message:"Connection test failed"})}finally{o(!1)}},y=(e,t)=>{s(s=>({...s,[e]:t})),p(null)};return i?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(P.Rh,{size:"lg",text:"Loading email settings..."})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Email Settings"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Configure SMTP settings and manage email templates"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(f.A,{className:"h-6 w-6 text-blue-600"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(D.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Emails Sent"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.totalSent})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(et.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Failed Emails"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.totalFailed})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(ec.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Success Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-semibold text-gray-900",children:[t.totalSent+t.totalFailed>0?Math.round(t.totalSent/(t.totalSent+t.totalFailed)*100):0,"%"]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-gray-400 mr-2"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"SMTP Configuration"})]})}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[u&&(0,a.jsx)("div",{className:`p-4 rounded-md ${u.success?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"}`,children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:u.success?(0,a.jsx)(D.A,{className:"h-5 w-5 text-green-400"}):(0,a.jsx)(et.A,{className:"h-5 w-5 text-red-400"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:`text-sm font-medium ${u.success?"text-green-800":"text-red-800"}`,children:u.message})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"SMTP Host *"}),(0,a.jsx)("input",{type:"text",value:e.host,onChange:e=>y("host",e.target.value),placeholder:"smtp.gmail.com",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"SMTP Port *"}),(0,a.jsx)("input",{type:"number",value:e.port,onChange:e=>y("port",parseInt(e.target.value)),placeholder:"587",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Username *"}),(0,a.jsx)("input",{type:"text",value:e.username,onChange:e=>y("username",e.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:m?"text":"password",value:e.password,onChange:e=>y("password",e.target.value),placeholder:"App password or SMTP password",className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,a.jsx)("button",{type:"button",onClick:()=>h(!m),className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:m?(0,a.jsx)(ef.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(Y.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sender Name *"}),(0,a.jsx)("input",{type:"text",value:e.senderName,onChange:e=>y("senderName",e.target.value),placeholder:"HashCoreX Support",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sender Email *"}),(0,a.jsx)("input",{type:"email",value:e.senderEmail,onChange:e=>y("senderEmail",e.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"secure",checked:e.secure,onChange:e=>y("secure",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"secure",className:"ml-2 block text-sm text-gray-900",children:"Use SSL/TLS (recommended for port 465)"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"isActive",checked:e.isActive,onChange:e=>y("isActive",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"isActive",className:"ml-2 block text-sm text-gray-900",children:"Enable email sending"})]}),(0,a.jsxs)("div",{className:"flex space-x-4 pt-4",children:[(0,a.jsx)(eb.$,{onClick:b,disabled:x||!e.host||!e.username,variant:"outline",children:x?(0,a.jsx)(P.Rh,{size:"sm"}):"Test Connection"}),(0,a.jsx)(eb.$,{onClick:N,disabled:c||!e.host||!e.username,children:c?(0,a.jsx)(P.Rh,{size:"sm"}):"Save Configuration"})]})]})]})]})};var ey=t(11437),ew=t(8819);let eA=(0,g.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]),eC=()=>{let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(!1),[c,d]=(0,l.useState)(!1),[x,o]=(0,l.useState)("pricing"),[m,h]=(0,l.useState)(!1),[p,j]=(0,l.useState)(!1),{showConfirm:g,ConfirmDialog:f}=(0,P.G_)();(0,l.useEffect)(()=>{v()},[]);let v=async()=>{try{r(!0);let e=await fetch("/api/admin/settings",{credentials:"include"});if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("Failed to fetch settings:",e)}finally{r(!1)}},y=async()=>{if(e)try{n(!0),console.log("Saving settings:",e),console.log("maxBinaryPointsPerSide value:",e.maxBinaryPointsPerSide,typeof e.maxBinaryPointsPerSide),console.log("Full settings object being sent:",JSON.stringify(e,null,2));let s=await fetch("/api/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(s.ok)d(!0),setTimeout(()=>d(!1),3e3),console.log("Settings saved successfully");else{let e=await s.json();console.error("Failed to save settings:",e)}}catch(e){console.error("Failed to save settings:",e)}finally{n(!1)}},w=(t,a)=>{e&&s({...e,[t]:a})},A=(t,a,l)=>{if(!e)return;let r=[...e.earningsRanges];r[t]={...r[t],[a]:l},s({...e,earningsRanges:r})},C=()=>{e&&s({...e,earningsRanges:[...e.earningsRanges,{minTHS:0,maxTHS:100,dailyReturnMin:.5,dailyReturnMax:1,monthlyReturnMin:10,monthlyReturnMax:15}]})},S=t=>{if(!e)return;let a=e.earningsRanges.filter((e,s)=>s!==t);s({...e,earningsRanges:a})},E=async()=>{g({title:"Update Mining Units ROI",message:"Are you sure you want to update all existing mining units with the new ROI configuration? This will recalculate ROI for all active mining units based on their TH/s amounts.",variant:"warning",confirmText:"Update ROI",cancelText:"Cancel",darkMode:!0,onConfirm:async()=>{h(!0);try{let e=await fetch("/api/admin/update-mining-units-roi",{method:"POST",credentials:"include"}),s=await e.json();s.success?g({title:"ROI Update Completed",message:`Mining units ROI update completed successfully!

Units updated: ${s.data.unitsUpdated}
Total units: ${s.data.totalUnits}`,variant:"success",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}}):g({title:"Update Failed",message:`Error: ${s.error}`,variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}catch(e){console.error("Mining units ROI update error:",e),g({title:"Error",message:"Failed to update mining units ROI. Please try again.",variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}finally{h(!1)}}})},k=()=>{g({title:"Manual Binary Matching",message:"Are you sure you want to manually trigger binary matching? This will process all users with binary points and distribute earnings.",variant:"warning",confirmText:"Process Matching",darkMode:!0,onConfirm:async()=>{j(!0);try{let e=await fetch("/api/admin/binary-matching/manual",{method:"POST",credentials:"include"}),s=await e.json();if(s.success){let e=s.data.matchingResults||[],t=s.data.usersProcessed||0,a=s.data.totalPayouts||0,l=`Binary matching completed successfully!

`;l+=`📊 SUMMARY:
• Users processed: ${t}
• Total payouts: $${a.toFixed(2)}
• Total matched points: ${e.reduce((e,s)=>e+s.matchedPoints,0).toFixed(2)}

`,e.length>0&&(l+=`💰 DETAILED RESULTS:
`,e.slice(0,10).forEach((e,s)=>{l+=`${s+1}. User ${e.userId.substring(0,8)}...
   • Matched: ${e.matchedPoints.toFixed(2)} points
   • Payout: $${e.payout.toFixed(2)}
   • Remaining: L:${e.remainingLeftPoints.toFixed(2)} | R:${e.remainingRightPoints.toFixed(2)}

`}),e.length>10&&(l+=`... and ${e.length-10} more users

`)),l+=`✅ All earnings have been credited to user wallets.`,g({title:"Binary Matching Results",message:l,variant:"success",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}else g({title:"Error",message:`Error: ${s.error}`,variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}catch(e){console.error("Manual binary matching error:",e),g({title:"Error",message:"Failed to process binary matching. Please try again.",variant:"danger",confirmText:"OK",cancelText:"",darkMode:!0,onConfirm:()=>{}})}finally{j(!1)}}})},R=[{id:"pricing",label:"Mining & Pricing",icon:M.A},{id:"earnings",label:"Earnings Config",icon:b.A},{id:"binary",label:"Binary Matching",icon:N},{id:"deposits",label:"Deposits",icon:B.A},{id:"withdrawals",label:"Withdrawals",icon:u.A},{id:"platform",label:"Platform",icon:ey.A}];return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(P.$n,{onClick:y,loading:i,className:"flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(D.A,{className:"h-4 w-4"}),"Saved"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ew.A,{className:"h-4 w-4"}),"Save Changes"]})})}),(0,a.jsx)("div",{className:"border-b border-slate-700",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:R.map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>o(e.id),className:`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${x===e.id?"border-blue-500 text-blue-400":"border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-300"}`,children:[(0,a.jsx)(s,{className:"h-4 w-4"}),e.label]},e.id)})})}),function(){if(!e)return null;switch(x){case"pricing":return(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(M.A,{className:"h-5 w-5 text-blue-400"}),"Mining Unit Pricing"]})}),(0,a.jsx)(P.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"THS Price (USD)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.thsPriceUSD,onChange:e=>w("thsPriceUSD",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Purchase ($)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.minPurchaseAmount,onChange:e=>w("minPurchaseAmount",parseFloat(e.target.value)||0),placeholder:"50",className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Maximum Purchase ($)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.maxPurchaseAmount,onChange:e=>w("maxPurchaseAmount",parseFloat(e.target.value)||0),placeholder:"100000",className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})]})]})})]});case"earnings":return(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-orange-400"}),"Dynamic Earnings Configuration"]})}),(0,a.jsxs)(P.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white",children:"TH/s Based Daily Returns"}),(0,a.jsx)(P.$n,{onClick:C,size:"sm",className:"bg-orange-600 hover:bg-orange-700 text-white px-3 py-1.5 text-xs font-medium",children:"Add Range"})]}),(0,a.jsx)("div",{className:"space-y-3",children:e.earningsRanges?.map((e,s)=>(0,a.jsxs)("div",{className:"p-3 bg-slate-700 rounded-lg border border-slate-600",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Min TH/s"}),(0,a.jsx)(P.pd,{type:"number",step:"0.1",value:e.minTHS,onChange:e=>A(s,"minTHS",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Max TH/s"}),(0,a.jsx)(P.pd,{type:"number",step:"0.1",value:e.maxTHS,onChange:e=>A(s,"maxTHS",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsx)(P.$n,{onClick:()=>S(s),size:"sm",className:"bg-red-600 hover:bg-red-700 text-white w-full px-2 py-1 text-xs font-medium h-8",children:"Remove"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Min Daily Return (%)"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.dailyReturnMin,onChange:e=>A(s,"dailyReturnMin",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Max Daily Return (%)"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.dailyReturnMax,onChange:e=>A(s,"dailyReturnMax",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Min Monthly Return (%)"}),(0,a.jsx)(P.pd,{type:"number",step:"0.1",value:e.monthlyReturnMin,onChange:e=>A(s,"monthlyReturnMin",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-slate-300 mb-1",children:"Max Monthly Return (%)"}),(0,a.jsx)(P.pd,{type:"number",step:"0.1",value:e.monthlyReturnMax,onChange:e=>A(s,"monthlyReturnMax",parseFloat(e.target.value)||0),className:"bg-slate-600 border-slate-500 text-white text-sm h-8"})]})]})]},s))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Binary Bonus (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(eA,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.binaryBonusPercentage,onChange:e=>w("binaryBonusPercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Referral Bonus (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(eA,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.referralBonusPercentage,onChange:e=>w("referralBonusPercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-4 bg-slate-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Update Existing Mining Units"}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"Apply new ROI configuration to all active mining units"})]}),(0,a.jsx)(P.$n,{onClick:E,loading:m,className:"bg-orange-600 hover:bg-orange-700 text-white",children:"Update All Units"})]}),(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Important Notes:"}),(0,a.jsxs)("ul",{className:"text-xs text-slate-400 space-y-1",children:[(0,a.jsx)("li",{children:"• Changes to earnings configuration will affect new mining units automatically"}),(0,a.jsx)("li",{children:'• Use "Update All Units" button to apply changes to existing active mining units'}),(0,a.jsx)("li",{children:"• Monthly return limits are enforced to prevent excessive payouts"}),(0,a.jsx)("li",{children:"• TH/s ranges should not overlap for proper calculation"}),(0,a.jsx)("li",{children:"• Daily returns are randomly selected within the specified range for each unit"})]})]})]})]});case"binary":return(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(N,{className:"h-5 w-5 text-purple-400"}),"Binary Matching Settings"]})}),(0,a.jsxs)(P.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Max Points Per Side"}),(0,a.jsx)(P.pd,{type:"number",value:e.maxBinaryPointsPerSide,onChange:e=>{let s=e.target.value,t=""===s?0:parseInt(s);!isNaN(t)&&t>=0&&w("maxBinaryPointsPerSide",t)},className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Point Value (USD)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.binaryPointValue,onChange:e=>w("binaryPointValue",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Matching Schedule"}),(0,a.jsx)(P.pd,{type:"text",value:e.binaryMatchingSchedule,onChange:e=>w("binaryMatchingSchedule",e.target.value),placeholder:"Weekly at 15:00 UTC",className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-slate-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Binary Matching Enabled"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Enable automatic binary point matching"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.binaryMatchingEnabled,onChange:e=>w("binaryMatchingEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-4 bg-slate-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Manual Binary Matching"}),(0,a.jsx)("p",{className:"text-xs text-slate-400",children:"Trigger binary matching process manually"})]}),(0,a.jsx)(P.$n,{onClick:k,loading:p,className:"bg-purple-600 hover:bg-purple-700 text-white",children:"Process Matching"})]}),(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Binary Matching Rules:"}),(0,a.jsxs)("ul",{className:"text-xs text-slate-400 space-y-1",children:[(0,a.jsx)("li",{children:"• Points are matched weekly at the scheduled time"}),(0,a.jsx)("li",{children:"• Matched points are paid out at the configured point value"}),(0,a.jsx)("li",{children:"• Excess points beyond the maximum are reset (pressure out)"}),(0,a.jsx)("li",{children:"• Only active users (with mining units) receive binary points"}),(0,a.jsx)("li",{children:"• Manual matching can be triggered anytime by admin"})]})]})]})]});case"deposits":return(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(B.A,{className:"h-5 w-5 text-green-400"}),"Deposit Settings"]})}),(0,a.jsxs)(P.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-slate-700 p-4 rounded-lg border border-slate-600",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[(0,a.jsx)("div",{className:`w-3 h-3 rounded-full ${"mainnet"===e.tronNetwork?"bg-green-500":"bg-orange-500"} animate-pulse`}),"Tron Network Configuration"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Active Network"}),(0,a.jsxs)("select",{value:e.tronNetwork,onChange:e=>w("tronNetwork",e.target.value),className:"w-full bg-slate-600 border-slate-500 text-white rounded-md px-3 py-2 focus:border-blue-500 focus:outline-none",children:[(0,a.jsx)("option",{value:"testnet",children:"Shasta Testnet"}),(0,a.jsx)("option",{value:"mainnet",children:"Mainnet"})]}),(0,a.jsx)("p",{className:"text-xs text-slate-400 mt-1",children:"mainnet"===e.tronNetwork?"Production network for real transactions":"Test network for development"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Current Network Status"}),(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-slate-600 rounded-md",children:[(0,a.jsx)("div",{className:`w-2 h-2 rounded-full ${"mainnet"===e.tronNetwork?"bg-green-500":"bg-orange-500"} animate-pulse`}),(0,a.jsx)("span",{className:"text-sm text-white",children:"mainnet"===e.tronNetwork?"Mainnet Active":"Testnet Active"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Mainnet API URL"}),(0,a.jsx)(P.pd,{type:"text",value:e.tronMainnetApiUrl,onChange:e=>w("tronMainnetApiUrl",e.target.value),className:"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm",placeholder:"https://api.trongrid.io"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Testnet API URL"}),(0,a.jsx)(P.pd,{type:"text",value:e.tronTestnetApiUrl,onChange:e=>w("tronTestnetApiUrl",e.target.value),className:"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm",placeholder:"https://api.shasta.trongrid.io"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Mainnet USDT Contract"}),(0,a.jsx)(P.pd,{type:"text",value:e.usdtMainnetContract,onChange:e=>w("usdtMainnetContract",e.target.value),className:"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm",placeholder:"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Testnet USDT Contract"}),(0,a.jsx)(P.pd,{type:"text",value:e.usdtTestnetContract,onChange:e=>w("usdtTestnetContract",e.target.value),className:"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm",placeholder:"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"})]})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"USDT TRC20 Deposit Address"}),(0,a.jsx)(P.pd,{type:"text",value:e.usdtDepositAddress,onChange:e=>w("usdtDepositAddress",e.target.value),placeholder:"Enter USDT TRC20 address (e.g., TXXXxxxXXXxxxXXX...)",className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500 font-mono text-sm"}),(0,a.jsx)("p",{className:"text-xs text-slate-400 mt-1",children:"This address will be displayed to users for USDT deposits"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Deposit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.minDepositAmount,onChange:e=>w("minDepositAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Maximum Deposit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.maxDepositAmount,onChange:e=>w("maxDepositAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Deposit Fee (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(eA,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.depositFeePercentage,onChange:e=>w("depositFeePercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Confirmations"}),(0,a.jsx)(P.pd,{type:"number",value:e.minConfirmations,onChange:e=>w("minConfirmations",parseInt(e.target.value)||0),className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"}),(0,a.jsx)("p",{className:"text-xs text-slate-400 mt-1",children:"Number of blockchain confirmations required"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Deposits Enabled"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Allow users to make deposits"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.depositEnabled,onChange:e=>w("depositEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"})]})]})]})]})]});case"withdrawals":return(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-red-400"}),"Withdrawal Settings"]})}),(0,a.jsxs)(P.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Minimum Withdrawal"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.minWithdrawalAmount,onChange:e=>w("minWithdrawalAmount",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Fixed Fee ($)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.withdrawalFeeFixed,onChange:e=>w("withdrawalFeeFixed",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Percentage Fee (%)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(eA,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{type:"number",step:"0.01",value:e.withdrawalFeePercentage,onChange:e=>w("withdrawalFeePercentage",parseFloat(e.target.value)||0),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Processing Days"}),(0,a.jsx)(P.pd,{type:"number",value:e.withdrawalProcessingDays,onChange:e=>w("withdrawalProcessingDays",parseInt(e.target.value)||0),className:"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-4 bg-slate-700 rounded-lg",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Fee Calculation Example:"}),(0,a.jsxs)("p",{className:"text-xs text-slate-400",children:["For a $100 withdrawal: Fixed Fee ($",e.withdrawalFeeFixed,") + Percentage Fee ($",(100*(e.withdrawalFeePercentage/100)).toFixed(2),") = Total Fee: $",(e.withdrawalFeeFixed+100*(e.withdrawalFeePercentage/100)).toFixed(2)]}),(0,a.jsxs)("p",{className:"text-xs text-slate-400 mt-1",children:["User receives: $",(100-(e.withdrawalFeeFixed+100*(e.withdrawalFeePercentage/100))).toFixed(2)]})]})]})]});case"platform":return(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(ey.A,{className:"h-5 w-5 text-blue-400"}),"Platform Settings"]})}),(0,a.jsxs)(P.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6"}),(0,a.jsxs)("div",{className:"space-y-4 pt-4 border-t border-slate-600",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Maintenance Mode"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Temporarily disable platform access"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.maintenanceMode,onChange:e=>w("maintenanceMode",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"Registration Enabled"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Allow new user registrations"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.registrationEnabled,onChange:e=>w("registrationEnabled",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white",children:"KYC Required"}),(0,a.jsx)("p",{className:"text-sm text-slate-400",children:"Require KYC verification for withdrawals"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.kycRequired,onChange:e=>w("kycRequired",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]})]});default:return null}}(),(0,a.jsx)(f,{})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(I.A,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"Failed to Load Settings"}),(0,a.jsx)("p",{className:"text-slate-400",children:"Unable to load system settings. Please try again."})]})},eS=()=>{let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)(!0),[i,n]=(0,l.useState)(""),[c,d]=(0,l.useState)("all"),[x,o]=(0,l.useState)("today"),[m,h]=(0,l.useState)(1),[u,p]=(0,l.useState)(1);(0,l.useEffect)(()=>{j()},[m,i,c,x]);let j=async()=>{try{r(!0);let e=new URLSearchParams({page:m.toString(),limit:"20",search:i,action:c,dateRange:x}),t=await fetch(`/api/admin/logs?${e}`,{credentials:"include"});if(t.ok){let e=await t.json();e.success&&(s(e.logs),p(e.totalPages))}}catch(e){console.error("Failed to fetch logs:",e)}finally{r(!1)}},g=async()=>{try{let e=new URLSearchParams({search:i,action:c,dateRange:x,export:"true"}),s=await fetch(`/api/admin/logs/export?${e}`,{credentials:"include"});if(s.ok){let e=await s.blob(),t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download=`system-logs-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}}catch(e){console.error("Failed to export logs:",e)}},N=e=>{switch(e){case"USER_LOGIN":case"USER_LOGOUT":return(0,a.jsx)(R.A,{className:"h-4 w-4 text-blue-400"});case"USER_REGISTERED":case"USER_REGISTER":return(0,a.jsx)(D.A,{className:"h-4 w-4 text-green-400"});case"MINING_UNIT_PURCHASED":case"MINING_PURCHASE":return(0,a.jsx)(eh,{className:"h-4 w-4 text-purple-400"});case"WITHDRAWAL_REQUESTED":case"WITHDRAWAL_REQUEST":case"WITHDRAWAL_APPROVED":case"WITHDRAWAL_REJECTED":return(0,a.jsx)(eh,{className:"h-4 w-4 text-orange-400"});case"DEPOSIT_VERIFICATION_ATTEMPT":case"DEPOSIT_CONFIRMED":case"DEPOSIT_FAILED":return(0,a.jsx)(eh,{className:"h-4 w-4 text-cyan-400"});case"KYC_SUBMITTED":case"KYC_SUBMIT":case"KYC_APPROVED":case"KYC_APPROVE":case"KYC_REJECTED":case"KYC_REJECT":return(0,a.jsx)(y.A,{className:"h-4 w-4 text-orange-400"});case"BINARY_MATCHING_PROCESSED":case"MANUAL_BINARY_MATCHING_TRIGGERED":case"BINARY_MATCHING_CRON_EXECUTED":return(0,a.jsx)(eh,{className:"h-4 w-4 text-green-400"});case"ADMIN_ACTION":case"WALLET_ADJUSTMENT":return(0,a.jsx)(I.A,{className:"h-4 w-4 text-red-400"});case"LOGIN_FAILED":case"CLIENT_ERROR":case"DATABASE_ERROR":case"AUTH_ERROR":return(0,a.jsx)(et.A,{className:"h-4 w-4 text-red-400"});default:return(0,a.jsx)(el.A,{className:"h-4 w-4 text-slate-400"})}},b=e=>{switch(e){case"USER_LOGIN":case"USER_REGISTERED":case"USER_REGISTER":case"KYC_APPROVED":case"KYC_APPROVE":case"DEPOSIT_CONFIRMED":case"WITHDRAWAL_APPROVED":case"BINARY_MATCHING_PROCESSED":case"MANUAL_BINARY_MATCHING_TRIGGERED":return"text-green-300 bg-green-900/20 border border-green-700";case"USER_LOGOUT":return"text-blue-300 bg-blue-900/20 border border-blue-700";case"MINING_UNIT_PURCHASED":case"MINING_PURCHASE":case"WITHDRAWAL_REQUESTED":case"WITHDRAWAL_REQUEST":return"text-purple-300 bg-purple-900/20 border border-purple-700";case"DEPOSIT_VERIFICATION_ATTEMPT":return"text-cyan-300 bg-cyan-900/20 border border-cyan-700";case"KYC_SUBMITTED":case"KYC_SUBMIT":return"text-orange-300 bg-orange-900/20 border border-orange-700";case"KYC_REJECTED":case"KYC_REJECT":case"WITHDRAWAL_REJECTED":case"ADMIN_ACTION":case"WALLET_ADJUSTMENT":case"LOGIN_FAILED":case"CLIENT_ERROR":case"DATABASE_ERROR":case"AUTH_ERROR":case"DEPOSIT_FAILED":return"text-red-300 bg-red-900/20 border border-red-700";default:return"text-slate-300 bg-slate-700 border border-slate-600"}},f=e=>{if(!e)return null;try{if("object"==typeof e)return e;if("string"==typeof e)return JSON.parse(e);return e}catch{return e}},v=e=>{let s=f(e);return s?"string"==typeof s?s:"object"==typeof s?"CREDIT"===s.type||"DEBIT"===s.type?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Type:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.type)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Amount:"})," ",(0,a.jsxs)("span",{className:"text-green-400",children:["$",String(s.amount)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Reason:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.reason)})]}),s.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Description:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.description)})]})]}):s.targetUser&&"object"==typeof s.targetUser?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Type:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.type)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Amount:"})," ",(0,a.jsxs)("span",{className:"text-green-400",children:["$",String(s.amount)]})]}),s.reason&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Reason:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.reason)})]}),s.description&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Description:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.description)})]}),void 0!==s.previousBalance&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Previous Balance:"})," ",(0,a.jsxs)("span",{className:"text-slate-300",children:["$",String(s.previousBalance)]})]}),void 0!==s.newBalance&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"New Balance:"})," ",(0,a.jsxs)("span",{className:"text-slate-300",children:["$",String(s.newBalance)]})]})]}):s.targetUserId||s.targetUserEmail?(0,a.jsxs)("div",{className:"space-y-1",children:[s.targetUserEmail&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"User:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.targetUserEmail)})]}),s.targetUserId&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"User ID:"})," ",(0,a.jsx)("span",{className:"text-slate-300",children:String(s.targetUserId)})]}),s.amount&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Amount:"})," ",(0,a.jsxs)("span",{className:"text-green-400",children:["$",String(s.amount)]})]}),s.reason&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-slate-400",children:"Reason:"})," ",(0,a.jsx)("span",{className:"text-white",children:String(s.reason)})]})]}):(0,a.jsx)("div",{className:"space-y-1",children:Object.entries(s).map(([e,s])=>{let t;if("targetUser"===e)return null;if(null==s)t="N/A";else if("object"==typeof s)try{t=JSON.stringify(s,null,2)}catch{t="[Complex Object]"}else t=String(s);return(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-slate-400 capitalize",children:[e.replace(/([A-Z])/g," $1").toLowerCase(),":"]})," ",(0,a.jsx)("span",{className:"text-white whitespace-pre-wrap",children:t})]},e)}).filter(Boolean)}):String(s):"No details available"};return t?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-64 bg-slate-700 rounded"})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(P.$n,{onClick:g,variant:"outline",className:"flex items-center gap-2 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white",children:[(0,a.jsx)(eo,{className:"h-4 w-4"}),"Export Logs"]})}),(0,a.jsx)(P.Zp,{className:"bg-slate-800 border-slate-700",children:(0,a.jsx)(P.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,a.jsx)(P.pd,{placeholder:"Search logs...",value:i,onChange:e=>n(e.target.value),className:"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Actions"}),(0,a.jsx)("option",{value:"USER_LOGIN",children:"User Login"}),(0,a.jsx)("option",{value:"USER_LOGOUT",children:"User Logout"}),(0,a.jsx)("option",{value:"USER_REGISTERED",children:"User Registration"}),(0,a.jsx)("option",{value:"MINING_UNIT_PURCHASED",children:"Mining Purchase"}),(0,a.jsx)("option",{value:"WITHDRAWAL_REQUESTED",children:"Withdrawal Request"}),(0,a.jsx)("option",{value:"WITHDRAWAL_APPROVED",children:"Withdrawal Approved"}),(0,a.jsx)("option",{value:"WITHDRAWAL_REJECTED",children:"Withdrawal Rejected"}),(0,a.jsx)("option",{value:"DEPOSIT_VERIFICATION_ATTEMPT",children:"Deposit Verification"}),(0,a.jsx)("option",{value:"DEPOSIT_CONFIRMED",children:"Deposit Confirmed"}),(0,a.jsx)("option",{value:"KYC_SUBMITTED",children:"KYC Submission"}),(0,a.jsx)("option",{value:"KYC_APPROVED",children:"KYC Approval"}),(0,a.jsx)("option",{value:"KYC_REJECTED",children:"KYC Rejection"}),(0,a.jsx)("option",{value:"BINARY_MATCHING_PROCESSED",children:"Binary Matching"}),(0,a.jsx)("option",{value:"MANUAL_BINARY_MATCHING_TRIGGERED",children:"Manual Binary Matching"}),(0,a.jsx)("option",{value:"ADMIN_ACTION",children:"Admin Actions"}),(0,a.jsx)("option",{value:"WALLET_ADJUSTMENT",children:"Wallet Adjustments"}),(0,a.jsx)("option",{value:"LOGIN_FAILED",children:"Failed Logins"}),(0,a.jsx)("option",{value:"CLIENT_ERROR",children:"Client Errors"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("select",{value:x,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"today",children:"Today"}),(0,a.jsx)("option",{value:"week",children:"This Week"}),(0,a.jsx)("option",{value:"month",children:"This Month"}),(0,a.jsx)("option",{value:"all",children:"All Time"})]})}),(0,a.jsxs)("div",{className:"text-sm text-slate-400 flex items-center",children:[(0,a.jsx)(eh,{className:"h-4 w-4 mr-1"}),e.length," logs found"]})]})})}),(0,a.jsxs)(P.Zp,{className:"bg-slate-800 border-slate-700",children:[(0,a.jsx)(P.aR,{children:(0,a.jsxs)(P.ZB,{className:"flex items-center gap-2 text-white",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-blue-400"}),"Activity Logs"]})}),(0,a.jsxs)(P.Wu,{children:[(0,a.jsx)("div",{className:"space-y-2",children:0===e.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(y.A,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No Logs Found"}),(0,a.jsx)("p",{className:"text-slate-400",children:"No activity logs match your current filters."})]}):e.map(e=>(0,a.jsxs)("div",{className:"bg-slate-700 rounded-lg border border-slate-600 hover:border-slate-500 transition-all duration-200 hover:shadow-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-slate-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-full bg-slate-600",children:N(e.action)}),(0,a.jsx)("div",{children:(0,a.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${b(e.action)}`,children:e.action.replace(/_/g," ")})})]}),(0,a.jsx)("div",{className:"text-sm text-slate-400",children:(0,L.r6)(e.createdAt)})]}),(0,a.jsxs)("div",{className:"p-4 space-y-3",children:[e.user&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-slate-800 rounded-lg",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsxs)("span",{className:"text-slate-400 ml-2 text-sm",children:["(",e.user.email,")"]})]})]}),e.details&&(0,a.jsxs)("div",{className:"p-3 bg-slate-800 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-slate-300 mb-2",children:"Details:"}),(0,a.jsx)("div",{className:"text-sm",children:v(e.details)})]}),(e.ipAddress||e.userAgent)&&(0,a.jsx)("div",{className:"p-2 bg-slate-800 rounded-lg",children:(0,a.jsxs)("div",{className:"text-xs text-slate-400 space-y-1",children:[e.ipAddress&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"IP Address:"}),(0,a.jsx)("span",{className:"text-slate-300",children:e.ipAddress})]}),e.userAgent&&(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)("span",{className:"font-medium whitespace-nowrap",children:"User Agent:"}),(0,a.jsx)("span",{className:"text-slate-300 break-all",children:e.userAgent})]})]})})]})]},e.id))}),u>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-6 border-t border-slate-600",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-400",children:["Page ",m," of ",u]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(P.$n,{variant:"outline",size:"sm",onClick:()=>h(e=>Math.max(1,e-1)),disabled:1===m,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50",children:"Previous"}),(0,a.jsx)(P.$n,{variant:"outline",size:"sm",onClick:()=>h(e=>Math.min(u,e+1)),disabled:m===u,className:"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white disabled:opacity-50",children:"Next"})]})]})]})]})]})};function eE(){let[e,s]=(0,l.useState)("dashboard");return(0,a.jsx)(T,{activeTab:e,onTabChange:s,children:(()=>{switch(e){case"dashboard":default:return(0,a.jsx)($,{onTabChange:s});case"users":return(0,a.jsx)(K,{});case"kyc":return(0,a.jsx)(ee,{});case"deposits":return(0,a.jsx)(er,{});case"withdrawals":return(0,a.jsx)(ei,{});case"support":return(0,a.jsx)(ed,{});case"binary-points":return(0,a.jsx)(eu,{});case"referral-commissions":return(0,a.jsx)(eN,{});case"email-settings":return(0,a.jsx)(ev,{});case"settings":return(0,a.jsx)(eC,{});case"logs":return(0,a.jsx)(eS,{})}})()})}function ek(){return(0,a.jsx)(r.q_,{requireAuth:!0,requireAdmin:!0,children:(0,a.jsx)(eE,{})})}},70334:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},79551:e=>{"use strict";e.exports=require("url")},93508:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},97760:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},97840:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,1771,7634,4987,5494,6679],()=>t(28228));module.exports=a})();