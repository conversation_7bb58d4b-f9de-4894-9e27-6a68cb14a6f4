"use strict";(()=>{var e={};e.id=868,e.ids=[868],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>k,DY:()=>f,Lx:()=>w,Oj:()=>y,b9:()=>h,qc:()=>g});var s=t(85663),a=t(43205),n=t.n(a),i=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await s.Ay.hash(e,12),d=async(e,r)=>await s.Ay.compare(e,r),c=e=>n().sign(e,o,{expiresIn:u}),p=e=>{try{return n().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let s=await i.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},f=async e=>{let r,s;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await i.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await l(e.password),n=!1;do s=m(),n=!await i.Gy.findByReferralId(s);while(!n);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let r=await i.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>{let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||r.push("Password must contain at least one special character"),{valid:0===r.length,errors:r}},k=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await i.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},90742:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(12909),l=t(31183),d=t(6710);async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{userIds:s,action:a,rejectionReason:n}=await e.json();if(!s||!Array.isArray(s)||0===s.length)return o.NextResponse.json({success:!1,error:"User IDs array is required"},{status:400});if(!a||!["APPROVE","REJECT"].includes(a))return o.NextResponse.json({success:!1,error:"Invalid action"},{status:400});if("REJECT"===a&&!n)return o.NextResponse.json({success:!1,error:"Rejection reason is required"},{status:400});let i="APPROVE"===a?"APPROVED":"REJECTED",c=new Date,p=[];for(let r of s)try{let s=await l.prisma.kYCDocument.findMany({where:{userId:r,status:"PENDING"}});if(0===s.length){p.push({userId:r,success:!1,error:"No pending documents found"});continue}await l.prisma.kYCDocument.updateMany({where:{userId:r,status:"PENDING"},data:{status:i,reviewedAt:c,reviewedBy:t.email,rejectionReason:"REJECT"===a?n:null}}),await l.prisma.user.update({where:{id:r},data:{kycStatus:i}}),await d.AJ.create({action:"KYC_BULK_REVIEWED",adminId:t.id,details:{reviewedUserId:r,action:a,rejectionReason:"REJECT"===a?n:null,documentsCount:s.length,reviewedBy:t.email,bulkOperation:!0},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),p.push({userId:r,success:!0,status:i})}catch(e){console.error(`Error processing user ${r}:`,e),p.push({userId:r,success:!1,error:"Processing failed"})}let m=p.filter(e=>e.success).length,h=p.filter(e=>!e.success).length;return o.NextResponse.json({success:!0,message:`Bulk operation completed. ${m} successful, ${h} failed.`,data:{action:a,totalProcessed:s.length,successCount:m,failureCount:h,results:p}})}catch(e){return console.error("Bulk KYC operation error:",e),o.NextResponse.json({success:!1,error:"Failed to perform bulk operation"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/kyc/bulk/route",pathname:"/api/admin/kyc/bulk",filename:"route",bundlePath:"app/api/admin/kyc/bulk/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\bulk\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:f}=p;function w(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3306],()=>t(90742));module.exports=s})();