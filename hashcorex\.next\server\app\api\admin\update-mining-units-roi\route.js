"use strict";(()=>{var e={};e.id=6409,e.ids=[6409],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,a)=>{a.d(t,{DT:()=>h,DY:()=>f,Lx:()=>w,Oj:()=>y,b9:()=>g,qc:()=>E});var n=a(85663),r=a(43205),i=a.n(r),s=a(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",u=async e=>await n.Ay.hash(e,12),d=async(e,t)=>await n.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:l}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let a=0;a<8;a++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},g=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let a=m(t);if(!a)return{authenticated:!1,user:null};let n=await s.Gy.findByEmail(a.email);return n?{authenticated:!0,user:n}:{authenticated:!1,user:null}},f=async e=>{let t,n;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let a=await s.Gy.findByReferralId(e.referralCode);if(!a)throw Error("Invalid referral code");t=a.id}let r=await u(e.password),i=!1;do n=p(),i=!await s.Gy.findByReferralId(n);while(!i);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:r,referralId:n});if(t){let{placeUserByReferralType:n}=await a.e(2746).then(a.bind(a,2746)),r="general";"left"===e.placementSide?r="left":"right"===e.placementSide&&(r="right"),await n(t,o.id,r)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},h=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),E=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39794:(e,t,a)=>{a.d(t,{Py:()=>s,k8:()=>u,kp:()=>l});var n=a(31183),r=a(6710);async function i(e){return await n.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,t,a,s,l){let u=await i(e);if(0===u.length)throw Error("No active mining units found for earnings allocation");let d=[],c=t;for(let e of u){var m;if(c<=0)break;let t=Math.max(0,5*(m=e).investmentAmount-(m.miningEarnings+m.referralEarnings+m.binaryEarnings));if(t<=0)continue;let r=Math.min(c,t);if(r>0){let i={};switch(a){case"MINING_EARNINGS":i.miningEarnings={increment:r};break;case"DIRECT_REFERRAL":i.referralEarnings={increment:r};break;case"BINARY_BONUS":i.binaryEarnings={increment:r}}i.totalEarned={increment:r},await n.prisma.miningUnit.update({where:{id:e.id},data:i}),await n.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:a,amount:r,description:l}}),d.push({miningUnitId:e.id,amount:r,remainingCapacity:t-r}),c-=r;let u=await n.prisma.miningUnit.findUnique({where:{id:e.id}});u&&function(e){let t=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=t}(u)&&await o(e.id,"5x_investment_reached")}}return c>0&&(console.warn(`Unable to allocate ${c} to mining units - all units at capacity`),await r.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:t,allocatedAmount:t-c,overflowAmount:c,earningType:a,reason:"all_units_at_capacity"}})),d}async function o(e,t){let a=await n.prisma.miningUnit.findUnique({where:{id:e}});if(!a)throw Error(`Mining unit ${e} not found`);await n.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let i=a.miningEarnings+a.referralEarnings+a.binaryEarnings;await r.AJ.create({action:"MINING_UNIT_EXPIRED",userId:a.userId,details:{miningUnitId:e,reason:t,totalEarned:i,miningEarnings:a.miningEarnings,referralEarnings:a.referralEarnings,binaryEarnings:a.binaryEarnings,investmentAmount:a.investmentAmount,multiplier:i/a.investmentAmount}}),console.log(`Mining unit ${e} expired due to ${t}. Total earnings: ${i}`)}async function l(e){return await n.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function u(e){return await n.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66839:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var n={};a.r(n),a.d(n,{POST:()=>c});var r=a(96559),i=a(48088),s=a(37719),o=a(32190),l=a(12909),u=a(92731),d=a(6710);async function c(e){try{let{authenticated:t,user:a}=await (0,l.b9)(e);if(!t||!a)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==a.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});console.log(`Mining units ROI update initiated by admin: ${a.email}`);let n=await (0,u.s2)();return await d.AJ.create({action:"MANUAL_MINING_UNITS_ROI_UPDATE_TRIGGERED",details:{triggeredBy:a.email,adminId:a.id,result:n,timestamp:new Date().toISOString()}}),console.log(`Mining units ROI update completed. Updated ${n.unitsUpdated} out of ${n.totalUnits} units`),o.NextResponse.json({success:!0,message:"Mining units ROI update completed successfully",data:{unitsUpdated:n.unitsUpdated,totalUnits:n.totalUnits,updateResults:n.updateResults}})}catch(t){console.error("Mining units ROI update error:",t);try{let{authenticated:a,user:n}=await (0,l.b9)(e);await d.AJ.create({action:"MANUAL_MINING_UNITS_ROI_UPDATE_ERROR",details:{triggeredBy:n?.email||"unknown",error:t.message,timestamp:new Date().toISOString()}})}catch(e){console.error("Failed to log mining units ROI update error:",e)}return o.NextResponse.json({success:!1,error:"Failed to update mining units ROI"},{status:500})}}let m=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/update-mining-units-roi/route",pathname:"/api/admin/update-mining-units-roi",filename:"route",bundlePath:"app/api/admin/update-mining-units-roi/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\update-mining-units-roi\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:f}=m;function w(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[4447,580,5315,3306,5112],()=>a(66839));module.exports=n})();