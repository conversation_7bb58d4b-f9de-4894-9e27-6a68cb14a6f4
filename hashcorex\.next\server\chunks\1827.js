"use strict";exports.id=1827,exports.ids=[1827],exports.modules={1827:(t,e,i)=>{i.d(e,{j:()=>l});var a=i(6710),o=i(31183),n=i(59480);let r=new Map,s=new Map;class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}async start(){if(this.isRunning)return void console.log("Deposit verification service is already running");this.isRunning=!0,console.log("Starting deposit verification service..."),await this.processPendingVerifications(),await this.processWaitingForConfirmations(),console.log("Deposit verification service started successfully")}stop(){this.isRunning=!1,s.forEach(t=>{clearTimeout(t)}),s.clear(),r.clear(),console.log("Deposit verification service stopped")}async processPendingVerifications(){try{let t=await a.J6.getPendingVerificationDeposits();for(let e of(console.log(`Found ${t.length} deposits pending verification`),t))r.has(e.transactionId)||this.scheduleVerification(e.transactionId,e.tronAddress)}catch(t){console.error("Error processing pending verifications:",t),await a.AJ.create({action:"DEPOSIT_VERIFICATION_ERROR",details:`Error processing pending verifications: ${t instanceof Error?t.message:"Unknown error"}`})}}async processWaitingForConfirmations(){try{let t=await a.J6.getWaitingForConfirmationsDeposits();for(let e of(console.log(`Found ${t.length} deposits waiting for confirmations`),t))s.has(e.transactionId)||this.scheduleConfirmationCheck(e.transactionId,e.tronAddress)}catch(t){console.error("Error processing waiting for confirmations:",t),await a.AJ.create({action:"CONFIRMATION_CHECK_ERROR",details:`Error processing waiting for confirmations: ${t instanceof Error?t.message:"Unknown error"}`})}}scheduleVerification(t,e){r.has(t)||(r.set(t,!0),console.log(`Scheduling verification for transaction: ${t}`),this.verifyTransaction(t,e,!1),setTimeout(()=>{this.verifyTransaction(t,e,!0)},6e4))}async verifyTransaction(t,e,i){try{console.log(`${i?"Retrying":"Attempting"} verification for transaction: ${t}`);let s=await a.rs.get("usdtDepositAddress");s||(s=await a.rs.get("USDT_DEPOSIT_ADDRESS")),s&&(s=s.replace(/['"]/g,"").trim());let c=s||e;console.log(`Using deposit address for verification: ${c} (from admin settings: ${!!s})`);let l=parseInt(await a.rs.get("minConfirmations")||"10"),d=parseFloat(await a.rs.get("minDepositAmount")||"10"),u=parseFloat(await a.rs.get("maxDepositAmount")||"10000"),f=(0,n.gp)(t,c,1),p=new Promise((t,e)=>setTimeout(()=>e(Error("Verification timeout")),3e4)),m=await Promise.race([f,p]);if(!m.isValid&&0===m.confirmations){i&&(await a.J6.updateStatus(t,"FAILED",{failureReason:"Transaction not found or invalid after verification attempts",processedAt:new Date}),await a.AJ.create({action:"DEPOSIT_VERIFICATION_FAILED",details:`Transaction ${t} failed verification after retry`}),r.delete(t));return}if(!(m.toAddress.toLowerCase().includes(c.toLowerCase().slice(1,10))||c.toLowerCase().includes(m.toAddress.toLowerCase().slice(1,10)))){await a.J6.updateStatus(t,"FAILED",{failureReason:"Invalid recipient address",processedAt:new Date}),r.delete(t);return}if(m.amount<d){await a.J6.updateStatus(t,"FAILED",{failureReason:`Deposit amount ${m.amount} USDT is below minimum ${d} USDT`,processedAt:new Date}),r.delete(t);return}if(m.amount>u){await a.J6.updateStatus(t,"FAILED",{failureReason:`Deposit amount ${m.amount} USDT exceeds maximum ${u} USDT`,processedAt:new Date}),r.delete(t);return}await o.prisma.depositTransaction.update({where:{transactionId:t},data:{amount:m.amount,usdtAmount:m.amount,senderAddress:m.fromAddress,blockNumber:m.blockNumber.toString(),blockTimestamp:new Date(m.blockTimestamp),confirmations:m.confirmations,tronAddress:c}}),await a.J6.updateStatus(t,"PENDING",{confirmations:m.confirmations}),console.log(`Transaction ${t} verified with ${m.confirmations} confirmations (required: ${l})`),m.confirmations>=l?await this.completeDeposit(t,m.amount):(await a.J6.updateStatus(t,"WAITING_FOR_CONFIRMATIONS"),this.scheduleConfirmationCheck(t,c)),r.delete(t)}catch(n){console.error(`Error verifying transaction ${t}:`,n);let e=n instanceof Error?n.message:"Unknown error",o=e.includes("timeout")||e.includes("network")||e.includes("ECONNRESET");i||!o?(await a.J6.updateStatus(t,"FAILED",{failureReason:`Verification error: ${e}`,processedAt:new Date}),r.delete(t),await a.AJ.create({action:"DEPOSIT_VERIFICATION_FAILED",details:`Transaction ${t} failed verification: ${e}`})):await a.AJ.create({action:"DEPOSIT_VERIFICATION_NETWORK_ERROR",details:`Network error verifying transaction ${t}: ${e}. Will retry.`})}}scheduleConfirmationCheck(t,e){if(s.has(t))return;console.log(`Starting confirmation checking for transaction: ${t}`);let i=setInterval(async()=>{await this.checkConfirmations(t,e)},6e4);s.set(t,i),this.checkConfirmations(t,e)}async checkConfirmations(t,e){try{console.log(`Checking confirmations for transaction: ${t}`);let i=await a.rs.get("usdtDepositAddress");i||(i=await a.rs.get("USDT_DEPOSIT_ADDRESS")),i&&(i=i.replace(/['"]/g,"").trim());let o=i||e,r=parseInt(await a.rs.get("minConfirmations")||"10"),c=await (0,n.gp)(t,o,1);if(!c.isValid)return void console.log(`Transaction ${t} is no longer valid during confirmation check`);if(await a.J6.updateConfirmations(t,c.confirmations),console.log(`Transaction ${t} has ${c.confirmations} confirmations (required: ${r})`),c.confirmations>=r){await this.completeDeposit(t,c.amount);let e=s.get(t);e&&(clearInterval(e),s.delete(t))}}catch(e){console.error(`Error checking confirmations for transaction ${t}:`,e),await a.AJ.create({action:"CONFIRMATION_CHECK_ERROR",details:`Error checking confirmations for ${t}: ${e instanceof Error?e.message:"Unknown error"}`})}}async completeDeposit(t,e){try{console.log(`Completing deposit for transaction: ${t} with amount: ${e}`);let i=await a.J6.findByTransactionId(t);if(!i)throw Error("Deposit record not found");if("CONFIRMED"===i.status||"COMPLETED"===i.status)return void console.log(`Deposit ${t} already completed, skipping...`);await o.prisma.$transaction(async a=>{await a.depositTransaction.update({where:{transactionId:t},data:{status:"CONFIRMED",verifiedAt:new Date,processedAt:new Date}});let o=await a.walletBalance.findUnique({where:{userId:i.userId}});o?await a.walletBalance.update({where:{userId:i.userId},data:{availableBalance:o.availableBalance+e,totalDeposits:o.totalDeposits+e,lastUpdated:new Date}}):await a.walletBalance.create({data:{userId:i.userId,availableBalance:e,pendingBalance:0,totalDeposits:e,totalWithdrawals:0,totalEarnings:0}});let n=await a.transaction.findFirst({where:{userId:i.userId,type:"DEPOSIT",description:`USDT TRC20 Deposit - TX: ${t}`,status:"PENDING"}});n?await a.transaction.update({where:{id:n.id},data:{status:"COMPLETED",amount:e}}):await a.transaction.create({data:{userId:i.userId,type:"DEPOSIT",amount:e,description:`USDT TRC20 Deposit - TX: ${t}`,status:"COMPLETED"}})}),await a.AJ.create({action:"DEPOSIT_COMPLETED",userId:i.userId,details:`Deposit completed: ${e} USDT from transaction ${t}`}),console.log(`Deposit completed successfully for transaction: ${t}`)}catch(e){console.error(`Error completing deposit for transaction ${t}:`,e),await a.J6.updateStatus(t,"FAILED",{failureReason:`Completion error: ${e instanceof Error?e.message:"Unknown error"}`,processedAt:new Date}),await a.AJ.create({action:"DEPOSIT_COMPLETION_ERROR",details:`Error completing deposit ${t}: ${e instanceof Error?e.message:"Unknown error"}`})}}async addTransactionForVerification(t,e){this.isRunning||console.log("Deposit verification service is not running, starting verification manually"),this.scheduleVerification(t,e)}getStatus(){return{isRunning:this.isRunning,activeVerifications:r.size,confirmationChecks:s.size}}constructor(){this.isRunning=!1}}let l=c.getInstance()}};