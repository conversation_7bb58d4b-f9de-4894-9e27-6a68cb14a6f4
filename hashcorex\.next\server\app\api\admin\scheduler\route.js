"use strict";(()=>{var e={};e.id=2329,e.ids=[2329],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>E,DY:()=>y,Lx:()=>m,Oj:()=>w,b9:()=>g,qc:()=>f});var s=r(85663),a=r(43205),n=r.n(a),i=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",c=async e=>await s.Ay.hash(e,12),u=async(e,t)=>await s.Ay.compare(e,t),d=e=>n().sign(e,o,{expiresIn:l}),h=e=>{try{return n().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},g=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=h(t);if(!r)return{authenticated:!1,user:null};let s=await i.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},y=async e=>{let t,s;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await i.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await c(e.password),n=!1;do s=p(),n=!await i.Gy.findByReferralId(s);while(!n);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},m=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:d({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},E=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),f=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51134:(e,t,r)=>{r.d(t,{c:()=>o});var s=r(92731),a=r(2746),n=r(6710);class i{constructor(){this.tasks=new Map,this.intervals=new Map,this.isInitialized=!1,this.setupTasks()}setupTasks(){this.addTask({id:"daily-roi",name:"Daily ROI Calculation",cronExpression:"0 1 0 * * *",handler:this.handleDailyROI.bind(this),isRunning:!1}),this.addTask({id:"weekly-payout",name:"Weekly Earnings Payout",cronExpression:"0 30 18 * * 0",handler:this.handleWeeklyPayout.bind(this),isRunning:!1}),this.addTask({id:"binary-matching",name:"Binary Matching",cronExpression:"0 30 9 * * 6",handler:this.handleBinaryMatching.bind(this),isRunning:!1})}addTask(e){this.tasks.set(e.id,e)}async handleDailyROI(){try{console.log("\uD83D\uDD04 Starting scheduled daily ROI calculation...");let e=await (0,s.eB)();console.log(`⏰ Expired ${e} old mining units`);let t=await (0,s.WL)();console.log(`💰 Processed ${t.length} users for daily ROI`);let r=t.reduce((e,t)=>e+t.totalEarnings,0);await n.AJ.create({action:"SCHEDULED_DAILY_ROI_EXECUTED",details:{usersProcessed:t.length,totalEarnings:r,expiredUnits:e,executionTime:new Date().toISOString(),scheduler:"server-side"}}),console.log(`✅ Daily ROI calculation completed. Total earnings: $${r.toFixed(2)}`)}catch(e){console.error("❌ Error in daily ROI calculation:",e),await n.AJ.create({action:"SCHEDULED_DAILY_ROI_ERROR",details:{error:e instanceof Error?e.message:"Unknown error",executionTime:new Date().toISOString(),scheduler:"server-side"}})}}async handleWeeklyPayout(){try{console.log("\uD83D\uDD04 Starting scheduled weekly earnings payout...");let e=await (0,s.Oh)(),t=e.reduce((e,t)=>e+t.totalEarnings,0);await n.AJ.create({action:"SCHEDULED_WEEKLY_PAYOUT_EXECUTED",details:{usersProcessed:e.length,totalDistributed:t,executionTime:new Date().toISOString(),payoutTime:"Sunday 00:00 AM GMT+5:30",scheduler:"server-side"}}),console.log(`✅ Weekly payout completed. Distributed: $${t.toFixed(2)} to ${e.length} users`)}catch(e){console.error("❌ Error in weekly payout:",e),await n.AJ.create({action:"SCHEDULED_WEEKLY_PAYOUT_ERROR",details:{error:e instanceof Error?e.message:"Unknown error",executionTime:new Date().toISOString(),scheduler:"server-side"}})}}async handleBinaryMatching(){try{console.log("\uD83D\uDD04 Starting scheduled binary matching...");let e=await (0,a.E5)(),t=e.reduce((e,t)=>e+t.payout,0),r=e.reduce((e,t)=>e+t.matchedPoints,0);await n.AJ.create({action:"SCHEDULED_BINARY_MATCHING_EXECUTED",details:{usersProcessed:e.length,totalPayouts:t,totalMatchedPoints:r,executionTime:new Date().toISOString(),matchingTime:"Saturday 15:00 GMT+5:30",scheduler:"server-side"}}),console.log(`✅ Binary matching completed. Payouts: $${t.toFixed(2)}, Matched points: ${r}`)}catch(e){console.error("❌ Error in binary matching:",e),await n.AJ.create({action:"SCHEDULED_BINARY_MATCHING_ERROR",details:{error:e instanceof Error?e.message:"Unknown error",executionTime:new Date().toISOString(),scheduler:"server-side"}})}}parseCronExpression(e){let t=e.split(" ");if(6!==t.length)throw Error("Invalid cron expression format");let[r,s,a,,,n]=t.map(e=>"*"===e?-1:parseInt(e)),i=new Date,o=new Date;if(-1!==a&&o.setUTCHours(a),-1!==s&&o.setUTCMinutes(s),-1!==r&&o.setUTCSeconds(r),o.setUTCMilliseconds(0),-1!==n){let e=(n-o.getUTCDay()+7)%7;0===e&&o<=i?o.setUTCDate(o.getUTCDate()+7):e>0&&o.setUTCDate(o.getUTCDate()+e)}else o<=i&&o.setUTCDate(o.getUTCDate()+1);return o.getTime()-i.getTime()}scheduleTask(e){try{let t=this.parseCronExpression(e.cronExpression);e.nextRun=new Date(Date.now()+t),console.log(`📅 Scheduled ${e.name} to run at ${e.nextRun.toISOString()}`);let r=setTimeout(async()=>{if(!e.isRunning){e.isRunning=!0,e.lastRun=new Date;try{await e.handler()}finally{e.isRunning=!1,this.scheduleTask(e)}}},t),s=this.intervals.get(e.id);s&&clearTimeout(s),this.intervals.set(e.id,r)}catch(t){console.error(`❌ Error scheduling task ${e.name}:`,t)}}start(){if(this.isInitialized)return void console.log("⚠️ Scheduler already initialized");for(let e of(console.log("\uD83D\uDE80 Starting server-side scheduler..."),this.tasks.values()))this.scheduleTask(e);this.isInitialized=!0,console.log(`✅ Scheduler initialized with ${this.tasks.size} tasks`)}stop(){for(let e of(console.log("\uD83D\uDED1 Stopping server-side scheduler..."),this.intervals.values()))clearTimeout(e);this.intervals.clear(),this.isInitialized=!1,console.log("✅ Scheduler stopped")}getTaskStatus(){let e=[];for(let t of this.tasks.values())e.push({id:t.id,name:t.name,cronExpression:t.cronExpression,lastRun:t.lastRun,nextRun:t.nextRun,isRunning:t.isRunning});return e}async runTaskManually(e){let t=this.tasks.get(e);if(!t)throw Error(`Task ${e} not found`);if(t.isRunning)throw Error(`Task ${e} is already running`);console.log(`🔧 Manually running task: ${t.name}`),t.isRunning=!0;try{await t.handler(),t.lastRun=new Date}finally{t.isRunning=!1}}}let o=new i},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78629:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>h,serverHooks:()=>y,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>d});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),l=r(12909),c=r(51134);async function u(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=c.c.getTaskStatus();return o.NextResponse.json({success:!0,data:{tasks:s,serverTime:new Date().toISOString()}})}catch(e){return console.error("Error getting scheduler status:",e),o.NextResponse.json({success:!1,error:"Failed to get scheduler status"},{status:500})}}async function d(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{taskId:s}=await e.json();if(!s)return o.NextResponse.json({success:!1,error:"Task ID is required"},{status:400});return await c.c.runTaskManually(s),o.NextResponse.json({success:!0,message:`Task ${s} executed successfully`})}catch(e){return console.error("Error running task manually:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Failed to run task"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/scheduler/route",pathname:"/api/admin/scheduler",filename:"route",bundlePath:"app/api/admin/scheduler/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\scheduler\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:y}=h;function m(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306,2746,5112],()=>r(78629));module.exports=s})();