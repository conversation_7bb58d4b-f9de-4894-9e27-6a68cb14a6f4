"use strict";(()=>{var e={};e.id=936,e.ids=[936],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37023:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>A,routeModule:()=>_,serverHooks:()=>I,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>l});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),p=r(6710);async function u(e){try{console.log("\uD83E\uDDEA Testing binary settings retrieval...");let e=await p.rs.get("MAX_BINARY_POINTS_PER_SIDE");console.log(`Raw database value: "${e}" (type: ${typeof e})`);let t=parseFloat(e||"10");console.log(`Parsed value: ${t} (type: ${typeof t})`);let r=await p.rs.getAll(),s={};return r.forEach(e=>{try{s[e.key]=JSON.parse(e.value)}catch{s[e.key]=e.value}}),s.MAX_BINARY_POINTS_PER_SIDE&&(s.maxBinaryPointsPerSide=parseFloat(s.MAX_BINARY_POINTS_PER_SIDE)),console.log("Settings object:",{raw:s.MAX_BINARY_POINTS_PER_SIDE,mapped:s.maxBinaryPointsPerSide}),i.NextResponse.json({success:!0,data:{rawDatabaseValue:e,parsedValue:t,settingsObjectRaw:s.MAX_BINARY_POINTS_PER_SIDE,settingsObjectMapped:s.maxBinaryPointsPerSide,allBinarySettings:{MAX_BINARY_POINTS_PER_SIDE:s.MAX_BINARY_POINTS_PER_SIDE,BINARY_POINT_VALUE:s.BINARY_POINT_VALUE,BINARY_MATCHING_ENABLED:s.BINARY_MATCHING_ENABLED,BINARY_MATCHING_SCHEDULE:s.BINARY_MATCHING_SCHEDULE}}})}catch(e){return console.error("Test error:",e),i.NextResponse.json({success:!1,error:"Test failed"},{status:500})}}async function l(e){try{let{testValue:t}=await e.json();console.log(`🧪 Testing binary settings update with value: ${t}`),await p.rs.set("MAX_BINARY_POINTS_PER_SIDE",String(t));let r=await p.rs.get("MAX_BINARY_POINTS_PER_SIDE");return console.log(`Updated value: "${r}"`),i.NextResponse.json({success:!0,data:{originalValue:t,storedValue:r,parsedValue:parseFloat(r||"10")}})}catch(e){return console.error("Test update error:",e),i.NextResponse.json({success:!1,error:"Test update failed"},{status:500})}}let _=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test/binary-settings/route",pathname:"/api/test/binary-settings",filename:"route",bundlePath:"app/api/test/binary-settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\test\\binary-settings\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:I}=_;function A(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,3306],()=>r(37023));module.exports=s})();