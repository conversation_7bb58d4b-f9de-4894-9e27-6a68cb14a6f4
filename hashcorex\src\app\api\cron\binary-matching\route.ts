import { NextRequest, NextResponse } from 'next/server';
import { processBinaryMatching } from '@/lib/referral';
import { systemLogDb } from '@/lib/database';

// This endpoint should be called weekly at 15:00 UTC (same time as weekly payout)
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron service)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'default-secret';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Starting binary matching cron job...');

    // Process binary matching
    const result = await processBinaryMatching();

    const totalPayouts = result.matchingResults.reduce((sum, result) => sum + result.payout, 0);
    const totalMatchedPoints = result.matchingResults.reduce((sum, result) => sum + result.matchedPoints, 0);

    // Log the cron job execution
    await systemLogDb.create({
      action: 'BINARY_MATCHING_CRON_EXECUTED',
      details: {
        usersProcessed: result.usersProcessed,
        totalPayouts,
        totalMatchedPoints,
        executionTime: new Date().toISOString(),
        matchingTime: '15:00 UTC (Weekly)',
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Binary matching completed',
      data: {
        usersProcessed: result.usersProcessed,
        totalPayouts,
        totalMatchedPoints,
      },
    });

  } catch (error: any) {
    console.error('Binary matching cron job error:', error);

    // Log the error
    await systemLogDb.create({
      action: 'BINARY_MATCHING_CRON_ERROR',
      details: {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json(
      { success: false, error: 'Binary matching failed' },
      { status: 500 }
    );
  }
}
