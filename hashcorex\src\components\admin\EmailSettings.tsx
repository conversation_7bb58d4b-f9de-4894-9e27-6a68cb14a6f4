'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Loading } from '@/components/ui';
import { Mail, Send, Settings, CheckCircle, XCircle, Eye, EyeOff } from 'lucide-react';

interface SMTPConfig {
  id?: string;
  host: string;
  port: number;
  secure: boolean;
  username: string;
  password: string;
  senderName: string;
  senderEmail: string;
  isActive: boolean;
}

interface EmailStats {
  totalSent: number;
  totalFailed: number;
  recentEmails: any[];
}

export const EmailSettings: React.FC = () => {
  const [smtpConfig, setSMTPConfig] = useState<SMTPConfig>({
    host: '',
    port: 587,
    secure: false,
    username: '',
    password: '',
    senderName: '',
    senderEmail: '',
    isActive: true,
  });

  const [emailStats, setEmailStats] = useState<EmailStats>({
    totalSent: 0,
    totalFailed: 0,
    recentEmails: [],
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  useEffect(() => {
    fetchSMTPConfig();
    fetchEmailStats();
  }, []);

  const fetchSMTPConfig = async () => {
    try {
      const response = await fetch('/api/admin/email/smtp', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setSMTPConfig(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching SMTP config:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEmailStats = async () => {
    try {
      const response = await fetch('/api/admin/email/stats', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setEmailStats(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching email stats:', error);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/admin/email/smtp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(smtpConfig),
      });

      const data = await response.json();

      if (data.success) {
        setTestResult({ success: true, message: 'SMTP configuration saved successfully!' });
        await fetchSMTPConfig();
      } else {
        setTestResult({ success: false, message: data.error || 'Failed to save configuration' });
      }
    } catch (error) {
      setTestResult({ success: false, message: 'Failed to save configuration' });
    } finally {
      setSaving(false);
    }
  };

  const handleTestConnection = async () => {
    setTesting(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/admin/email/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(smtpConfig),
      });

      const data = await response.json();
      setTestResult({
        success: data.success,
        message: data.success ? 'Connection test successful!' : data.error || 'Connection test failed',
      });
    } catch (error) {
      setTestResult({ success: false, message: 'Connection test failed' });
    } finally {
      setTesting(false);
    }
  };

  const handleInputChange = (field: keyof SMTPConfig, value: any) => {
    setSMTPConfig(prev => ({ ...prev, [field]: value }));
    setTestResult(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loading size="lg" text="Loading email settings..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Email Settings</h2>
          <p className="text-gray-600">Configure SMTP settings and manage email templates</p>
        </div>
        <div className="flex items-center space-x-2">
          <Mail className="h-6 w-6 text-blue-600" />
        </div>
      </div>

      {/* Email Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Emails Sent</p>
              <p className="text-2xl font-semibold text-gray-900">{emailStats.totalSent}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Failed Emails</p>
              <p className="text-2xl font-semibold text-gray-900">{emailStats.totalFailed}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Send className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Success Rate</p>
              <p className="text-2xl font-semibold text-gray-900">
                {emailStats.totalSent + emailStats.totalFailed > 0
                  ? Math.round((emailStats.totalSent / (emailStats.totalSent + emailStats.totalFailed)) * 100)
                  : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* SMTP Configuration */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <Settings className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">SMTP Configuration</h3>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Test Result */}
          {testResult && (
            <div className={`p-4 rounded-md ${
              testResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  {testResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-400" />
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    testResult.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {testResult.message}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* SMTP Host */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SMTP Host *
              </label>
              <input
                type="text"
                value={smtpConfig.host}
                onChange={(e) => handleInputChange('host', e.target.value)}
                placeholder="smtp.gmail.com"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* SMTP Port */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SMTP Port *
              </label>
              <input
                type="number"
                value={smtpConfig.port}
                onChange={(e) => handleInputChange('port', parseInt(e.target.value))}
                placeholder="587"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Username */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Username *
              </label>
              <input
                type="text"
                value={smtpConfig.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Password */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password *
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={smtpConfig.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="App password or SMTP password"
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            {/* Sender Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sender Name *
              </label>
              <input
                type="text"
                value={smtpConfig.senderName}
                onChange={(e) => handleInputChange('senderName', e.target.value)}
                placeholder="HashCoreX Support"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Sender Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sender Email *
              </label>
              <input
                type="email"
                value={smtpConfig.senderEmail}
                onChange={(e) => handleInputChange('senderEmail', e.target.value)}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* SSL/TLS */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="secure"
              checked={smtpConfig.secure}
              onChange={(e) => handleInputChange('secure', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="secure" className="ml-2 block text-sm text-gray-900">
              Use SSL/TLS (recommended for port 465)
            </label>
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              checked={smtpConfig.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
              Enable email sending
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4 pt-4">
            <Button
              onClick={handleTestConnection}
              disabled={testing || !smtpConfig.host || !smtpConfig.username}
              variant="outline"
            >
              {testing ? <Loading size="sm" /> : 'Test Connection'}
            </Button>

            <Button
              onClick={handleSave}
              disabled={saving || !smtpConfig.host || !smtpConfig.username}
            >
              {saving ? <Loading size="sm" /> : 'Save Configuration'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
