"use strict";(()=>{var e={};e.id=7660,e.ids=[7660],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4381:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>I,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var o={};s.r(o),s.d(o,{POST:()=>u});var r=s(96559),a=s(48088),n=s(37719),i=s(32190),c=s(6710),d=s(31183),p=s(59480);async function u(e){try{let t=e.headers.get("authorization"),s=process.env.CRON_SECRET;if(!s||t!==`Bearer ${s}`)return i.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});console.log("Starting deposit processing cron job...");let o=parseInt(await c.rs.get("minConfirmations")||"10"),r=await c.J6.findByStatus("PENDING");console.log(`Found ${r.length} pending deposits to process`);let a=0,n=0,u=0;for(let e of r)try{console.log(`Processing deposit ${e.transactionId} for user ${e.userId}`);let t=await (0,p.gp)(e.transactionId,e.tronAddress,1);if(await c.J6.updateConfirmations(e.transactionId,t.confirmations),console.log(`Transaction ${e.transactionId} has ${t.confirmations} confirmations (required: ${o})`),t.confirmations>=o&&t.isValid){console.log(`Transaction ${e.transactionId} has enough confirmations, processing...`),await c.J6.updateStatus(e.transactionId,"CONFIRMED",{verifiedAt:new Date,processedAt:new Date}),await c.k_.addDeposit(e.userId,e.amount);let s=await d.prisma.transaction.findFirst({where:{userId:e.userId,type:"DEPOSIT",description:`USDT TRC20 Deposit - TX: ${e.transactionId}`,status:"PENDING"}});s?await d.prisma.transaction.update({where:{id:s.id},data:{status:"COMPLETED",amount:e.amount}}):await c.DR.create({userId:e.userId,type:"DEPOSIT",amount:e.amount,description:`USDT TRC20 Deposit - TX: ${e.transactionId}`,status:"COMPLETED"}),await c.AJ.create({action:"DEPOSIT_AUTO_COMPLETED",userId:e.userId,details:{transactionId:e.transactionId,amount:e.amount,confirmations:t.confirmations,minConfirmations:o,processedBy:"CRON_JOB"}}),n++,console.log(`✅ Completed deposit ${e.transactionId} for ${e.amount} USDT`)}else t.isValid?console.log(`⏳ Deposit ${e.transactionId} still pending - ${t.confirmations}/${o} confirmations`):(await c.J6.markAsFailed(e.transactionId,`Transaction verification failed: ${t.confirmations} confirmations, invalid transaction`),await c.AJ.create({action:"DEPOSIT_AUTO_FAILED",userId:e.userId,details:{transactionId:e.transactionId,amount:e.amount,confirmations:t.confirmations,reason:"Transaction verification failed",processedBy:"CRON_JOB"}}),u++,console.log(`❌ Failed deposit ${e.transactionId} - invalid transaction`));a++}catch(t){console.error(`Error processing deposit ${e.transactionId}:`,t),u++}return await c.AJ.create({action:"DEPOSIT_PROCESSING_CRON_EXECUTED",details:{totalPending:r.length,processed:a,completed:n,failed:u,minConfirmations:o,executionTime:new Date().toISOString()}}),console.log(`Deposit processing completed: ${a} processed, ${n} completed, ${u} failed`),i.NextResponse.json({success:!0,message:"Deposit processing completed",data:{totalPending:r.length,processed:a,completed:n,failed:u,minConfirmations:o}})}catch(e){return console.error("Deposit processing cron error:",e),await c.AJ.create({action:"DEPOSIT_PROCESSING_CRON_ERROR",details:{error:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()}}),i.NextResponse.json({success:!1,error:"Deposit processing failed"},{status:500})}}let l=new r.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/cron/process-deposits/route",pathname:"/api/cron/process-deposits",filename:"route",bundlePath:"app/api/cron/process-deposits/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\process-deposits\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:I}=l;function f(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),o=t.X(0,[4447,580,3306,9480],()=>s(4381));module.exports=o})();