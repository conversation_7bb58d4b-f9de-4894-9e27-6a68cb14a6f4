'use client';

import React, { useState } from 'react';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { DashboardOverview } from '@/components/dashboard/DashboardOverview';
import { PurchaseMiningUnit } from '@/components/dashboard/PurchaseMiningUnit';
import { EarningsTracker } from '@/components/dashboard/EarningsTracker';
import { WalletDashboard } from '@/components/dashboard/WalletDashboard';
import { D3BinaryTree } from '@/components/dashboard/D3BinaryTree';
import { KYCPortal } from '@/components/dashboard/KYCPortal';
import { MiningUnitsTable } from '@/components/dashboard/MiningUnitsTable';
import { SupportCenter } from '@/components/dashboard/SupportCenter';
import { UserProfileSettings } from '@/components/dashboard/UserProfileSettings';

function DashboardContent() {
  const [activeTab, setActiveTab] = useState('overview');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <DashboardOverview onTabChange={setActiveTab} />;
      case 'mining':
        return (
          <div className="space-y-6">
            <PurchaseMiningUnit onPurchaseComplete={() => window.location.reload()} />
            <MiningUnitsTable />
          </div>
        );
      case 'earnings':
        return <EarningsTracker />;
      case 'wallet':
        return <WalletDashboard />;
      case 'referrals':
        return <D3BinaryTree />;
      case 'kyc':
        return <KYCPortal />;
      case 'support':
        return <SupportCenter />;
      case 'profile':
        return <UserProfileSettings />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <DashboardLayout activeTab={activeTab} onTabChange={setActiveTab}>
      {renderTabContent()}
    </DashboardLayout>
  );
}

export default function DashboardPage() {
  return (
    <AuthGuard requireAuth>
      <DashboardContent />
    </AuthGuard>
  );
}
