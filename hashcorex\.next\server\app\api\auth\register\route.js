"use strict";(()=>{var e={};e.id=1612,e.ids=[1612],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>w,Lx:()=>E,Oj:()=>f,b9:()=>h,qc:()=>y});var a=r(85663),s=r(43205),i=r.n(s),l=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",n=process.env.JWT_EXPIRES_IN||"7d",c=async e=>await a.Ay.hash(e,12),m=async(e,t)=>await a.Ay.compare(e,t),u=e=>i().sign(e,o,{expiresIn:n}),d=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=d(t);if(!r)return{authenticated:!1,user:null};let a=await l.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},w=async e=>{let t,a;if(await l.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await l.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await c(e.password),i=!1;do a=p(),i=!await l.Gy.findByReferralId(a);while(!i);let o=await l.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},E=async e=>{let t=await l.Gy.findByEmail(e.email);if(!t||!await m(e.password,t.password))throw Error("Invalid email or password");return{token:u({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},f=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),y=async e=>{let t=await l.Gy.findById(e);return t?.role==="ADMIN"}},21820:e=>{e.exports=require("os")},26767:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>E});var a={};r.r(a),r.d(a,{POST:()=>p});var s=r(96559),i=r(48088),l=r(37719),o=r(32190),n=r(12909),c=r(6710),m=r(61904);class u{async sendWelcomeEmail(e){try{let{user:t}=e,r={firstName:t.firstName,lastName:t.lastName,email:t.email,referralId:t.referralId,loginUrl:"http://localhost:3000/login",dashboardUrl:"http://localhost:3000/dashboard",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.USER_REGISTRATION,t.email,r,t.id)}catch(e){return console.error("❌ Failed to send welcome email:",e),!1}}async sendEmailVerificationOTP(e){try{let{user:t,otp:r}=e,a={firstName:t.firstName,lastName:t.lastName,otp:r,expiryMinutes:10,supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.EMAIL_VERIFICATION,t.email,a,t.id)}catch(e){return console.error("❌ Failed to send email verification OTP:",e),!1}}async sendPasswordResetOTP(e){try{let{user:t,otp:r}=e,a={firstName:t.firstName,lastName:t.lastName,otp:r,expiryMinutes:15,resetUrl:"http://localhost:3000/reset-password",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.PASSWORD_RESET,t.email,a,t.id)}catch(e){return console.error("❌ Failed to send password reset OTP:",e),!1}}async sendKYCApprovalEmail(e){try{let{user:t}=e,r={firstName:t.firstName,lastName:t.lastName,dashboardUrl:"http://localhost:3000/dashboard",miningUrl:"http://localhost:3000/dashboard?tab=mining",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.KYC_APPROVED,t.email,r,t.id)}catch(e){return console.error("❌ Failed to send KYC approval email:",e),!1}}async sendKYCRejectionEmail(e){try{let{user:t,reason:r}=e,a={firstName:t.firstName,lastName:t.lastName,reason:r||"Please ensure all documents are clear and valid.",kycUrl:"http://localhost:3000/dashboard?tab=kyc",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.KYC_REJECTED,t.email,a,t.id)}catch(e){return console.error("❌ Failed to send KYC rejection email:",e),!1}}async sendWithdrawalRequestEmail(e){try{let{user:t,amount:r,address:a,transactionId:s}=e,i={firstName:t.firstName,lastName:t.lastName,amount:r.toFixed(2),address:a,transactionId:s,walletUrl:"http://localhost:3000/dashboard?tab=wallet",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.WITHDRAWAL_REQUESTED,t.email,i,t.id)}catch(e){return console.error("❌ Failed to send withdrawal request email:",e),!1}}async sendWithdrawalApprovalEmail(e){try{let{user:t,amount:r,address:a,transactionId:s,txHash:i}=e,l={firstName:t.firstName,lastName:t.lastName,amount:r.toFixed(2),address:a,transactionId:s,txHash:i||"Processing...",explorerUrl:i?`https://tronscan.org/#/transaction/${i}`:"",walletUrl:"http://localhost:3000/dashboard?tab=wallet",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.WITHDRAWAL_APPROVED,t.email,l,t.id)}catch(e){return console.error("❌ Failed to send withdrawal approval email:",e),!1}}async sendWithdrawalRejectionEmail(e){try{let{user:t,amount:r,reason:a,transactionId:s}=e,i={firstName:t.firstName,lastName:t.lastName,amount:r.toFixed(2),reason:a||"Please contact support for more information.",transactionId:s,walletUrl:"http://localhost:3000/dashboard?tab=wallet",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.WITHDRAWAL_REJECTED,t.email,i,t.id)}catch(e){return console.error("❌ Failed to send withdrawal rejection email:",e),!1}}async sendMiningUnitPurchaseEmail(e){try{let{user:t,packageName:r,amount:a,units:s,dailyROI:i}=e,l={firstName:t.firstName,lastName:t.lastName,packageName:r,amount:a.toFixed(2),units:s,dailyROI:i.toFixed(2),miningUrl:"http://localhost:3000/dashboard?tab=mining",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.MINING_UNIT_PURCHASED,t.email,l,t.id)}catch(e){return console.error("❌ Failed to send mining unit purchase email:",e),!1}}async sendWeeklyEarningsEmail(e){try{let{user:t,totalEarnings:r,miningEarnings:a,referralCommissions:s,binaryCommissions:i}=e,l={firstName:t.firstName,lastName:t.lastName,totalEarnings:r.toFixed(2),miningEarnings:a.toFixed(2),referralCommissions:s.toFixed(2),binaryCommissions:i.toFixed(2),walletUrl:"http://localhost:3000/dashboard?tab=wallet",earningsUrl:"http://localhost:3000/dashboard?tab=earnings",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.WEEKLY_EARNINGS,t.email,l,t.id)}catch(e){return console.error("❌ Failed to send weekly earnings email:",e),!1}}async sendReferralCommissionEmail(e){try{let{user:t,referredUserName:r,commissionAmount:a,packageName:s}=e,i={firstName:t.firstName,lastName:t.lastName,referredUserName:r,commissionAmount:a.toFixed(2),packageName:s,walletUrl:"http://localhost:3000/dashboard?tab=wallet",referralUrl:"http://localhost:3000/dashboard?tab=referrals",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.REFERRAL_COMMISSION,t.email,i,t.id)}catch(e){return console.error("❌ Failed to send referral commission email:",e),!1}}async sendBinaryCommissionEmail(e){try{let{user:t,commissionAmount:r,matchedPoints:a,leftPoints:s,rightPoints:i}=e,l={firstName:t.firstName,lastName:t.lastName,commissionAmount:r.toFixed(2),matchedPoints:a.toFixed(2),leftPoints:s.toFixed(2),rightPoints:i.toFixed(2),walletUrl:"http://localhost:3000/dashboard?tab=wallet",binaryUrl:"http://localhost:3000/dashboard?tab=binary",supportEmail:"<EMAIL>",currentYear:new Date().getFullYear()};return await m.g.sendTemplateEmail(m.b.BINARY_COMMISSION,t.email,l,t.id)}catch(e){return console.error("❌ Failed to send binary commission email:",e),!1}}}let d=new u;async function p(e){try{let{email:t,firstName:r,lastName:a,password:s,confirmPassword:i,referralCode:l}=await e.json();if(!t||!r||!a||!s||!i)return o.NextResponse.json({success:!1,error:"All fields are required"},{status:400});if(!(0,n.DT)(t))return o.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});if(s!==i)return o.NextResponse.json({success:!1,error:"Passwords do not match"},{status:400});let m=(0,n.Oj)(s);if(!m.valid)return o.NextResponse.json({success:!1,error:m.errors.join(", ")},{status:400});let u=new URL(e.url).searchParams.get("side"),p=await (0,n.DY)({email:t,firstName:r,lastName:a,password:s,referralCode:l,placementSide:u||void 0});return await c.AJ.create({action:"USER_REGISTERED",userId:p.id,details:{email:p.email,referralCode:l||null},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),d.sendWelcomeEmail({user:p}).catch(e=>{console.error("Failed to send welcome email:",e)}),o.NextResponse.json({success:!0,message:"Registration successful",data:p})}catch(e){return console.error("Registration error:",e),o.NextResponse.json({success:!1,error:e.message||"Registration failed"},{status:400})}}let h=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:E,serverHooks:f}=h;function g(){return(0,l.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:E})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},61904:(e,t,r)=>{r.d(t,{b:()=>o,g:()=>l});var a=r(49526),s=r(6710);class i{async initializeTransporter(){try{let e=await s.prisma.sMTPConfiguration.findFirst({where:{isActive:!0}});if(!e)return void console.warn("No active SMTP configuration found");this.currentConfig=e,this.transporter=a.createTransporter({host:e.host,port:e.port,secure:e.secure,auth:{user:e.username,pass:e.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("✅ Email service initialized successfully")}catch(e){console.error("❌ Failed to initialize email service:",e),this.transporter=null,this.currentConfig=null}}async sendEmail(e){try{if((!this.transporter||!this.currentConfig)&&(await this.initializeTransporter(),!this.transporter||!this.currentConfig))throw Error("Email service not configured");let t={from:`"${this.currentConfig.senderName}" <${this.currentConfig.senderEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text};return await this.transporter.sendMail(t),await this.logEmail({userId:e.userId,recipient:e.to,subject:e.subject,templateId:e.templateId,status:"SENT",sentAt:new Date}),console.log(`✅ Email sent successfully to ${e.to}`),!0}catch(t){return console.error(`❌ Failed to send email to ${e.to}:`,t),await this.logEmail({userId:e.userId,recipient:e.to,subject:e.subject,templateId:e.templateId,status:"FAILED",errorMessage:t instanceof Error?t.message:"Unknown error"}),!1}}async sendTemplateEmail(e,t,r={},a){try{let s=await this.getTemplate(e);if(!s)throw Error(`Template '${e}' not found`);let i=this.processTemplate(s.subject,r),l=this.processTemplate(s.htmlContent,r),o=s.textContent?this.processTemplate(s.textContent,r):void 0;return await this.sendEmail({to:t,subject:i,html:l,text:o,templateId:s.id,userId:a})}catch(r){return console.error(`❌ Failed to send template email '${e}' to ${t}:`,r),!1}}processTemplate(e,t){let r=e;return Object.entries(t).forEach(([e,t])=>{let a=RegExp(`{{\\s*${e}\\s*}}`,"g");r=r.replace(a,String(t))}),r}async getTemplate(e){try{return await s.prisma.emailTemplate.findFirst({where:{name:e,isActive:!0}})}catch(t){return console.error(`❌ Failed to get template '${e}':`,t),null}}async logEmail(e){try{await s.prisma.emailLog.create({data:e})}catch(e){console.error("❌ Failed to log email:",e)}}async testConnection(){try{if(await this.initializeTransporter(),!this.transporter)return{success:!1,error:"Failed to initialize transporter"};return await this.transporter.verify(),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async getEmailStats(){try{let[e,t,r]=await Promise.all([s.prisma.emailLog.count({where:{status:"SENT"}}),s.prisma.emailLog.count({where:{status:"FAILED"}}),s.prisma.emailLog.findMany({take:10,orderBy:{createdAt:"desc"},include:{user:{select:{email:!0,firstName:!0,lastName:!0}},template:{select:{name:!0}}}})]);return{totalSent:e,totalFailed:t,recentEmails:r}}catch(e){return console.error("❌ Failed to get email stats:",e),{totalSent:0,totalFailed:0,recentEmails:[]}}}constructor(){this.transporter=null,this.currentConfig=null}}let l=new i,o={USER_REGISTRATION:"user-registration",EMAIL_VERIFICATION:"email-verification",PASSWORD_RESET:"password-reset",KYC_APPROVED:"kyc-approved",KYC_REJECTED:"kyc-rejected",WITHDRAWAL_REQUESTED:"withdrawal-requested",WITHDRAWAL_APPROVED:"withdrawal-approved",WITHDRAWAL_REJECTED:"withdrawal-rejected",MINING_UNIT_PURCHASED:"mining-unit-purchased",WEEKLY_EARNINGS:"weekly-earnings",BINARY_COMMISSION:"binary-commission",REFERRAL_COMMISSION:"referral-commission"}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,9526,3306],()=>r(26767));module.exports=a})();