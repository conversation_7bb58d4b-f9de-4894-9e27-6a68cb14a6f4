exports.id=3306,exports.ids=[3306],exports.modules={6710:(e,a,t)=>{"use strict";t.d(a,{AJ:()=>o,DR:()=>n,FW:()=>c,Gy:()=>s,J6:()=>p,OZ:()=>w,cc:()=>d,k_:()=>m,o1:()=>y,prisma:()=>i.prisma,rs:()=>u,tg:()=>r,wJ:()=>l});var i=t(31183);let s={create:async e=>await i.prisma.user.create({data:{email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password,referralId:e.referralId||void 0}}),findByEmail:async e=>await i.prisma.user.findUnique({where:{email:e},include:{miningUnits:!0,transactions:!0,binaryPoints:!0}}),findById:async e=>await i.prisma.user.findUnique({where:{id:e},include:{miningUnits:!0,transactions:!0,binaryPoints:!0}}),findByReferralId:async e=>await i.prisma.user.findUnique({where:{referralId:e}}),update:async(e,a)=>await i.prisma.user.update({where:{id:e},data:a}),updateKYCStatus:async(e,a)=>await i.prisma.user.update({where:{id:e},data:{kycStatus:a}}),updateWithdrawalAddress:async(e,a)=>await i.prisma.user.update({where:{email:e},data:{withdrawalAddress:a}})},r={async create(e){let a=new Date;return a.setFullYear(a.getFullYear()+2),await i.prisma.miningUnit.create({data:{userId:e.userId,thsAmount:e.thsAmount,investmentAmount:e.investmentAmount,dailyROI:e.dailyROI,expiryDate:a}})},findActiveByUserId:async e=>await i.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}}}),updateTotalEarned:async(e,a)=>await i.prisma.miningUnit.update({where:{id:e},data:{totalEarned:{increment:a}}}),expireUnit:async e=>await i.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}}),findAllActive:async()=>await i.prisma.miningUnit.findMany({where:{status:"ACTIVE",expiryDate:{gt:new Date}},include:{user:!0}}),async updateEarnings(e,a,t){let s={totalEarned:{increment:t}};switch(a){case"mining":s.miningEarnings={increment:t};break;case"referral":s.referralEarnings={increment:t};break;case"binary":s.binaryEarnings={increment:t}}return await i.prisma.miningUnit.update({where:{id:e},data:s})}},n={create:async e=>await i.prisma.transaction.create({data:{userId:e.userId,type:e.type,amount:e.amount,description:e.description,reference:e.reference,status:e.status||"PENDING"}}),async findByUserId(e,a){let t={userId:e};a?.types&&a.types.length>0&&(t.type={in:a.types}),a?.status&&(t.status=a.status),a?.search&&(t.OR=[{description:{contains:a.search,mode:"insensitive"}},{type:{contains:a.search,mode:"insensitive"}},{reference:{contains:a.search,mode:"insensitive"}}]);let s=a?.includeUser?{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}:void 0;return await i.prisma.transaction.findMany({where:t,include:s,orderBy:{createdAt:"desc"},take:a?.limit||50,skip:a?.offset})},async updateStatus(e,a,t){let s={status:a};return t?.amount!==void 0&&(s.amount=t.amount),t?.description&&(s.description=t.description),await i.prisma.transaction.update({where:{id:e},data:s})},findPendingByTypeAndDescription:async(e,a,t)=>await i.prisma.transaction.findFirst({where:{userId:e,type:a,description:{contains:t},status:"PENDING"}}),async updateByReference(e,a,t,s){let r={status:t};return s?.amount!==void 0&&(r.amount=s.amount),s?.description&&(r.description=s.description),await i.prisma.transaction.updateMany({where:{reference:e,type:a,status:"PENDING"},data:r})}},d={create:async e=>await i.prisma.referral.create({data:{referrerId:e.referrerId,referredId:e.referredId,placementSide:e.placementSide}}),findByReferrerId:async e=>await i.prisma.referral.findMany({where:{referrerId:e},include:{referred:{select:{id:!0,email:!0,createdAt:!0}}}})},c={async upsert(e){let a=void 0!==e.leftPoints?Math.round(100*e.leftPoints)/100:void 0,t=void 0!==e.rightPoints?Math.round(100*e.rightPoints)/100:void 0;return await i.prisma.binaryPoints.upsert({where:{userId:e.userId},update:{leftPoints:void 0!==a?{increment:a}:void 0,rightPoints:void 0!==t?{increment:t}:void 0},create:{userId:e.userId,leftPoints:a||0,rightPoints:t||0}})},findByUserId:async e=>await i.prisma.binaryPoints.findUnique({where:{userId:e}}),resetPoints:async(e,a,t)=>await i.prisma.binaryPoints.update({where:{userId:e},data:{leftPoints:a,rightPoints:t,flushDate:new Date}})},l={create:async e=>await i.prisma.withdrawalRequest.create({data:{userId:e.userId,amount:e.amount,usdtAddress:e.usdtAddress}}),findPending:async()=>await i.prisma.withdrawalRequest.findMany({where:{status:"PENDING"},include:{user:{select:{id:!0,email:!0,kycStatus:!0}}},orderBy:{createdAt:"asc"}}),updateStatus:async(e,a,t,s,r)=>await i.prisma.withdrawalRequest.update({where:{id:e},data:{status:a,processedBy:t,txid:s,rejectionReason:r,processedAt:new Date}})},u={async get(e){let a=await i.prisma.adminSettings.findUnique({where:{key:e}});return a?.value},set:async(e,a,t)=>await i.prisma.adminSettings.upsert({where:{key:e},update:{value:a},create:{key:e,value:a}}),getAll:async()=>await i.prisma.adminSettings.findMany()},o={create:async e=>await i.prisma.systemLog.create({data:{action:e.action,userId:e.userId,adminId:e.adminId,details:e.details?JSON.stringify(e.details):null,ipAddress:e.ipAddress,userAgent:e.userAgent}})},m={async getOrCreate(e){let a=await i.prisma.walletBalance.findUnique({where:{userId:e}});return a||(a=await i.prisma.walletBalance.create({data:{userId:e,availableBalance:0,pendingBalance:0,totalDeposits:0,totalWithdrawals:0,totalEarnings:0}})),a},updateBalance:async(e,a)=>await i.prisma.walletBalance.update({where:{userId:e},data:{...a,lastUpdated:new Date}}),async addDeposit(e,a){let t=await this.getOrCreate(e);return await i.prisma.walletBalance.update({where:{userId:e},data:{availableBalance:t.availableBalance+a,totalDeposits:t.totalDeposits+a,lastUpdated:new Date}})},async addEarnings(e,a){let t=await this.getOrCreate(e);return await i.prisma.walletBalance.update({where:{userId:e},data:{availableBalance:t.availableBalance+a,totalEarnings:t.totalEarnings+a,lastUpdated:new Date}})},async deductWithdrawal(e,a){let t=await this.getOrCreate(e);if(t.availableBalance<a)throw Error("Insufficient balance");return await i.prisma.walletBalance.update({where:{userId:e},data:{availableBalance:t.availableBalance-a,totalWithdrawals:t.totalWithdrawals+a,lastUpdated:new Date}})},async findByUserId(e){return await this.getOrCreate(e)}},p={create:async e=>await i.prisma.depositTransaction.create({data:{userId:e.userId,transactionId:e.transactionId,amount:e.amount,usdtAmount:e.usdtAmount,tronAddress:e.tronAddress,senderAddress:e.senderAddress,blockNumber:e.blockNumber,blockTimestamp:e.blockTimestamp,confirmations:e.confirmations||0,status:"PENDING"}}),findByTransactionId:async e=>await i.prisma.depositTransaction.findUnique({where:{transactionId:e},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}}),async findByUserId(e,a){let t={userId:e};return a?.status&&(t.status=a.status),await i.prisma.depositTransaction.findMany({where:t,orderBy:{createdAt:"desc"},take:a?.limit||50,skip:a?.offset,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}})},async findAll(e){let a={};return e?.status&&(a.status=e.status),await i.prisma.depositTransaction.findMany({where:a,orderBy:{createdAt:"desc"},take:e?.limit||100,skip:e?.offset,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}})},async updateStatus(e,a,t){let s={status:a};return t?.verifiedAt&&(s.verifiedAt=t.verifiedAt),t?.processedAt&&(s.processedAt=t.processedAt),t?.failureReason&&(s.failureReason=t.failureReason),t?.confirmations!==void 0&&(s.confirmations=t.confirmations),await i.prisma.depositTransaction.update({where:{transactionId:e},data:s})},async markAsCompleted(e){return await this.updateStatus(e,"COMPLETED",{processedAt:new Date})},async markAsFailed(e,a){return await this.updateStatus(e,"FAILED",{failureReason:a,processedAt:new Date})},async getPendingDeposits(){return await this.findAll({status:"PENDING"})},async getPendingVerificationDeposits(){return await this.findAll({status:"PENDING_VERIFICATION"})},async getWaitingForConfirmationsDeposits(){return await this.findAll({status:"WAITING_FOR_CONFIRMATIONS"})},findByStatus:async e=>await i.prisma.depositTransaction.findMany({where:{status:e},orderBy:{createdAt:"desc"},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}}),updateConfirmations:async(e,a)=>await i.prisma.depositTransaction.update({where:{transactionId:e},data:{confirmations:a}}),async getDepositStats(){let e=await i.prisma.depositTransaction.aggregate({_count:{id:!0},_sum:{usdtAmount:!0},where:{status:{in:["COMPLETED","CONFIRMED"]}}}),a=await i.prisma.depositTransaction.count({where:{status:{in:["PENDING","PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS"]}}});return{totalDeposits:e._count.id||0,totalAmount:e._sum.usdtAmount||0,pendingDeposits:a}}},w={create:async e=>await i.prisma.supportTicket.create({data:e,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}},responses:{include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"asc"}}}}),findByUserId:async e=>await i.prisma.supportTicket.findMany({where:{userId:e},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}},responses:{include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"asc"}}},orderBy:{createdAt:"desc"}}),findById:async e=>await i.prisma.supportTicket.findUnique({where:{id:e},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}},responses:{include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"asc"}}}}),findAll:async()=>await i.prisma.supportTicket.findMany({include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}},responses:{include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"asc"}}},orderBy:{createdAt:"desc"}}),updateStatus:async(e,a)=>await i.prisma.supportTicket.update({where:{id:e},data:{status:a,updatedAt:new Date},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}},responses:{include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"asc"}}}})},y={create:async e=>await i.prisma.ticketResponse.create({data:e,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}}}),findByTicketId:async e=>await i.prisma.ticketResponse.findMany({where:{ticketId:e},include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"asc"}})}},31183:(e,a,t)=>{"use strict";t.d(a,{prisma:()=>s});var i=t(96330);let s=globalThis.prisma??new i.PrismaClient},78335:()=>{},96487:()=>{}};