"use strict";(()=>{var e={};e.id=5276,e.ids=[5276],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,n)=>{n.d(t,{DT:()=>w,DY:()=>f,Lx:()=>h,Oj:()=>y,b9:()=>g,qc:()=>E});var a=n(85663),r=n(43205),i=n.n(r),s=n(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await a.Ay.hash(e,12),d=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let n=0;n<8;n++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},g=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let n=m(t);if(!n)return{authenticated:!1,user:null};let a=await s.Gy.findByEmail(n.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let n=await s.Gy.findByReferralId(e.referralCode);if(!n)throw Error("Invalid referral code");t=n.id}let r=await l(e.password),i=!1;do a=p(),i=!await s.Gy.findByReferralId(a);while(!i);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:r,referralId:a});if(t){let{placeUserByReferralType:a}=await n.e(2746).then(n.bind(n,2746)),r="general";"left"===e.placementSide?r="left":"right"===e.placementSide&&(r="right"),await a(t,o.id,r)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),E=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39794:(e,t,n)=>{n.d(t,{Py:()=>s,k8:()=>l,kp:()=>u});var a=n(31183),r=n(6710);async function i(e){return await a.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,t,n,s,u){let l=await i(e);if(0===l.length)throw Error("No active mining units found for earnings allocation");let d=[],c=t;for(let e of l){var m;if(c<=0)break;let t=Math.max(0,5*(m=e).investmentAmount-(m.miningEarnings+m.referralEarnings+m.binaryEarnings));if(t<=0)continue;let r=Math.min(c,t);if(r>0){let i={};switch(n){case"MINING_EARNINGS":i.miningEarnings={increment:r};break;case"DIRECT_REFERRAL":i.referralEarnings={increment:r};break;case"BINARY_BONUS":i.binaryEarnings={increment:r}}i.totalEarned={increment:r},await a.prisma.miningUnit.update({where:{id:e.id},data:i}),await a.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:n,amount:r,description:u}}),d.push({miningUnitId:e.id,amount:r,remainingCapacity:t-r}),c-=r;let l=await a.prisma.miningUnit.findUnique({where:{id:e.id}});l&&function(e){let t=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=t}(l)&&await o(e.id,"5x_investment_reached")}}return c>0&&(console.warn(`Unable to allocate ${c} to mining units - all units at capacity`),await r.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:t,allocatedAmount:t-c,overflowAmount:c,earningType:n,reason:"all_units_at_capacity"}})),d}async function o(e,t){let n=await a.prisma.miningUnit.findUnique({where:{id:e}});if(!n)throw Error(`Mining unit ${e} not found`);await a.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let i=n.miningEarnings+n.referralEarnings+n.binaryEarnings;await r.AJ.create({action:"MINING_UNIT_EXPIRED",userId:n.userId,details:{miningUnitId:e,reason:t,totalEarned:i,miningEarnings:n.miningEarnings,referralEarnings:n.referralEarnings,binaryEarnings:n.binaryEarnings,investmentAmount:n.investmentAmount,multiplier:i/n.investmentAmount}}),console.log(`Mining unit ${e} expired due to ${t}. Total earnings: ${i}`)}async function u(e){return await a.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function l(e){return await a.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},87646:(e,t,n)=>{n.r(t),n.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var a={};n.r(a),n.d(a,{GET:()=>c});var r=n(96559),i=n(48088),s=n(37719),o=n(32190),u=n(12909),l=n(39794),d=n(31183);async function c(e,{params:t}){try{let{authenticated:n,user:a}=await (0,u.b9)(e);if(!n||!a)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let r=t.id,i=await d.prisma.miningUnit.findUnique({where:{id:r},select:{userId:!0,investmentAmount:!0,thsAmount:!0,status:!0}});if(!i)return o.NextResponse.json({success:!1,error:"Mining unit not found"},{status:404});if(i.userId!==a.id)return o.NextResponse.json({success:!1,error:"Access denied"},{status:403});let s=await (0,l.k8)(r),c=s.reduce((e,t)=>{let n=t.earningType;return e[n]||(e[n]={totalAmount:0,count:0,allocations:[]}),e[n].totalAmount+=t.amount,e[n].count+=1,e[n].allocations.push({amount:t.amount,date:t.allocatedAt,description:t.description,transactionId:t.transactionId}),e},{}),m=s.reduce((e,t)=>e+t.amount,0),p=5*i.investmentAmount,g=Math.max(0,p-m),f=m/p*100;return o.NextResponse.json({success:!0,data:{miningUnit:{id:r,thsAmount:i.thsAmount,investmentAmount:i.investmentAmount,status:i.status,maxEarnings:p},earnings:{totalAllocated:m,remainingCapacity:g,progressPercentage:Math.min(f,100),summary:c},history:s.map(e=>({id:e.id,earningType:e.earningType,amount:e.amount,description:e.description,allocatedAt:e.allocatedAt,transaction:{id:e.transaction.id,type:e.transaction.type,status:e.transaction.status,createdAt:e.transaction.createdAt}}))}})}catch(e){return console.error("Mining unit earnings history fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch earnings history"},{status:500})}}let m=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/mining-units/[id]/earnings/route",pathname:"/api/mining-units/[id]/earnings",filename:"route",bundlePath:"app/api/mining-units/[id]/earnings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\mining-units\\[id]\\earnings\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:f}=m;function h(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>n(87646));module.exports=a})();