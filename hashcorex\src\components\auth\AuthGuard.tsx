'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { Loading } from '@/components/ui';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  requireAdmin = false,
  redirectTo = '/login',
  fallback,
}) => {
  const { user, loading, isInitialized } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Only perform redirects after authentication is fully initialized
    if (!isInitialized || loading) {
      return;
    }

    // Check if authentication is required
    if (requireAuth && !user) {
      router.push(redirectTo);
      return;
    }

    // Check if admin access is required
    if (requireAdmin && user && user.role !== 'ADMIN') {
      router.push('/dashboard');
      return;
    }
  }, [user, loading, isInitialized, requireAuth, requireAdmin, redirectTo, router]);

  // Show loading while authentication is being checked
  if (!isInitialized || loading) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Checking authentication..." />
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !user) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Redirecting to login..." />
      </div>
    );
  }

  // If admin access is required but user is not admin
  if (requireAdmin && user && user.role !== 'ADMIN') {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Redirecting to dashboard..." />
      </div>
    );
  }

  // Render children if all checks pass
  return <>{children}</>;
};

// Convenience wrapper for pages that require authentication
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<AuthGuardProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <AuthGuard {...options}>
      <Component {...props} />
    </AuthGuard>
  );

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Convenience wrapper for admin pages
export const withAdminAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<AuthGuardProps, 'children' | 'requireAdmin'>
) => {
  const WrappedComponent = (props: P) => (
    <AuthGuard requireAdmin {...options}>
      <Component {...props} />
    </AuthGuard>
  );

  WrappedComponent.displayName = `withAdminAuth(${Component.displayName || Component.name})`;
  return WrappedComponent;
};
