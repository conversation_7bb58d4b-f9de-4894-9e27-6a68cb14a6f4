'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';
import { Container, Grid, PublicLayout } from '@/components/layout';
import { Mail, Phone, MapPin, Clock, Send } from 'lucide-react';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Here you would typically send the form data to your API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      setSubmitted(true);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PublicLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-solar-50 to-eco-50">
        <Container>
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl lg:text-6xl font-bold text-dark-900 mb-6">
              Contact <span className="text-solar-500">HashCoreX</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Have questions about our solar-powered mining platform? We&apos;re here to help.
              Get in touch with our team of experts.
            </p>
          </div>
        </Container>
      </section>

      {/* Contact Information */}
      <section className="py-16 bg-white">
        <Container>
          <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={8}>
            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="w-16 h-16 bg-solar-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="h-8 w-8 text-solar-600" />
              </div>
              <h3 className="text-lg font-semibold text-dark-900 mb-2">Email Us</h3>
              <p className="text-gray-600 mb-2">Get in touch via email</p>
              <a href="mailto:<EMAIL>" className="text-solar-600 hover:text-solar-700 font-medium">
                <EMAIL>
              </a>
            </div>

            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="w-16 h-16 bg-eco-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="h-8 w-8 text-eco-600" />
              </div>
              <h3 className="text-lg font-semibold text-dark-900 mb-2">Call Us</h3>
              <p className="text-gray-600 mb-2">Speak with our team</p>
              <a href="tel:******-HASHCORE" className="text-eco-600 hover:text-eco-700 font-medium">
                +1 (555) HASH-CORE
              </a>
            </div>

            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-dark-900 mb-2">Visit Us</h3>
              <p className="text-gray-600 mb-2">Our headquarters</p>
              <p className="text-blue-600 font-medium">
                123 Solar Drive<br />
                Green Valley, CA 90210
              </p>
            </div>

            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-dark-900 mb-2">Support Hours</h3>
              <p className="text-gray-600 mb-2">We&apos;re here to help</p>
              <p className="text-purple-600 font-medium">
                24/7 Support<br />
                Always Available
              </p>
            </div>
          </Grid>
        </Container>
      </section>

      {/* Contact Form */}
      <section className="py-16 bg-gray-50">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-dark-900 mb-4">Send Us a Message</h2>
              <p className="text-xl text-gray-600">
                Fill out the form below and we&apos;ll get back to you within 24 hours.
              </p>
            </div>

            {submitted ? (
              <div className="bg-green-50 border border-green-200 rounded-xl p-8 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Send className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-semibold text-green-800 mb-2">Message Sent!</h3>
                <p className="text-green-700">
                  Thank you for contacting us. We&apos;ll get back to you within 24 hours.
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="bg-white rounded-xl shadow-lg p-8">
                <Grid cols={{ default: 1, md: 2 }} gap={6}>
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
                      placeholder="Your full name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </Grid>

                <div className="mt-6">
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
                  >
                    <option value="">Select a subject</option>
                    <option value="general">General Inquiry</option>
                    <option value="technical">Technical Support</option>
                    <option value="billing">Billing & Payments</option>
                    <option value="partnership">Partnership Opportunities</option>
                    <option value="media">Media & Press</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="mt-6">
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
                    placeholder="Tell us how we can help you..."
                  />
                </div>

                <div className="mt-8">
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full bg-solar-500 hover:bg-solar-600 text-white"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="h-5 w-5 mr-2" />
                        Send Message
                      </>
                    )}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </Container>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <Container>
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-dark-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600">
              Quick answers to common questions about HashCoreX.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <Grid cols={{ default: 1, md: 2 }} gap={8}>
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-dark-900 mb-3">How do I get started?</h3>
                <p className="text-gray-600">
                  Simply create an account, complete KYC verification, and purchase your first mining unit.
                  You&apos;ll start earning daily returns immediately.
                </p>
              </div>

              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-dark-900 mb-3">What are the minimum requirements?</h3>
                <p className="text-gray-600">
                  The minimum investment is $50 for 1 TH/s. You can start small and scale up 
                  as you become more comfortable with the platform.
                </p>
              </div>

              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-dark-900 mb-3">How are returns calculated?</h3>
                <p className="text-gray-600">
                  Returns are calculated daily based on your TH/s amount and current market conditions. 
                  Larger investments receive higher ROI rates.
                </p>
              </div>

              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-dark-900 mb-3">Is my investment secure?</h3>
                <p className="text-gray-600">
                  Yes, we use industry-leading security measures and our solar-powered infrastructure 
                  provides stable, sustainable mining operations.
                </p>
              </div>
            </Grid>
          </div>
        </Container>
      </section>
    </PublicLayout>
  );
}
