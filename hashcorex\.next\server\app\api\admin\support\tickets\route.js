const CHUNK_PUBLIC_PATH = "server/app/api/admin/support/tickets/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_6bc5554f._.js");
runtime.loadChunk("server/chunks/node_modules_08edb9b0._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__dc60cc73._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/support/tickets/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/support/tickets/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/support/tickets/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
