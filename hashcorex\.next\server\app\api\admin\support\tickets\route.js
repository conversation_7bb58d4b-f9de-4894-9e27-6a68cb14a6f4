"use strict";(()=>{var e={};e.id=6361,e.ids=[6361],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>x,DY:()=>f,Lx:()=>y,Oj:()=>w,b9:()=>h,qc:()=>k});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",u=async e=>await a.Ay.hash(e,12),d=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:l}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await u(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),k=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},80071:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>d});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(12909),u=r(6710);async function d(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("ADMIN"!==r.role)return o.NextResponse.json({success:!1,error:"Access denied"},{status:403});let a=new URL(e.url),s=a.searchParams.get("status"),i=a.searchParams.get("priority"),n=a.searchParams.get("userId"),d=await u.OZ.findAll();return s&&(d=d.filter(e=>e.status===s)),i&&(d=d.filter(e=>e.priority===i)),n&&(d=d.filter(e=>e.userId===n)),o.NextResponse.json({success:!0,data:d})}catch(e){return console.error("Admin support tickets fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch support tickets"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/support/tickets/route",pathname:"/api/admin/support/tickets",filename:"route",bundlePath:"app/api/admin/support/tickets/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\support\\tickets\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:h}=c;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(80071));module.exports=a})();