(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2678],{2657:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4105:(e,a,s)=>{"use strict";s.d(a,{A:()=>o,AuthProvider:()=>i});var t=s(5155),r=s(2115);let l=(0,r.createContext)(void 0),i=e=>{let{children:a}=e,[s,i]=(0,r.useState)(null),[o,c]=(0,r.useState)(!0),[n,d]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=localStorage.getItem("auth-user");if(e)try{let a=JSON.parse(e);i(a)}catch(e){console.error("Error parsing stored user:",e),localStorage.removeItem("auth-user")}m()},[]);let m=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let a=await e.json();a.success?(i(a.data),localStorage.setItem("auth-user",JSON.stringify(a.data))):(i(null),localStorage.removeItem("auth-user"))}else i(null),localStorage.removeItem("auth-user")}catch(e){console.error("Auth check failed:",e),i(null),localStorage.removeItem("auth-user")}finally{c(!1),d(!0)}},x=async(e,a)=>{c(!0);try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:a})}),t=await s.json();if(!t.success)throw Error(t.error||"Login failed");i(t.data.user),localStorage.setItem("auth-user",JSON.stringify(t.data.user)),d(!0)}catch(e){throw e}finally{c(!1)}},h=async(e,a,s,t,r,l,i)=>{c(!0);try{let o=await fetch(i?"/api/auth/register?side=".concat(i):"/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:a,lastName:s,password:t,confirmPassword:r,referralCode:l})}),c=await o.json();if(!c.success)throw Error(c.error||"Registration failed");await x(e,t)}catch(e){throw e}finally{c(!1)}},u=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{i(null),localStorage.removeItem("auth-user"),d(!0)}},f=async()=>{await m()};return(0,t.jsx)(l.Provider,{value:{user:s,loading:o,isInitialized:n,login:x,register:h,logout:u,refreshUser:f},children:a})},o=()=>{let e=(0,r.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4712:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>p});var t=s(5155),r=s(2115),l=s(6874),i=s.n(l),o=s(5695),c=s(8928),n=s(7508),d=s(8749),m=s(2657),x=s(5196),h=s(7550),u=s(4105),f=s(9434);function p(){let e=(0,o.useRouter)(),a=(0,o.useSearchParams)(),{register:s,loading:l}=(0,u.A)(),[p,g]=(0,r.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",referralCode:a.get("ref")||""}),[y,j]=(0,r.useState)(!1),[w,N]=(0,r.useState)(!1),[v,b]=(0,r.useState)(""),[S,k]=(0,r.useState)({isValid:!1,errors:[]}),P=async t=>{if(t.preventDefault(),b(""),p.password!==p.confirmPassword)return void b("Passwords do not match");if(!S.isValid)return void b("Please ensure your password meets all requirements");try{let t=a.get("side");await s(p.email,p.firstName,p.lastName,p.password,p.confirmPassword,p.referralCode||void 0,t||void 0),e.push("/dashboard")}catch(e){b(e instanceof Error?e.message:"Registration failed")}},C=e=>{let{name:a,value:s}=e.target;g(e=>({...e,[a]:s})),"password"===a&&k((0,f.Oj)(s))};return(0,t.jsxs)("div",{className:"min-h-screen relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 animated-gradient opacity-30"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/95 via-solar-50/90 to-eco-50/95"}),(0,t.jsx)("div",{className:"absolute top-20 left-10 w-16 h-16 bg-solar-400/20 rounded-full animate-float"}),(0,t.jsx)("div",{className:"absolute top-40 right-20 w-12 h-12 bg-eco-400/20 rounded-full animate-float",style:{animationDelay:"2s"}}),(0,t.jsx)("div",{className:"absolute bottom-40 left-20 w-10 h-10 bg-purple-400/20 rounded-full animate-float",style:{animationDelay:"4s"}}),(0,t.jsxs)("div",{className:"relative z-10 min-h-screen flex",children:[(0,t.jsx)("div",{className:"hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-solar-500/10 to-eco-500/10 items-center justify-center p-12",children:(0,t.jsxs)("div",{className:"max-w-lg text-center",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)(n.MX,{className:"h-24 w-24 text-solar-500 mx-auto mb-6"}),(0,t.jsx)("h2",{className:"text-4xl xl:text-5xl font-black text-dark-900 mb-4",children:"Start Your Mining Journey"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"Join our sustainable mining revolution and start earning daily returns with our eco-friendly, solar-powered platform."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 text-left",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-eco-500/20 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-eco-600 font-bold",children:"\uD83C\uDF31"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-dark-900",children:"Sustainable Mining"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Powered by renewable solar energy"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-solar-500/20 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-solar-600 font-bold",children:"\uD83D\uDCB0"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-dark-900",children:"Binary Referral System"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Earn from direct referrals & binary matching"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-purple-600 font-bold",children:"\uD83D\uDE80"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-dark-900",children:"Easy Start"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Get started with just $50 minimum"})]})]})]})]})}),(0,t.jsx)("div",{className:"w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-4 lg:p-8",children:(0,t.jsx)("div",{className:"w-full max-w-md",children:(0,t.jsxs)("div",{className:"glass-morphism rounded-3xl p-8 lg:p-10 shadow-2xl",children:[(0,t.jsxs)("div",{className:"text-center mb-8 lg:mb-10",children:[(0,t.jsxs)(i(),{href:"/",className:"inline-flex items-center space-x-3 mb-6 lg:mb-8 group lg:hidden",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.MX,{className:"h-10 w-10 lg:h-12 lg:w-12 text-solar-500 group-hover:scale-110 transition-transform"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-solar-500/20 rounded-full animate-ping"})]}),(0,t.jsx)("span",{className:"text-2xl lg:text-4xl font-black bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent",children:"HashCoreX"})]}),(0,t.jsx)("h1",{className:"text-2xl lg:text-3xl xl:text-4xl font-black text-dark-900 mb-3 lg:mb-4",children:"Create Account"}),(0,t.jsx)("p",{className:"text-base lg:text-lg text-gray-600 font-medium",children:"Start your sustainable mining journey"})]}),(0,t.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[v&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:v}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,t.jsx)(c.pd,{label:"First Name",type:"text",name:"firstName",value:p.firstName,onChange:C,placeholder:"Enter your first name",required:!0}),(0,t.jsx)(c.pd,{label:"Last Name",type:"text",name:"lastName",value:p.lastName,onChange:C,placeholder:"Enter your last name",required:!0})]}),(0,t.jsx)(c.pd,{label:"Email Address",type:"email",name:"email",value:p.email,onChange:C,placeholder:"Enter your email",required:!0}),(0,t.jsx)(c.pd,{label:"Referral Code (Optional)",type:"text",name:"referralCode",value:p.referralCode,onChange:C,placeholder:"Enter referral code"}),(0,t.jsx)(c.pd,{label:"Password",type:y?"text":"password",name:"password",value:p.password,onChange:C,placeholder:"Create a strong password",required:!0,rightIcon:(0,t.jsx)("button",{type:"button",onClick:()=>j(!y),className:"text-gray-400 hover:text-gray-600",children:y?(0,t.jsx)(d.A,{className:"h-5 w-5"}):(0,t.jsx)(m.A,{className:"h-5 w-5"})})}),p.password&&(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Password Requirements:"}),(0,t.jsx)("div",{className:"space-y-1",children:[{text:"At least 8 characters",check:p.password.length>=8},{text:"One uppercase letter",check:/[A-Z]/.test(p.password)},{text:"One lowercase letter",check:/[a-z]/.test(p.password)},{text:"One number",check:/\d/.test(p.password)},{text:"One special character",check:/[!@#$%^&*(),.?":{}|<>]/.test(p.password)}].map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 ".concat(e.check?"text-eco-500":"text-gray-300")}),(0,t.jsx)("span",{className:"text-sm ".concat(e.check?"text-eco-600":"text-gray-500"),children:e.text})]},a))})]}),(0,t.jsx)(c.pd,{label:"Confirm Password",type:w?"text":"password",name:"confirmPassword",value:p.confirmPassword,onChange:C,placeholder:"Confirm your password",required:!0,error:p.confirmPassword&&p.password!==p.confirmPassword?"Passwords do not match":void 0,rightIcon:(0,t.jsx)("button",{type:"button",onClick:()=>N(!w),className:"text-gray-400 hover:text-gray-600",children:w?(0,t.jsx)(d.A,{className:"h-5 w-5"}):(0,t.jsx)(m.A,{className:"h-5 w-5"})})}),(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",required:!0,className:"mt-1 rounded border-gray-300 text-solar-500 focus:ring-solar-500"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["I agree to the"," ",(0,t.jsx)(i(),{href:"/terms",className:"text-solar-500 hover:text-solar-600",children:"Terms of Service"})," ","and"," ",(0,t.jsx)(i(),{href:"/privacy",className:"text-solar-500 hover:text-solar-600",children:"Privacy Policy"})]})]}),(0,t.jsx)(c.$n,{type:"submit",variant:"primary",size:"xl",className:"w-full font-bold",loading:l,disabled:!S.isValid||p.password!==p.confirmPassword,children:"Create Account"})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",(0,t.jsx)(i(),{href:"/login",className:"text-solar-500 hover:text-solar-600 font-medium",children:"Sign in"})]})}),(0,t.jsx)("div",{className:"mt-6 text-center lg:hidden",children:(0,t.jsxs)(i(),{href:"/",className:"inline-flex items-center text-gray-500 hover:text-gray-700 text-sm",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Back to Home"]})})]})})})]})]})}},5196:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5373:(e,a,s)=>{Promise.resolve().then(s.bind(s,4712))},5695:(e,a,s)=>{"use strict";var t=s(8999);s.o(t,"useRouter")&&s.d(a,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(a,{useSearchParams:function(){return t.useSearchParams}})},7550:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8749:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[3501,6083,8441,1684,7358],()=>a(5373)),_N_E=e.O()}]);