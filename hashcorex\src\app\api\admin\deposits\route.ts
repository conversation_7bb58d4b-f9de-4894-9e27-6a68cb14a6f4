import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { depositTransactionDb } from '@/lib/database';
import { DepositStatus } from '@/types';

// GET - Fetch all deposits for admin
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status') as DepositStatus | null;
    const userId = searchParams.get('userId') || undefined;

    // Fetch deposits with filters
    let deposits;
    if (userId) {
      deposits = await depositTransactionDb.findByUserId(userId, {
        status: status || undefined,
        limit: Math.min(limit, 100),
        offset,
      });
    } else {
      deposits = await depositTransactionDb.findAll({
        status: status || undefined,
        limit: Math.min(limit, 100),
        offset,
      });
    }

    // Get deposit statistics
    const stats = await depositTransactionDb.getDepositStats();

    return NextResponse.json({
      success: true,
      data: {
        deposits: deposits.map(deposit => ({
          id: deposit.id,
          userId: deposit.userId,
          user: deposit.user ? {
            id: deposit.user.id,
            email: deposit.user.email,
            firstName: deposit.user.firstName,
            lastName: deposit.user.lastName,
          } : null,
          transactionId: deposit.transactionId,
          amount: deposit.amount,
          usdtAmount: deposit.usdtAmount,
          tronAddress: deposit.tronAddress,
          senderAddress: deposit.senderAddress,
          status: deposit.status,
          blockNumber: deposit.blockNumber,
          blockTimestamp: deposit.blockTimestamp,
          confirmations: deposit.confirmations,
          verifiedAt: deposit.verifiedAt,
          processedAt: deposit.processedAt,
          failureReason: deposit.failureReason,
          createdAt: deposit.createdAt,
          updatedAt: deposit.updatedAt,
        })),
        stats: {
          totalDeposits: stats.totalDeposits,
          totalAmount: stats.totalAmount,
          pendingDeposits: stats.pendingDeposits,
        },
        pagination: {
          limit,
          offset,
          hasMore: deposits.length === limit,
        },
        filters: {
          status,
          userId,
        },
      },
    });

  } catch (error) {
    console.error('Admin deposits fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch deposits' },
      { status: 500 }
    );
  }
}

// POST - Admin actions on deposits (approve, reject, etc.)
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, transactionId, reason } = body;

    if (!action || !transactionId) {
      return NextResponse.json(
        { success: false, error: 'Action and transaction ID are required' },
        { status: 400 }
      );
    }

    // Find the deposit
    const deposit = await depositTransactionDb.findByTransactionId(transactionId);
    if (!deposit) {
      return NextResponse.json(
        { success: false, error: 'Deposit not found' },
        { status: 404 }
      );
    }

    // Manual actions are no longer supported - deposits are processed automatically
    return NextResponse.json(
      {
        success: false,
        error: 'Manual deposit actions are no longer supported. Deposits are processed automatically by the system.'
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Admin deposit action error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process deposit action' },
      { status: 500 }
    );
  }
}
