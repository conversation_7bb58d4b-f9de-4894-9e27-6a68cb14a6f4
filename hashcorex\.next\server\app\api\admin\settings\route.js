"use strict";(()=>{var e={};e.id=8225,e.ids=[8225],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,a)=>{a.d(t,{DT:()=>I,DY:()=>P,Lx:()=>y,Oj:()=>h,b9:()=>A,qc:()=>S});var r=a(85663),n=a(43205),s=a.n(n),i=a(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await r.Ay.hash(e,12),u=async(e,t)=>await r.Ay.compare(e,t),c=e=>s().sign(e,o,{expiresIn:l}),p=e=>{try{return s().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let a=0;a<8;a++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},A=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let a=p(t);if(!a)return{authenticated:!1,user:null};let r=await i.Gy.findByEmail(a.email);return r?{authenticated:!0,user:r}:{authenticated:!1,user:null}},P=async e=>{let t,r;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let a=await i.Gy.findByReferralId(e.referralCode);if(!a)throw Error("Invalid referral code");t=a.id}let n=await d(e.password),s=!1;do r=m(),s=!await i.Gy.findByReferralId(r);while(!s);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:n,referralId:r});if(t){let{placeUserByReferralType:r}=await a.e(2746).then(a.bind(a,2746)),n="general";"left"===e.placementSide?n="left":"right"===e.placementSide&&(n="right"),await r(t,o.id,n)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},h=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},I=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),S=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},60864:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>P,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>A});var r={};a.r(r),a.d(r,{GET:()=>u,PUT:()=>c});var n=a(96559),s=a(48088),i=a(37719),o=a(32190),l=a(12909),d=a(6710);async function u(e){try{let{authenticated:t,user:a}=await (0,l.b9)(e);if(!t||!a)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,l.qc)(a.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let r=await d.rs.getAll(),n={};r.forEach(e=>{try{n[e.key]=JSON.parse(e.value)}catch{n[e.key]=e.value}}),n.MAX_BINARY_POINTS_PER_SIDE&&(n.maxBinaryPointsPerSide=parseFloat(n.MAX_BINARY_POINTS_PER_SIDE),console.log(`Mapped MAX_BINARY_POINTS_PER_SIDE: ${n.MAX_BINARY_POINTS_PER_SIDE} → maxBinaryPointsPerSide: ${n.maxBinaryPointsPerSide}`),delete n.MAX_BINARY_POINTS_PER_SIDE),n.BINARY_POINT_VALUE&&(n.binaryPointValue=parseFloat(n.BINARY_POINT_VALUE),delete n.BINARY_POINT_VALUE),n.BINARY_MATCHING_ENABLED&&(n.binaryMatchingEnabled="true"===n.BINARY_MATCHING_ENABLED,delete n.BINARY_MATCHING_ENABLED),n.BINARY_MATCHING_SCHEDULE&&(n.binaryMatchingSchedule=n.BINARY_MATCHING_SCHEDULE,delete n.BINARY_MATCHING_SCHEDULE),n.THS_PRICE&&(n.thsPriceUSD=parseFloat(n.THS_PRICE),delete n.THS_PRICE),n.MINIMUM_PURCHASE&&(n.minPurchaseAmount=parseFloat(n.MINIMUM_PURCHASE),delete n.MINIMUM_PURCHASE),n.MAXIMUM_PURCHASE&&(n.maxPurchaseAmount=parseFloat(n.MAXIMUM_PURCHASE),delete n.MAXIMUM_PURCHASE),console.log("Settings object after mapping:",{maxBinaryPointsPerSide:n.maxBinaryPointsPerSide});let s={thsPriceUSD:50,minPurchaseAmount:100,maxPurchaseAmount:1e4,earningsRanges:[{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}],binaryBonusPercentage:10,referralBonusPercentage:5,maxBinaryPointsPerSide:10,binaryPointValue:10,binaryMatchingEnabled:!0,binaryMatchingSchedule:"Weekly at 15:00 UTC",usdtDepositAddress:"",minDepositAmount:10,maxDepositAmount:1e4,depositEnabled:!0,minConfirmations:1,depositFeePercentage:0,tronNetwork:"testnet",tronMainnetApiUrl:"https://api.trongrid.io",tronTestnetApiUrl:"https://api.shasta.trongrid.io",usdtMainnetContract:"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",usdtTestnetContract:"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs",minWithdrawalAmount:50,withdrawalFeeFixed:3,withdrawalFeePercentage:1,withdrawalProcessingDays:3,platformFeePercentage:1,maintenanceMode:!1,registrationEnabled:!0,kycRequired:!0,...n};return o.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Admin settings fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch settings"},{status:500})}}async function c(e){try{let{authenticated:t,user:a}=await (0,l.b9)(e);if(!t||!a)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,l.qc)(a.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let r=await e.json();console.log("Received settings for update:",r),console.log("Received maxBinaryPointsPerSide:",r.maxBinaryPointsPerSide,typeof r.maxBinaryPointsPerSide);let n={...r};["MAX_BINARY_POINTS_PER_SIDE","BINARY_POINT_VALUE","BINARY_MATCHING_ENABLED","BINARY_MATCHING_SCHEDULE","THS_PRICE","MINIMUM_PURCHASE","MAXIMUM_PURCHASE"].forEach(e=>{n[e]&&(console.log(`Removing conflicting database key: ${e} = ${n[e]}`),delete n[e])}),console.log("Cleaned settings for processing:",{maxBinaryPointsPerSide:n.maxBinaryPointsPerSide,binaryPointValue:n.binaryPointValue,binaryMatchingEnabled:n.binaryMatchingEnabled,binaryMatchingSchedule:n.binaryMatchingSchedule});let s={maxBinaryPointsPerSide:"MAX_BINARY_POINTS_PER_SIDE",binaryPointValue:"BINARY_POINT_VALUE",binaryMatchingEnabled:"BINARY_MATCHING_ENABLED",binaryMatchingSchedule:"BINARY_MATCHING_SCHEDULE",thsPriceUSD:"THS_PRICE",minPurchaseAmount:"MINIMUM_PURCHASE",maxPurchaseAmount:"MAXIMUM_PURCHASE"},i=Object.entries(n).map(async([e,t])=>{let r=s[e]||e;if(!s[e])return d.rs.set(r,JSON.stringify(t),a.id);{console.log(`Mapping ${e} (${t}) → ${r} (${String(t)})`);let n=await d.rs.set(r,String(t),a.id);return console.log(`Database update result for ${r}:`,n),n}});return await Promise.all(i),console.log("All settings updates completed"),await d.AJ.create({action:"SYSTEM_SETTINGS_UPDATED",userId:a.id,details:{updatedSettings:Object.keys(n)},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Settings updated successfully"})}catch(e){return console.error("Admin settings update error:",e),o.NextResponse.json({success:!1,error:"Failed to update settings"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/settings/route",pathname:"/api/admin/settings",filename:"route",bundlePath:"app/api/admin/settings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:A,serverHooks:P}=p;function y(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:A})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580,5315,3306],()=>a(60864));module.exports=r})();