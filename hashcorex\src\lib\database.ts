import { prisma } from './prisma';
import { User, MiningUnit, Transaction, Referral, BinaryPoints, WalletBalance, DepositTransaction, DepositStatus } from '@/types';

// User Database Operations
export const userDb = {
  async create(data: {
    email: string;
    firstName: string;
    lastName: string;
    password: string;
    referralId?: string;
  }) {
    return await prisma.user.create({
      data: {
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        password: data.password,
        referralId: data.referralId || undefined,
      },
    });
  },

  async findByEmail(email: string) {
    return await prisma.user.findUnique({
      where: { email },
      include: {
        miningUnits: true,
        transactions: true,
        binaryPoints: true,
      },
    });
  },

  async findById(id: string) {
    return await prisma.user.findUnique({
      where: { id },
      include: {
        miningUnits: true,
        transactions: true,
        binaryPoints: true,
      },
    });
  },

  async findByReferralId(referralId: string) {
    return await prisma.user.findUnique({
      where: { referralId },
    });
  },

  async update(id: string, data: Partial<{
    firstName: string;
    lastName: string;
    email: string;
    role: 'USER' | 'ADMIN';
    isActive: boolean;
    kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  }>) {
    return await prisma.user.update({
      where: { id },
      data,
    });
  },

  async updateKYCStatus(userId: string, status: 'PENDING' | 'APPROVED' | 'REJECTED') {
    return await prisma.user.update({
      where: { id: userId },
      data: { kycStatus: status },
    });
  },

  async updateWithdrawalAddress(email: string, withdrawalAddress: string | null) {
    return await prisma.user.update({
      where: { email },
      data: { withdrawalAddress },
    });
  },
};

// Mining Unit Database Operations
export const miningUnitDb = {
  async create(data: {
    userId: string;
    thsAmount: number;
    investmentAmount: number;
    dailyROI: number;
  }) {
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now

    return await prisma.miningUnit.create({
      data: {
        userId: data.userId,
        thsAmount: data.thsAmount,
        investmentAmount: data.investmentAmount,
        dailyROI: data.dailyROI,
        expiryDate,
      },
    });
  },

  async findActiveByUserId(userId: string) {
    return await prisma.miningUnit.findMany({
      where: {
        userId,
        status: 'ACTIVE',
        expiryDate: {
          gt: new Date(),
        },
      },
    });
  },

  async updateTotalEarned(unitId: string, amount: number) {
    return await prisma.miningUnit.update({
      where: { id: unitId },
      data: {
        totalEarned: {
          increment: amount,
        },
      },
    });
  },

  async expireUnit(unitId: string) {
    return await prisma.miningUnit.update({
      where: { id: unitId },
      data: { status: 'EXPIRED' },
    });
  },

  async findAllActive() {
    return await prisma.miningUnit.findMany({
      where: {
        status: 'ACTIVE',
        expiryDate: {
          gt: new Date(),
        },
      },
      include: {
        user: true,
      },
    });
  },

  async updateEarnings(unitId: string, earningType: 'mining' | 'referral' | 'binary', amount: number) {
    const updateData: any = { totalEarned: { increment: amount } };

    switch (earningType) {
      case 'mining':
        updateData.miningEarnings = { increment: amount };
        break;
      case 'referral':
        updateData.referralEarnings = { increment: amount };
        break;
      case 'binary':
        updateData.binaryEarnings = { increment: amount };
        break;
    }

    return await prisma.miningUnit.update({
      where: { id: unitId },
      data: updateData,
    });
  },
};

// Transaction Database Operations
export const transactionDb = {
  async create(data: {
    userId: string;
    type: 'MINING_EARNINGS' | 'DIRECT_REFERRAL' | 'BINARY_BONUS' | 'DEPOSIT' | 'WITHDRAWAL' | 'PURCHASE' | 'ADMIN_CREDIT' | 'ADMIN_DEBIT';
    amount: number;
    description: string;
    reference?: string;
    status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  }) {
    return await prisma.transaction.create({
      data: {
        userId: data.userId,
        type: data.type,
        amount: data.amount,
        description: data.description,
        reference: data.reference,
        status: data.status || 'PENDING',
      },
    });
  },

  async findByUserId(userId: string, filters?: {
    types?: string[];
    status?: string;
    limit?: number;
    offset?: number;
    includeUser?: boolean;
    search?: string;
  }) {
    const where: any = { userId };

    if (filters?.types && filters.types.length > 0) {
      where.type = { in: filters.types };
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.search) {
      where.OR = [
        { description: { contains: filters.search, mode: 'insensitive' } },
        { type: { contains: filters.search, mode: 'insensitive' } },
        { reference: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    const include = filters?.includeUser ? {
      user: {
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
        },
      },
    } : undefined;

    return await prisma.transaction.findMany({
      where,
      include,
      orderBy: { createdAt: 'desc' },
      take: filters?.limit || 50,
      skip: filters?.offset,
    });
  },

  async updateStatus(
    transactionId: string,
    status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED',
    additionalData?: {
      amount?: number;
      description?: string;
    }
  ) {
    const updateData: any = { status };

    if (additionalData?.amount !== undefined) {
      updateData.amount = additionalData.amount;
    }

    if (additionalData?.description) {
      updateData.description = additionalData.description;
    }

    return await prisma.transaction.update({
      where: { id: transactionId },
      data: updateData,
    });
  },

  async findPendingByTypeAndDescription(
    userId: string,
    type: string,
    descriptionPattern: string
  ) {
    return await prisma.transaction.findFirst({
      where: {
        userId,
        type,
        description: {
          contains: descriptionPattern,
        },
        status: 'PENDING',
      },
    });
  },

  async updateByReference(
    reference: string,
    type: string,
    status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED',
    additionalData?: {
      amount?: number;
      description?: string;
    }
  ) {
    const updateData: any = { status };

    if (additionalData?.amount !== undefined) {
      updateData.amount = additionalData.amount;
    }

    if (additionalData?.description) {
      updateData.description = additionalData.description;
    }

    return await prisma.transaction.updateMany({
      where: {
        reference,
        type,
        status: 'PENDING', // Only update pending transactions
      },
      data: updateData,
    });
  },
};

// Referral Database Operations
export const referralDb = {
  async create(data: {
    referrerId: string;
    referredId: string;
    placementSide: 'LEFT' | 'RIGHT';
  }) {
    return await prisma.referral.create({
      data: {
        referrerId: data.referrerId,
        referredId: data.referredId,
        placementSide: data.placementSide,
      },
    });
  },

  async findByReferrerId(referrerId: string) {
    return await prisma.referral.findMany({
      where: { referrerId },
      include: {
        referred: {
          select: {
            id: true,
            email: true,
            createdAt: true,
          },
        },
      },
    });
  },
};

// Binary Points Database Operations
export const binaryPointsDb = {
  async upsert(data: {
    userId: string;
    leftPoints?: number;
    rightPoints?: number;
  }) {
    // Round to 2 decimal places to ensure precision
    const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;
    const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;

    return await prisma.binaryPoints.upsert({
      where: { userId: data.userId },
      update: {
        leftPoints: leftPoints !== undefined ? { increment: leftPoints } : undefined,
        rightPoints: rightPoints !== undefined ? { increment: rightPoints } : undefined,
      },
      create: {
        userId: data.userId,
        leftPoints: leftPoints || 0,
        rightPoints: rightPoints || 0,
      },
    });
  },

  async findByUserId(userId: string) {
    return await prisma.binaryPoints.findUnique({
      where: { userId },
    });
  },

  async resetPoints(userId: string, leftPoints: number, rightPoints: number) {
    return await prisma.binaryPoints.update({
      where: { userId },
      data: {
        leftPoints,
        rightPoints,
        flushDate: new Date(),
      },
    });
  },
};

// Withdrawal Database Operations
export const withdrawalDb = {
  async create(data: {
    userId: string;
    amount: number;
    usdtAddress: string;
  }) {
    return await prisma.withdrawalRequest.create({
      data: {
        userId: data.userId,
        amount: data.amount,
        usdtAddress: data.usdtAddress,
      },
    });
  },

  async findPending() {
    return await prisma.withdrawalRequest.findMany({
      where: { status: 'PENDING' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            kycStatus: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });
  },

  async updateStatus(
    requestId: string, 
    status: 'APPROVED' | 'REJECTED' | 'COMPLETED',
    processedBy?: string,
    txid?: string,
    rejectionReason?: string
  ) {
    return await prisma.withdrawalRequest.update({
      where: { id: requestId },
      data: {
        status,
        processedBy,
        txid,
        rejectionReason,
        processedAt: new Date(),
      },
    });
  },
};

// Admin Settings Database Operations
export const adminSettingsDb = {
  async get(key: string) {
    const setting = await prisma.adminSettings.findUnique({
      where: { key },
    });
    return setting?.value;
  },

  async set(key: string, value: string, updatedBy?: string) {
    return await prisma.adminSettings.upsert({
      where: { key },
      update: { value },
      create: { key, value },
    });
  },

  async getAll() {
    return await prisma.adminSettings.findMany();
  },
};

// System Logs
export const systemLogDb = {
  async create(data: {
    action: string;
    userId?: string;
    adminId?: string;
    details?: any;
    ipAddress?: string;
    userAgent?: string;
  }) {
    return await prisma.systemLog.create({
      data: {
        action: data.action,
        userId: data.userId,
        adminId: data.adminId,
        details: data.details ? JSON.stringify(data.details) : null,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
      },
    });
  },
};

// Wallet Balance Database Operations
export const walletBalanceDb = {
  async getOrCreate(userId: string): Promise<WalletBalance> {
    let walletBalance = await prisma.walletBalance.findUnique({
      where: { userId },
    });

    if (!walletBalance) {
      walletBalance = await prisma.walletBalance.create({
        data: {
          userId,
          availableBalance: 0,
          pendingBalance: 0,
          totalDeposits: 0,
          totalWithdrawals: 0,
          totalEarnings: 0,
        },
      });
    }

    return walletBalance as WalletBalance;
  },

  async updateBalance(userId: string, updates: {
    availableBalance?: number;
    pendingBalance?: number;
    totalDeposits?: number;
    totalWithdrawals?: number;
    totalEarnings?: number;
  }) {
    return await prisma.walletBalance.update({
      where: { userId },
      data: {
        ...updates,
        lastUpdated: new Date(),
      },
    });
  },

  async addDeposit(userId: string, amount: number) {
    const wallet = await this.getOrCreate(userId);
    return await prisma.walletBalance.update({
      where: { userId },
      data: {
        availableBalance: wallet.availableBalance + amount,
        totalDeposits: wallet.totalDeposits + amount,
        lastUpdated: new Date(),
      },
    });
  },

  async addEarnings(userId: string, amount: number) {
    const wallet = await this.getOrCreate(userId);
    return await prisma.walletBalance.update({
      where: { userId },
      data: {
        availableBalance: wallet.availableBalance + amount,
        totalEarnings: wallet.totalEarnings + amount,
        lastUpdated: new Date(),
      },
    });
  },

  async deductWithdrawal(userId: string, amount: number) {
    const wallet = await this.getOrCreate(userId);
    if (wallet.availableBalance < amount) {
      throw new Error('Insufficient balance');
    }

    return await prisma.walletBalance.update({
      where: { userId },
      data: {
        availableBalance: wallet.availableBalance - amount,
        totalWithdrawals: wallet.totalWithdrawals + amount,
        lastUpdated: new Date(),
      },
    });
  },

  async findByUserId(userId: string) {
    return await this.getOrCreate(userId);
  },
};

// Deposit Transaction Database Operations
export const depositTransactionDb = {
  async create(data: {
    userId: string;
    transactionId: string;
    amount: number;
    usdtAmount: number;
    tronAddress: string;
    senderAddress?: string;
    blockNumber?: string;
    blockTimestamp?: Date;
    confirmations?: number;
  }) {
    return await prisma.depositTransaction.create({
      data: {
        userId: data.userId,
        transactionId: data.transactionId,
        amount: data.amount,
        usdtAmount: data.usdtAmount,
        tronAddress: data.tronAddress,
        senderAddress: data.senderAddress,
        blockNumber: data.blockNumber,
        blockTimestamp: data.blockTimestamp,
        confirmations: data.confirmations || 0,
        status: 'PENDING',
      },
    });
  },

  async findByTransactionId(transactionId: string) {
    return await prisma.depositTransaction.findUnique({
      where: { transactionId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  },

  async findByUserId(userId: string, filters?: {
    status?: DepositStatus;
    limit?: number;
    offset?: number;
  }) {
    const where: any = { userId };

    if (filters?.status) {
      where.status = filters.status;
    }

    return await prisma.depositTransaction.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: filters?.limit || 50,
      skip: filters?.offset,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  },

  async findAll(filters?: {
    status?: DepositStatus;
    limit?: number;
    offset?: number;
  }) {
    const where: any = {};

    if (filters?.status) {
      where.status = filters.status;
    }

    return await prisma.depositTransaction.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: filters?.limit || 100,
      skip: filters?.offset,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  },

  async updateStatus(
    transactionId: string,
    status: DepositStatus,
    updates?: {
      verifiedAt?: Date;
      processedAt?: Date;
      failureReason?: string;
      confirmations?: number;
    }
  ) {
    const updateData: any = { status };

    if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;
    if (updates?.processedAt) updateData.processedAt = updates.processedAt;
    if (updates?.failureReason) updateData.failureReason = updates.failureReason;
    if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;

    return await prisma.depositTransaction.update({
      where: { transactionId },
      data: updateData,
    });
  },

  async markAsCompleted(transactionId: string) {
    return await this.updateStatus(transactionId, 'COMPLETED', {
      processedAt: new Date(),
    });
  },

  async markAsFailed(transactionId: string, reason: string) {
    return await this.updateStatus(transactionId, 'FAILED', {
      failureReason: reason,
      processedAt: new Date(),
    });
  },

  async getPendingDeposits() {
    return await this.findAll({ status: 'PENDING' });
  },

  async getPendingVerificationDeposits() {
    return await this.findAll({ status: 'PENDING_VERIFICATION' });
  },

  async getWaitingForConfirmationsDeposits() {
    return await this.findAll({ status: 'WAITING_FOR_CONFIRMATIONS' });
  },

  async findByStatus(status: DepositStatus) {
    return await prisma.depositTransaction.findMany({
      where: { status },
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  },

  async updateConfirmations(transactionId: string, confirmations: number) {
    return await prisma.depositTransaction.update({
      where: { transactionId },
      data: { confirmations },
    });
  },

  async getDepositStats() {
    const stats = await prisma.depositTransaction.aggregate({
      _count: {
        id: true,
      },
      _sum: {
        usdtAmount: true,
      },
      where: {
        status: { in: ['COMPLETED', 'CONFIRMED'] },
      },
    });

    const pendingCount = await prisma.depositTransaction.count({
      where: {
        status: {
          in: ['PENDING', 'PENDING_VERIFICATION', 'WAITING_FOR_CONFIRMATIONS']
        }
      },
    });

    return {
      totalDeposits: stats._count.id || 0,
      totalAmount: stats._sum.usdtAmount || 0,
      pendingDeposits: pendingCount,
    };
  },
};

// Support Ticket Database Operations
export const supportTicketDb = {
  create: async (data: any) => {
    return await prisma.supportTicket.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        responses: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
      },
    });
  },

  findByUserId: async (userId: string) => {
    return await prisma.supportTicket.findMany({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        responses: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  },

  findById: async (id: string) => {
    return await prisma.supportTicket.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        responses: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
      },
    });
  },

  findAll: async () => {
    return await prisma.supportTicket.findMany({
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        responses: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  },

  updateStatus: async (id: string, status: any) => {
    return await prisma.supportTicket.update({
      where: { id },
      data: { status, updatedAt: new Date() },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        responses: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
      },
    });
  },
};

// Ticket Response Database Operations
export const ticketResponseDb = {
  create: async (data: any) => {
    return await prisma.ticketResponse.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  },

  findByTicketId: async (ticketId: string) => {
    return await prisma.ticketResponse.findMany({
      where: { ticketId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });
  },
};

// Export prisma instance for direct database operations
export { prisma };
