"use strict";exports.id=9480,exports.ids=[9480],exports.modules={47034:function(t,r,e){var o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(r,"__esModule",{value:!0}),r.toHex=r.fromHex=void 0;let n=o(e(55511)),a=e(53607),s=t=>n.default.createHash("sha256").update(Buffer.from(t,"hex")).digest("hex");r.fromHex=t=>{let r=`41${t.substring(2)}`,e=s(s(r)).substring(0,8),o=Buffer.from(r+e,"hex");return(0,a.encode58)(o)},r.toHex=t=>{if(t.length<=4)throw Error("Invalid address provided");let r=Buffer.from((0,a.decode58)(t)).toString("hex"),e=r.substring(r.length-8,r.length),o=s(s(r=r.substring(0,r.length-8))).substring(0,8);if(`${e}`==`${o}`)return`0x${r.substring(2)}`;throw Error("Invalid address provided")}},53607:(t,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.decode58=r.encode58=void 0;let e="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz",o=e.split("").reduce((t,r,e)=>(t[r]=e,t),{});r.encode58=t=>{if(0===t.length)return"";let r=[0];for(let e=0;e<t.length;e++){for(let t=0;t<r.length;t++)r[t]<<=8;r[0]+=t[e];let o=0;for(let t=0;t<r.length;++t)r[t]+=o,o=r[t]/58|0,r[t]%=58;for(;o;)r.push(o%58),o=o/58|0}for(let e=0;0===t[e]&&e<t.length-1;e++)r.push(0);return[...r].reverse().map(t=>e[t]).join("")},r.decode58=t=>{if(0===t.length)return[];let r=[0];for(let e=0;e<t.length;e++){let n=t[e];if(!(n in o))throw Error("Non-base58 character");for(let t=0;t<r.length;t++)r[t]*=58;r[0]+=o[n];let a=0;for(let t=0;t<r.length;++t)r[t]+=a,a=r[t]>>8,r[t]&=255;for(;a;)r.push(255&a),a>>=8}for(let e=0;"1"===t[e]&&e<t.length-1;e++)r.push(0);return r.reverse()}},59480:(t,r,e)=>{e.d(r,{TA:()=>p,af:()=>b,gp:()=>m,i4:()=>h});var o=e(6710),n=e(47034);let a=process.env.TRONGRID_API_KEY,s=0;async function i(){try{let t=await o.rs.get("tronNetwork")||"testnet",r=await o.rs.get("tronMainnetApiUrl")||"https://api.trongrid.io",e=await o.rs.get("tronTestnetApiUrl")||"https://api.shasta.trongrid.io",n=await o.rs.get("usdtMainnetContract")||"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",a=await o.rs.get("usdtTestnetContract")||"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs";r=r.replace(/['"]/g,"").trim(),e=e.replace(/['"]/g,"").trim(),n=n.replace(/['"]/g,"").trim(),a=a.replace(/['"]/g,"").trim(),r=r.replace(/\/$/,""),e=e.replace(/\/$/,"");let s="mainnet"===t,i=s?r:e,c=s?n:a;return console.log("Tron network config:",{network:t,apiUrl:i,usdtContract:c}),{apiUrl:i,usdtContract:c,network:t}}catch(t){return console.error("Error getting Tron network config, using testnet defaults:",t),{apiUrl:"https://api.shasta.trongrid.io",usdtContract:"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs",network:"testnet"}}}async function c(){let t=Date.now()-s;if(t<1e3){let r=1e3-t;await new Promise(t=>setTimeout(t,r))}s=Date.now()}async function l(t,r){try{let e=r||await i(),o=`${e.apiUrl}/walletsolidity/gettransactionbyid`;console.log("Fetching transaction by ID:",{txId:t,url:o,network:e.network});let n=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({value:t})});if(!n.ok){let t=await n.text();throw console.error("Trongrid API error response:",{status:n.status,statusText:n.statusText,body:t}),Error(`Trongrid API error: ${n.status} ${n.statusText}`)}let a=await n.json();return console.log("Transaction data received:",a),a.txID?a:null}catch(r){return console.error("Error fetching transaction:",{txId:t,error:r instanceof Error?r.message:r,stack:r instanceof Error?r.stack:void 0}),null}}async function d(t,r){try{let e=r||await i(),o=`${e.apiUrl}/walletsolidity/gettransactioninfobyid`;console.log("Fetching transaction info:",{txId:t,url:o,network:e.network});let n=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({value:t})});if(!n.ok){let t=await n.text();throw console.error("Trongrid API error response:",{status:n.status,statusText:n.statusText,body:t}),Error(`Trongrid API error: ${n.status} ${n.statusText}`)}let a=await n.json();return console.log("Transaction info received:",a),a.id?a:null}catch(r){return console.error("Error fetching transaction info:",{txId:t,error:r instanceof Error?r.message:r,stack:r instanceof Error?r.stack:void 0}),null}}async function u(t){try{let r=t||await i(),e=`${r.apiUrl}/walletsolidity/getnowblock`;console.log("Fetching current block:",{url:e,network:r.network});let o=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"}});if(!o.ok){let t=await o.text();throw console.error("Trongrid API error response:",{status:o.status,statusText:o.statusText,body:t}),Error(`Trongrid API error: ${o.status} ${o.statusText}`)}let n=await o.json();return console.log("Current block data received:",n),n.block_header?{blockNumber:n.block_header.raw_data.number}:null}catch(t){return console.error("Error fetching current block:",{error:t instanceof Error?t.message:t,stack:t instanceof Error?t.stack:void 0}),null}}function f(t){return parseInt(t,16)}function g(t){try{let r=t.startsWith("0x")?t.slice(2):t;r.length<40&&(r="0".repeat(40-r.length)+r);let e="41"+r;return(0,n.fromHex)(e)}catch(r){return console.error("Error converting hex to Tron address:",r),`T${t.slice(-30)}`}}async function m(t,r,e=1){let o=await i();console.log("Verifying USDT transaction:",{txId:t,expectedToAddress:r,minConfirmations:e,network:o.network,apiUrl:o.apiUrl,usdtContract:o.usdtContract});try{let n=await l(t,o);if(console.log("Transaction details:",n),!n)return console.log("Transaction not found"),{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:o.usdtContract,blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:t};let a=await d(t,o);if(console.log("Transaction info:",a),!a)return console.log("Transaction info not found"),{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:o.usdtContract,blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:t};if(console.log("Transaction receipt result:",a.receipt?.result),a.receipt?.result!=="SUCCESS")return console.log("Transaction failed or not successful"),{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:o.usdtContract,blockNumber:a.blockNumber,blockTimestamp:a.blockTimeStamp,confirmations:0,transactionId:t};let s=function(t,r,e){if(console.log("Parsing USDT transfer from transaction and logs:",r.length,"Contract:",e),t?.raw_data?.contract?.[0]?.parameter?.value){let r=t.raw_data.contract[0].parameter.value;if(g(r.contract_address?.replace("41","")||"").toLowerCase()===e.toLowerCase()&&r.data)try{let t=r.data;if(t.startsWith("a9059cbb")){console.log("Found USDT transfer in transaction data");let e=t.slice(8,72).slice(24),o=g(e),n=t.slice(72),a=f(n)/1e6,s=g(r.owner_address?.replace("41","")||"");return console.log("Parsed transfer from transaction data:",{fromAddress:s,toAddress:o,amount:a,recipientHex:e,amountHex:n}),{amount:a,fromAddress:s,toAddress:o}}}catch(t){console.error("Error parsing transaction data:",t)}}let o=r.find(t=>{let r=g(t.address).toLowerCase()===e.toLowerCase(),o=t.topics.length>=3,n="ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"===t.topics[0];return r&&o&&n});if(!o)return console.log("No USDT transfer found in transaction data or logs"),null;try{console.log("Parsing from logs as fallback:",o);let t=o.topics[1].slice(26),r=o.topics[2].slice(26),e=o.data.startsWith("0x")?o.data.slice(2):o.data,n=g(t),a=g(r);return{amount:f(e)/1e6,fromAddress:n,toAddress:a}}catch(t){return console.error("Error parsing USDT transfer from logs:",t),null}}(n,a.log||[],o.usdtContract);if(!s)return console.log("No USDT transfer details found"),{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:o.usdtContract,blockNumber:a.blockNumber,blockTimestamp:a.blockTimeStamp,confirmations:0,transactionId:t};let i=await u(o),c=0;i&&a.blockNumber&&(c=i.blockNumber-a.blockNumber),console.log("Confirmation calculation:",{currentBlockNumber:i?.blockNumber,transactionBlockNumber:a.blockNumber,confirmations:c,minConfirmations:e});let m=s.toAddress.toLowerCase()===r.toLowerCase();console.log("Address verification:",{transferToAddress:s.toAddress,expectedToAddress:r,isValidRecipient:m,addressesMatch:s.toAddress.toLowerCase()===r.toLowerCase()});let p=m&&c>=e&&s.amount>0;return console.log("Final verification result:",{isValid:p,isValidRecipient:m,confirmations:c,minConfirmations:e,amount:s.amount}),{isValid:p,amount:s.amount,fromAddress:s.fromAddress,toAddress:s.toAddress,contractAddress:o.usdtContract,blockNumber:a.blockNumber,blockTimestamp:a.blockTimeStamp,confirmations:Math.max(0,c),transactionId:t}}catch(r){return console.error("Error verifying USDT transaction:",r),{isValid:!1,amount:0,fromAddress:"",toAddress:"",contractAddress:o.usdtContract,blockNumber:0,blockTimestamp:0,confirmations:0,transactionId:t}}}function p(t){return/^[a-fA-F0-9]{64}$/.test(t)}function b(t){return/^T[A-Za-z1-9]{33}$/.test(t)}async function h(){return await i()}}};