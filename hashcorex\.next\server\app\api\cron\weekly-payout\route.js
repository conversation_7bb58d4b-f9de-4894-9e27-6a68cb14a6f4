"use strict";(()=>{var e={};e.id=6303,e.ids=[6303],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39794:(e,n,t)=>{t.d(n,{Py:()=>s,k8:()=>c,kp:()=>u});var r=t(31183),a=t(6710);async function i(e){return await r.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,n,t,s,u){let c=await i(e);if(0===c.length)throw Error("No active mining units found for earnings allocation");let l=[],d=n;for(let e of c){var p;if(d<=0)break;let n=Math.max(0,5*(p=e).investmentAmount-(p.miningEarnings+p.referralEarnings+p.binaryEarnings));if(n<=0)continue;let a=Math.min(d,n);if(a>0){let i={};switch(t){case"MINING_EARNINGS":i.miningEarnings={increment:a};break;case"DIRECT_REFERRAL":i.referralEarnings={increment:a};break;case"BINARY_BONUS":i.binaryEarnings={increment:a}}i.totalEarned={increment:a},await r.prisma.miningUnit.update({where:{id:e.id},data:i}),await r.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:t,amount:a,description:u}}),l.push({miningUnitId:e.id,amount:a,remainingCapacity:n-a}),d-=a;let c=await r.prisma.miningUnit.findUnique({where:{id:e.id}});c&&function(e){let n=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=n}(c)&&await o(e.id,"5x_investment_reached")}}return d>0&&(console.warn(`Unable to allocate ${d} to mining units - all units at capacity`),await a.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:n,allocatedAmount:n-d,overflowAmount:d,earningType:t,reason:"all_units_at_capacity"}})),l}async function o(e,n){let t=await r.prisma.miningUnit.findUnique({where:{id:e}});if(!t)throw Error(`Mining unit ${e} not found`);await r.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let i=t.miningEarnings+t.referralEarnings+t.binaryEarnings;await a.AJ.create({action:"MINING_UNIT_EXPIRED",userId:t.userId,details:{miningUnitId:e,reason:n,totalEarned:i,miningEarnings:t.miningEarnings,referralEarnings:t.referralEarnings,binaryEarnings:t.binaryEarnings,investmentAmount:t.investmentAmount,multiplier:i/t.investmentAmount}}),console.log(`Mining unit ${e} expired due to ${n}. Total earnings: ${i}`)}async function u(e){return await r.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function c(e){return await r.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77158:(e,n,t)=>{t.r(n),t.d(n,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var r={};t.r(r),t.d(r,{POST:()=>l});var a=t(96559),i=t(48088),s=t(37719),o=t(32190),u=t(92731),c=t(6710);async function l(e){try{let n=e.headers.get("authorization"),t=process.env.CRON_SECRET||"default-secret";if(n!==`Bearer ${t}`)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});console.log("Starting weekly earnings payout cron job...");let r=await (0,u.Oh)();console.log(`Processed earnings for ${r.length} users`);let a=r.reduce((e,n)=>e+n.totalEarnings,0);return await c.AJ.create({action:"WEEKLY_PAYOUT_CRON_EXECUTED",details:{usersProcessed:r.length,totalDistributed:a,executionTime:new Date().toISOString(),payoutDay:"Sunday",payoutTime:"18:30 UTC (00:00 AM GMT+5:30)"}}),o.NextResponse.json({success:!0,message:"Weekly earnings payout completed",data:{usersProcessed:r.length,totalDistributed:a}})}catch(e){return console.error("Weekly payout cron job error:",e),await c.AJ.create({action:"WEEKLY_PAYOUT_CRON_ERROR",details:{error:e.message,stack:e.stack,timestamp:new Date().toISOString()}}),o.NextResponse.json({success:!1,error:"Weekly payout failed"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cron/weekly-payout/route",pathname:"/api/cron/weekly-payout",filename:"route",bundlePath:"app/api/cron/weekly-payout/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\weekly-payout\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:m}=d;function y(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},96330:e=>{e.exports=require("@prisma/client")}};var n=require("../../../../webpack-runtime.js");n.C(e);var t=e=>n(n.s=e),r=n.X(0,[4447,580,3306,5112],()=>t(77158));module.exports=r})();