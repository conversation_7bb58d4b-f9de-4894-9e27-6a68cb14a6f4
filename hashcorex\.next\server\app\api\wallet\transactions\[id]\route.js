"use strict";(()=>{var e={};e.id=9091,e.ids=[9091],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>x,DY:()=>m,Lx:()=>w,Oj:()=>y,b9:()=>h,qc:()=>A});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:d}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},m=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await l(e.password),i=!1;do a=f(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),A=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},94055:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>u});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),d=r(12909),l=r(31183);async function u(e,{params:t}){try{let{authenticated:r,user:a}=await (0,d.b9)(e);if(!r||!a)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=t.id,i=await l.prisma.transaction.findUnique({where:{id:s,userId:a.id}});if(!i)return o.NextResponse.json({success:!1,error:"Transaction not found"},{status:404});let n={};if("WITHDRAWAL"===i.type&&i.reference){let e=await l.prisma.withdrawalRequest.findUnique({where:{id:i.reference}});e&&(n={txid:e.txid,usdtAddress:e.usdtAddress,rejectionReason:e.rejectionReason,processedAt:e.processedAt,withdrawalStatus:e.status})}else if("DEPOSIT"===i.type){let e=i.description.match(/TX: ([a-fA-F0-9]+)/);if(e){let t=e[1],r=await l.prisma.depositTransaction.findUnique({where:{transactionId:t}});r&&(n={txid:r.transactionId,usdtAddress:r.tronAddress,confirmations:r.confirmations,blockNumber:r.blockNumber,senderAddress:r.senderAddress,depositStatus:r.status})}}let u={id:i.id,type:i.type,amount:i.amount,description:i.description,status:i.status,reference:i.reference,createdAt:i.createdAt,...n};return o.NextResponse.json({success:!0,data:u})}catch(e){return console.error("Transaction details fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch transaction details"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/transactions/[id]/route",pathname:"/api/wallet/transactions/[id]",filename:"route",bundlePath:"app/api/wallet/transactions/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\transactions\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:h}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(94055));module.exports=a})();