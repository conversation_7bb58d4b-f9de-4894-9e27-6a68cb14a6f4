import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch all users' binary points data
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Fetch all binary points data with user information
    const binaryPointsData = await prisma.binaryPoints.findMany({
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            createdAt: true,
          },
        },
      },
      orderBy: [
        { leftPoints: 'desc' },
        { rightPoints: 'desc' },
      ],
    });

    return NextResponse.json({
      success: true,
      data: binaryPointsData,
    });

  } catch (error: any) {
    console.error('Binary points data fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch binary points data' },
      { status: 500 }
    );
  }
}

// POST - Update user's binary points (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { userId, leftPoints, rightPoints, action } = body;

    // Validation
    if (!userId || (!leftPoints && !rightPoints && !action)) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data' },
        { status: 400 }
      );
    }

    let result;

    if (action === 'reset') {
      // Reset user's binary points to 0
      result = await prisma.binaryPoints.upsert({
        where: { userId },
        update: {
          leftPoints: 0,
          rightPoints: 0,
          flushDate: new Date(),
        },
        create: {
          userId,
          leftPoints: 0,
          rightPoints: 0,
          flushDate: new Date(),
        },
      });
    } else if (action === 'set') {
      // Set specific values
      result = await prisma.binaryPoints.upsert({
        where: { userId },
        update: {
          leftPoints: leftPoints || 0,
          rightPoints: rightPoints || 0,
        },
        create: {
          userId,
          leftPoints: leftPoints || 0,
          rightPoints: rightPoints || 0,
        },
      });
    } else {
      // Add to existing points
      result = await prisma.binaryPoints.upsert({
        where: { userId },
        update: {
          leftPoints: { increment: leftPoints || 0 },
          rightPoints: { increment: rightPoints || 0 },
        },
        create: {
          userId,
          leftPoints: leftPoints || 0,
          rightPoints: rightPoints || 0,
        },
      });
    }

    // Log the admin action
    await prisma.systemLog.create({
      data: {
        action: 'ADMIN_BINARY_POINTS_UPDATE',
        details: JSON.stringify({
          adminId: user.id,
          adminEmail: user.email,
          targetUserId: userId,
          action,
          leftPoints,
          rightPoints,
          timestamp: new Date().toISOString(),
        }),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Binary points updated successfully',
      data: result,
    });

  } catch (error: any) {
    console.error('Binary points update error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to update binary points' },
      { status: 500 }
    );
  }
}
