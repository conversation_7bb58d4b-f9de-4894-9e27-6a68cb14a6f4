"use strict";(()=>{var e={};e.id=926,e.ids=[926],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49106:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var n={};t.r(n),t.d(n,{GET:()=>p});var s=t(96559),a=t(48088),i=t(37719),o=t(32190),u=t(6710);async function p(e){try{let e=await u.rs.getAll(),r={};e.forEach(e=>{try{r[e.key]=JSON.parse(e.value)}catch{r[e.key]=e.value}});let t={thsPriceUSD:50,minPurchaseAmount:100,maxPurchaseAmount:1e4,earningsRanges:[{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}],...r},n={thsPrice:t.thsPriceUSD,minPurchaseAmount:t.minPurchaseAmount,maxPurchaseAmount:t.maxPurchaseAmount,earningsRanges:t.earningsRanges};return o.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Pricing fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch pricing settings"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/settings/pricing/route",pathname:"/api/admin/settings/pricing",filename:"route",bundlePath:"app/api/admin/settings/pricing/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\settings\\pricing\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:l}=c;function x(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,580,3306],()=>t(49106));module.exports=n})();