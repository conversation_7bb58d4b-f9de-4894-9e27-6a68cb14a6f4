"use strict";(()=>{var e={};e.id=1776,e.ids=[1776],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>f,Lx:()=>w,Oj:()=>y,b9:()=>h,qc:()=>x});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await a.Ay.hash(e,12),c=async(e,t)=>await a.Ay.compare(e,t),u=e=>i().sign(e,o,{expiresIn:l}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await c(e.password,t.password))throw Error("Invalid email or password");return{token:u({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38974:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>c});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(12909),d=r(31183);async function c(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),s=a.get("search")||"",i=a.get("action")||"",n=a.get("userId")||"",c=a.get("dateFrom")||"",u=a.get("dateTo")||"",p={};if(s&&(p.OR=[{action:{contains:s,mode:"insensitive"}},{details:{contains:s,mode:"insensitive"}},{ipAddress:{contains:s,mode:"insensitive"}},{userAgent:{contains:s,mode:"insensitive"}}]),i&&"all"!==i&&("ERROR"===i?p.action={contains:"ERROR",mode:"insensitive"}:"LOGIN"===i?p.action={contains:"LOGIN",mode:"insensitive"}:"ADMIN"===i?p.action={contains:"ADMIN",mode:"insensitive"}:"PAYMENT"===i?p.OR=[{action:{contains:"PAYMENT",mode:"insensitive"}},{action:{contains:"WALLET",mode:"insensitive"}},{action:{contains:"DEPOSIT",mode:"insensitive"}},{action:{contains:"WITHDRAWAL",mode:"insensitive"}}]:p.action={contains:i,mode:"insensitive"}),n&&(p.userId=n),(c||u)&&(p.createdAt={},c&&(p.createdAt.gte=new Date(c)),u)){let e=new Date(u);e.setHours(23,59,59,999),p.createdAt.lte=e}let m=(await d.prisma.systemLog.findMany({where:p,include:{user:{select:{firstName:!0,lastName:!0,email:!0}}},orderBy:{createdAt:"desc"}})).map(e=>[e.createdAt.toISOString(),e.action,e.user?`${e.user.firstName} ${e.user.lastName}`:"System",e.user?.email||"N/A",e.details||"",e.ipAddress||"",e.userAgent||""]),h=["Date,Action,User,Email,Details,IP Address,User Agent",...m.map(e=>e.map(e=>`"${String(e).replace(/"/g,'""')}"`).join(","))].join("\n");return new o.NextResponse(h,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="system-logs-${new Date().toISOString().split("T")[0]}.csv"`}})}catch(e){return console.error("Admin logs export error:",e),o.NextResponse.json({success:!1,error:"Failed to export logs"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/logs/export/route",pathname:"/api/admin/logs/export",filename:"route",bundlePath:"app/api/admin/logs/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\logs\\export\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:h}=u;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(38974));module.exports=a})();