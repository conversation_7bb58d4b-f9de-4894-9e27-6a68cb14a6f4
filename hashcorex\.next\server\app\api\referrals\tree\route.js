"use strict";(()=>{var e={};e.id=3355,e.ids=[3355],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>m,Lx:()=>w,Oj:()=>y,b9:()=>h,qc:()=>x});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let l=process.env.JWT_SECRET||"fallback-secret-key",o=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,l,{expiresIn:o}),p=e=>{try{return i().verify(e,l)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},m=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=f(),i=!await n.Gy.findByReferralId(a);while(!i);let l=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,l.id,s)}return{id:l.id,email:l.email,referralId:l.referralId,kycStatus:l.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},28367:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>f,serverHooks:()=>w,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>p});var s=r(96559),i=r(48088),n=r(37719),l=r(32190),o=r(12909),d=r(2746),u=r(6710),c=r(31183);async function p(e){try{let{authenticated:t,user:r}=await (0,o.b9)(e);if(!t||!r)return l.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{searchParams:a}=new URL(e.url),s=parseInt(a.get("depth")||"3"),i="true"===a.get("enhanced"),n=a.get("expanded")?new Set(a.get("expanded").split(",").filter(e=>e.length>0)):new Set,p=a.get("nodeId");if(p){let e=await (0,d.OZ)(p);return l.NextResponse.json({success:!0,data:e})}let f=await (0,d.PM)(r.id,Math.min(s,50),n),h=await c.prisma.user.findMany({where:{referrerId:r.id},select:{id:!0,email:!0,firstName:!0,lastName:!0,isActive:!0,createdAt:!0}}),m=await u.cc.findByReferrerId(r.id),w=await u.FW.findByUserId(r.id),y=m.filter(e=>"LEFT"===e.placementSide).length,g=m.filter(e=>"RIGHT"===e.placementSide).length,x=await (0,d.l6)(r.id),I=(await u.DR.findByUserId(r.id,{types:["DIRECT_REFERRAL","BINARY_BONUS"],status:"COMPLETED"})).reduce((e,t)=>e+t.amount,0),R={treeStructure:f,statistics:{totalDirectReferrals:h.length,leftPlacements:y,rightPlacements:g,leftReferrals:x.left,rightReferrals:x.right,leftTeam:x.left,rightTeam:x.right,totalTeam:x.total,totalCommissions:I,binaryPoints:w||{leftPoints:0,rightPoints:0,matchedPoints:0}},referralLinks:{left:`http://localhost:3000/register?ref=${r.referralId}&side=left`,right:`http://localhost:3000/register?ref=${r.referralId}&side=right`,general:`http://localhost:3000/register?ref=${r.referralId}`}};if(i){let e=await (0,d.fB)(r.id),t=await (0,d.g5)(r.id);R.statistics.detailedStats=e,R.statistics.treeHealth=t}return l.NextResponse.json({success:!0,data:R})}catch(e){return console.error("Binary tree fetch error:",e),l.NextResponse.json({success:!1,error:"Failed to fetch binary tree"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/referrals/tree/route",pathname:"/api/referrals/tree",filename:"route",bundlePath:"app/api/referrals/tree/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\referrals\\tree\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:w}=f;function y(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306,2746],()=>r(28367));module.exports=a})();