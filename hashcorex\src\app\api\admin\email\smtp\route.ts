import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/database';

// GET - Get SMTP configuration
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check admin role
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get active SMTP configuration
    const smtpConfig = await prisma.sMTPConfiguration.findFirst({
      where: { isActive: true },
    });

    if (!smtpConfig) {
      return NextResponse.json({
        success: true,
        data: null,
      });
    }

    // Don't return the actual password for security
    const safeConfig = {
      ...smtpConfig,
      password: smtpConfig.password ? '••••••••' : '',
    };

    return NextResponse.json({
      success: true,
      data: safeConfig,
    });
  } catch (error) {
    console.error('Error getting SMTP configuration:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get SMTP configuration' },
      { status: 500 }
    );
  }
}

// POST - Save SMTP configuration
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check admin role
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      host,
      port,
      secure,
      username,
      password,
      senderName,
      senderEmail,
      isActive,
    } = body;

    // Validation
    if (!host || !port || !username || !senderName || !senderEmail) {
      return NextResponse.json(
        { success: false, error: 'All required fields must be provided' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(senderEmail)) {
      return NextResponse.json(
        { success: false, error: 'Invalid sender email format' },
        { status: 400 }
      );
    }

    // Port validation
    if (port < 1 || port > 65535) {
      return NextResponse.json(
        { success: false, error: 'Invalid port number' },
        { status: 400 }
      );
    }

    // Get existing configuration
    const existingConfig = await prisma.sMTPConfiguration.findFirst({
      where: { isActive: true },
    });

    const configData = {
      host,
      port: parseInt(port),
      secure: Boolean(secure),
      username,
      password: password === '••••••••' && existingConfig ? existingConfig.password : password,
      senderName,
      senderEmail,
      isActive: Boolean(isActive),
    };

    if (existingConfig) {
      // Update existing configuration
      await prisma.sMTPConfiguration.update({
        where: { id: existingConfig.id },
        data: configData,
      });
    } else {
      // Create new configuration
      await prisma.sMTPConfiguration.create({
        data: configData,
      });
    }

    return NextResponse.json({
      success: true,
      message: 'SMTP configuration saved successfully',
    });
  } catch (error) {
    console.error('Error saving SMTP configuration:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to save SMTP configuration' },
      { status: 500 }
    );
  }
}
