import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/database';

// GET - Get SMTP configuration
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check admin role
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get SMTP configuration from AdminSettings
    const smtpSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['smtp_host', 'smtp_port', 'smtp_user', 'smtp_password', 'smtp_secure', 'smtp_from_email', 'smtp_from_name']
        }
      }
    });

    if (smtpSettings.length === 0) {
      return NextResponse.json({
        success: true,
        data: null,
      });
    }

    // Convert settings array to object
    const smtpConfig = smtpSettings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, string>);

    // Don't return the actual password for security
    const safeConfig = {
      ...smtpConfig,
      smtp_password: smtpConfig.smtp_password ? '••••••••' : '',
    };

    return NextResponse.json({
      success: true,
      data: safeConfig,
    });
  } catch (error) {
    console.error('Error getting SMTP configuration:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get SMTP configuration' },
      { status: 500 }
    );
  }
}

// POST - Save SMTP configuration
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check admin role
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      smtp_host,
      smtp_port,
      smtp_secure,
      smtp_user,
      smtp_password,
      smtp_from_name,
      smtp_from_email,
    } = body;

    // Validation
    if (!smtp_host || !smtp_port || !smtp_user || !smtp_from_name || !smtp_from_email) {
      return NextResponse.json(
        { success: false, error: 'All required fields must be provided' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(smtp_from_email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid sender email format' },
        { status: 400 }
      );
    }

    // Port validation
    const port = parseInt(smtp_port);
    if (port < 1 || port > 65535) {
      return NextResponse.json(
        { success: false, error: 'Invalid port number' },
        { status: 400 }
      );
    }

    // Get existing password if not changed
    let finalPassword = smtp_password;
    if (smtp_password === '••••••••') {
      const existingPassword = await prisma.adminSettings.findUnique({
        where: { key: 'smtp_password' }
      });
      finalPassword = existingPassword?.value || '';
    }

    // Prepare settings to upsert
    const settingsToUpdate = [
      { key: 'smtp_host', value: smtp_host },
      { key: 'smtp_port', value: smtp_port.toString() },
      { key: 'smtp_secure', value: smtp_secure ? 'true' : 'false' },
      { key: 'smtp_user', value: smtp_user },
      { key: 'smtp_password', value: finalPassword },
      { key: 'smtp_from_name', value: smtp_from_name },
      { key: 'smtp_from_email', value: smtp_from_email },
    ];

    // Update or create each setting
    for (const setting of settingsToUpdate) {
      await prisma.adminSettings.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: setting,
      });
    }

    return NextResponse.json({
      success: true,
      message: 'SMTP configuration saved successfully',
    });
  } catch (error) {
    console.error('Error saving SMTP configuration:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to save SMTP configuration' },
      { status: 500 }
    );
  }
}
