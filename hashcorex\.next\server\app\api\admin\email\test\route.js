"use strict";(()=>{var e={};e.id=3753,e.ids=[3753],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>g,DY:()=>f,Lx:()=>y,Oj:()=>x,b9:()=>h,qc:()=>w});var s=t(85663),a=t(43205),i=t.n(a),o=t(6710);let n=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",u=async e=>await s.Ay.hash(e,12),c=async(e,r)=>await s.Ay.compare(e,r),d=e=>i().sign(e,n,{expiresIn:l}),p=e=>{try{return i().verify(e,n)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let s=await o.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},f=async e=>{let r,s;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await u(e.password),i=!1;do s=m(),i=!await o.Gy.findByReferralId(s);while(!i);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,n.id,a)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},y=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await c(e.password,r.password))throw Error("Invalid email or password");return{token:d({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},x=e=>{let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||r.push("Password must contain at least one special character"),{valid:0===r.length,errors:r}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),w=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47267:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(12909),u=t(49526);async function c(e){try{let{authenticated:r,user:t}=await (0,l.b9)(e);if(!r||!t)return n.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,l.qc)(t.id))return n.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{host:s,port:a,secure:i,username:o,password:c,senderName:d,senderEmail:p}=await e.json();if(!s||!a||!o||!c||!d||!p)return n.NextResponse.json({success:!1,error:"All fields are required for testing"},{status:400});let m=u.createTransport({host:s,port:parseInt(a),secure:!!i,auth:{user:o,pass:c},tls:{rejectUnauthorized:!1}});await m.verify();let h={from:`"${d}" <${p}>`,to:p,subject:"HashCoreX SMTP Test Email",html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">SMTP Configuration Test</h2>
          <p>This is a test email to verify your SMTP configuration is working correctly.</p>
          <p><strong>Configuration Details:</strong></p>
          <ul>
            <li>Host: ${s}</li>
            <li>Port: ${a}</li>
            <li>Secure: ${i?"Yes":"No"}</li>
            <li>Username: ${o}</li>
            <li>Sender: ${d} &lt;${p}&gt;</li>
          </ul>
          <p>If you received this email, your SMTP configuration is working properly!</p>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 12px;">
            This is an automated test email from HashCoreX Email System.
          </p>
        </div>
      `,text:`
SMTP Configuration Test

This is a test email to verify your SMTP configuration is working correctly.

Configuration Details:
- Host: ${s}
- Port: ${a}
- Secure: ${i?"Yes":"No"}
- Username: ${o}
- Sender: ${d} <${p}>

If you received this email, your SMTP configuration is working properly!

This is an automated test email from HashCoreX Email System.
      `};return await m.sendMail(h),n.NextResponse.json({success:!0,message:"SMTP connection test successful! A test email has been sent."})}catch(r){console.error("SMTP test error:",r);let e="SMTP connection test failed";return r instanceof Error&&(e=r.message.includes("EAUTH")?"Authentication failed. Please check your username and password.":r.message.includes("ECONNREFUSED")?"Connection refused. Please check your host and port settings.":r.message.includes("ETIMEDOUT")?"Connection timeout. Please check your host and port settings.":r.message.includes("ENOTFOUND")?"Host not found. Please check your SMTP host setting.":`SMTP Error: ${r.message}`),n.NextResponse.json({success:!1,error:e},{status:400})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email/test/route",pathname:"/api/admin/email/test",filename:"route",bundlePath:"app/api/admin/email/test/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\test\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:h}=d;function f(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,9526,3306],()=>t(47267));module.exports=s})();