"use strict";exports.id=6679,exports.ids=[6679],exports.modules={36679:(e,s,t)=>{t.d(s,{mc:()=>i,so:()=>c,xA:()=>o,si:()=>j});var l=t(60687),r=t(43210),a=t(4780);let i=({children:e,className:s,size:t="lg"})=>(0,l.jsx)("div",{className:(0,a.cn)("mx-auto px-4 sm:px-6 lg:px-8",{sm:"max-w-3xl",md:"max-w-5xl",lg:"max-w-7xl",xl:"max-w-8xl",full:"max-w-full"}[t],s),children:e}),o=({children:e,className:s,cols:t={default:1,md:2,lg:3},gap:r=6})=>(0,l.jsx)("div",{className:(0,a.cn)((()=>{let e=["grid"];return t.default&&e.push(`grid-cols-${t.default}`),t.sm&&e.push(`sm:grid-cols-${t.sm}`),t.md&&e.push(`md:grid-cols-${t.md}`),t.lg&&e.push(`lg:grid-cols-${t.lg}`),t.xl&&e.push(`xl:grid-cols-${t.xl}`),e.push(`gap-${r}`),e.join(" ")})(),s),children:e}),c=({children:e,className:s,direction:t="row",align:r="start",justify:i="start",wrap:o="nowrap",gap:c=0})=>(0,l.jsx)("div",{className:(0,a.cn)("flex",{row:"flex-row",col:"flex-col","row-reverse":"flex-row-reverse","col-reverse":"flex-col-reverse"}[t],{start:"items-start",center:"items-center",end:"items-end",stretch:"items-stretch",baseline:"items-baseline"}[r],{start:"justify-start",center:"justify-center",end:"justify-end",between:"justify-between",around:"justify-around",evenly:"justify-evenly"}[i],{wrap:"flex-wrap",nowrap:"flex-nowrap","wrap-reverse":"flex-wrap-reverse"}[o],c>0&&`gap-${c}`,s),children:e});var n=t(85814),d=t.n(n),x=t(76276),h=t(71180),m=t(11860),g=t(12941);let j=({children:e})=>{let[s,t]=(0,r.useState)(!1);return(0,l.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,l.jsx)("nav",{className:"fixed top-0 w-full z-50 glass-morphism border-b border-white/20",children:(0,l.jsxs)(i,{children:[(0,l.jsxs)(c,{justify:"between",align:"center",className:"h-16 lg:h-20",children:[(0,l.jsxs)(d(),{href:"/",className:"flex items-center space-x-2 lg:space-x-3",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(h.MX,{className:"h-8 w-8 lg:h-10 lg:w-10 text-yellow-500 animate-pulse"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-yellow-500/20 rounded-full animate-ping"})]}),(0,l.jsx)("span",{className:"text-xl lg:text-3xl font-black text-slate-900",children:"HashCoreX"})]}),(0,l.jsxs)("div",{className:"hidden lg:flex items-center gap-8",children:[(0,l.jsx)(d(),{href:"/about",className:"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105",children:"About"}),(0,l.jsx)(d(),{href:"/how-it-works",className:"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105",children:"How It Works"}),(0,l.jsx)(d(),{href:"/contact",className:"text-gray-700 hover:text-yellow-600 transition-all duration-300 font-medium hover:scale-105",children:"Contact"}),(0,l.jsx)(d(),{href:"/login",children:(0,l.jsx)(x.$n,{variant:"ghost",size:"md",className:"font-semibold",children:"Login"})}),(0,l.jsx)(d(),{href:"/register",children:(0,l.jsx)(x.$n,{variant:"primary",size:"md",className:"font-semibold",children:"Get Started"})})]}),(0,l.jsx)("button",{onClick:()=>t(!s),className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors","aria-label":"Toggle mobile menu",children:s?(0,l.jsx)(m.A,{className:"h-6 w-6 text-gray-700"}):(0,l.jsx)(g.A,{className:"h-6 w-6 text-gray-700"})})]}),s&&(0,l.jsx)("div",{className:"lg:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,l.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[(0,l.jsx)(d(),{href:"/about",className:"block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2",onClick:()=>t(!1),children:"About"}),(0,l.jsx)(d(),{href:"/how-it-works",className:"block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2",onClick:()=>t(!1),children:"How It Works"}),(0,l.jsx)(d(),{href:"/contact",className:"block text-gray-700 hover:text-yellow-600 transition-colors font-medium py-2",onClick:()=>t(!1),children:"Contact"}),(0,l.jsxs)("div",{className:"pt-4 space-y-3",children:[(0,l.jsx)(d(),{href:"/login",onClick:()=>t(!1),children:(0,l.jsx)(x.$n,{variant:"ghost",size:"md",className:"w-full font-semibold",children:"Login"})}),(0,l.jsx)(d(),{href:"/register",onClick:()=>t(!1),children:(0,l.jsx)(x.$n,{variant:"primary",size:"md",className:"w-full font-semibold",children:"Get Started"})})]})]})})]})}),(0,l.jsx)("main",{className:"pt-16 lg:pt-20",children:e}),(0,l.jsx)("footer",{className:"bg-slate-900 text-white py-12",children:(0,l.jsxs)(i,{children:[(0,l.jsxs)(o,{cols:{default:1,md:4},gap:8,children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)(c,{align:"center",gap:2,className:"mb-4",children:[(0,l.jsx)(h.MX,{className:"h-8 w-8 text-yellow-400"}),(0,l.jsx)("span",{className:"text-2xl font-bold",children:"HashCoreX"})]}),(0,l.jsx)("p",{className:"text-gray-300",children:"Sustainable cryptocurrency mining powered by renewable energy."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-4 text-white",children:"Platform"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(d(),{href:"/about",className:"block text-gray-300 hover:text-white transition-colors",children:"About Us"}),(0,l.jsx)(d(),{href:"/how-it-works",className:"block text-gray-300 hover:text-white transition-colors",children:"How It Works"}),(0,l.jsx)(d(),{href:"/contact",className:"block text-gray-300 hover:text-white transition-colors",children:"Contact Us"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-4 text-white",children:"Support"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(d(),{href:"/contact",className:"block text-gray-300 hover:text-white transition-colors",children:"Contact Us"}),(0,l.jsx)(d(),{href:"/contact",className:"block text-gray-300 hover:text-white transition-colors",children:"Help Center"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-4 text-white",children:"Legal"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(d(),{href:"/privacy",className:"block text-gray-300 hover:text-white transition-colors",children:"Privacy Policy"}),(0,l.jsx)(d(),{href:"/terms",className:"block text-gray-300 hover:text-white transition-colors",children:"Terms of Service"})]})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-700 mt-12 pt-8 text-center text-gray-300",children:(0,l.jsx)("p",{children:"\xa9 2024 HashCoreX. All rights reserved."})})]})})]})}}};