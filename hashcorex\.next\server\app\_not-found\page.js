(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={2470:(e,t,r)=>{Promise.resolve().then(r.bind(r,57445))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26207:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34014:(e,t,r)=>{Promise.resolve().then(r.bind(r,49567))},39902:e=>{"use strict";e.exports=require("path")},49567:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var o=r(12907);let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","AuthProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","useAuth")},57445:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>i});var o=r(60687),s=r(43210);let n=(0,s.createContext)(void 0),i=({children:e})=>{let[t,r]=(0,s.useState)(null),[i,a]=(0,s.useState)(!0),[l,d]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=localStorage.getItem("auth-user");if(e)try{let t=JSON.parse(e);r(t)}catch(e){console.error("Error parsing stored user:",e),localStorage.removeItem("auth-user")}u()},[]);let u=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success?(r(t.data),localStorage.setItem("auth-user",JSON.stringify(t.data))):(r(null),localStorage.removeItem("auth-user"))}else r(null),localStorage.removeItem("auth-user")}catch(e){console.error("Auth check failed:",e),r(null),localStorage.removeItem("auth-user")}finally{a(!1),d(!0)}},h=async(e,t)=>{a(!0);try{let o=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),s=await o.json();if(!s.success)throw Error(s.error||"Login failed");r(s.data.user),localStorage.setItem("auth-user",JSON.stringify(s.data.user)),d(!0)}catch(e){throw e}finally{a(!1)}},c=async(e,t,r,o,s,n,i)=>{a(!0);try{let a=i?`/api/auth/register?side=${i}`:"/api/auth/register",l=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:r,password:o,confirmPassword:s,referralCode:n})}),d=await l.json();if(!d.success)throw Error(d.error||"Registration failed");await h(e,o)}catch(e){throw e}finally{a(!1)}},p=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{r(null),localStorage.removeItem("auth-user"),d(!0)}},m=async()=>{await u()};return(0,o.jsx)(n.Provider,{value:{user:t,loading:i,isInitialized:l,login:h,register:c,logout:p,refreshUser:m},children:e})},a=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86455:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},86726:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>u,routeModule:()=>c,tree:()=>d});var o=r(65239),s=r(48088),n=r(88170),i=r.n(n),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],h={require:r,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>a});var o=r(37413),s=r(25091),n=r.n(s);r(61135);var i=r(49567);let a={title:"HashCoreX - Solar-Powered Cloud Mining",description:"Sustainable cryptocurrency mining with solar energy. Earn daily ROI with our eco-friendly mining platform."};function l({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${n().variable} font-sans antialiased`,children:(0,o.jsx)(i.AuthProvider,{children:e})})})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,1771],()=>r(86726));module.exports=o})();