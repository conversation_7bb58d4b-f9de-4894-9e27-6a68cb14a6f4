module.exports = {

"[project]/src/lib/mining.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateDailyROI": (()=>calculateDailyROI),
    "calculateDynamicROI": (()=>calculateDynamicROI),
    "calculateEstimatedEarnings": (()=>calculateEstimatedEarnings),
    "expireOldMiningUnits": (()=>expireOldMiningUnits),
    "getMiningStats": (()=>getMiningStats),
    "getMonthlyReturnLimits": (()=>getMonthlyReturnLimits),
    "processWeeklyEarnings": (()=>processWeeklyEarnings),
    "updateExistingMiningUnitsROI": (()=>updateExistingMiningUnitsROI),
    "validateMonthlyReturn": (()=>validateMonthlyReturn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$miningUnitEarnings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/miningUnitEarnings.ts [app-route] (ecmascript)");
;
;
;
async function calculateDynamicROI(thsAmount) {
    try {
        // Get earnings ranges from admin settings
        const earningsRangesStr = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('earningsRanges');
        let earningsRanges;
        if (earningsRangesStr) {
            try {
                earningsRanges = JSON.parse(earningsRangesStr);
            } catch (parseError) {
                console.error('Error parsing earnings ranges:', parseError);
                earningsRanges = null;
            }
        }
        // Fallback to default ranges if not configured
        if (!earningsRanges || !Array.isArray(earningsRanges)) {
            earningsRanges = [
                {
                    minTHS: 0,
                    maxTHS: 10,
                    dailyReturnMin: 0.3,
                    dailyReturnMax: 0.5,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                },
                {
                    minTHS: 10,
                    maxTHS: 50,
                    dailyReturnMin: 0.4,
                    dailyReturnMax: 0.6,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                },
                {
                    minTHS: 50,
                    maxTHS: 999999,
                    dailyReturnMin: 0.5,
                    dailyReturnMax: 0.7,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                }
            ];
        }
        // Find the appropriate range for the TH/s amount
        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);
        let minROI;
        let maxROI;
        if (applicableRange) {
            minROI = applicableRange.dailyReturnMin;
            maxROI = applicableRange.dailyReturnMax;
        } else {
            // Fallback to highest range if no match found
            const highestRange = earningsRanges[earningsRanges.length - 1];
            minROI = highestRange.dailyReturnMin;
            maxROI = highestRange.dailyReturnMax;
        }
        // Add randomization within the range
        const randomROI = minROI + Math.random() * (maxROI - minROI);
        // Round to 2 decimal places
        return Math.round(randomROI * 100) / 100;
    } catch (error) {
        console.error('Error calculating dynamic ROI:', error);
        // Fallback to default values based on TH/s amount
        if (thsAmount >= 50) return 0.6;
        if (thsAmount >= 10) return 0.5;
        return 0.4;
    }
}
async function validateMonthlyReturn(dailyROI, thsAmount) {
    try {
        // Get earnings ranges from admin settings
        const earningsRangesStr = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('earningsRanges');
        let earningsRanges;
        if (earningsRangesStr) {
            try {
                earningsRanges = JSON.parse(earningsRangesStr);
            } catch (parseError) {
                console.error('Error parsing earnings ranges:', parseError);
                earningsRanges = null;
            }
        }
        // Fallback to default ranges if not configured
        if (!earningsRanges || !Array.isArray(earningsRanges)) {
            earningsRanges = [
                {
                    minTHS: 0,
                    maxTHS: 10,
                    dailyReturnMin: 0.3,
                    dailyReturnMax: 0.5,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                },
                {
                    minTHS: 10,
                    maxTHS: 50,
                    dailyReturnMin: 0.4,
                    dailyReturnMax: 0.6,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                },
                {
                    minTHS: 50,
                    maxTHS: 999999,
                    dailyReturnMin: 0.5,
                    dailyReturnMax: 0.7,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                }
            ];
        }
        // Find the appropriate range for the TH/s amount
        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);
        let monthlyMin = 10.0;
        let monthlyMax = 15.0;
        if (applicableRange) {
            monthlyMin = applicableRange.monthlyReturnMin || 10.0;
            monthlyMax = applicableRange.monthlyReturnMax || 15.0;
        } else {
            // Fallback to highest range if no match found
            const highestRange = earningsRanges[earningsRanges.length - 1];
            monthlyMin = highestRange.monthlyReturnMin || 10.0;
            monthlyMax = highestRange.monthlyReturnMax || 15.0;
        }
        const monthlyReturn = dailyROI * 30; // Approximate monthly return
        return monthlyReturn >= monthlyMin && monthlyReturn <= monthlyMax;
    } catch (error) {
        console.error('Error validating monthly return:', error);
        // Fallback to default 10-15% range
        const monthlyReturn = dailyROI * 30;
        return monthlyReturn >= 10 && monthlyReturn <= 15;
    }
}
async function getMonthlyReturnLimits(thsAmount) {
    try {
        // Get earnings ranges from admin settings
        const earningsRangesStr = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('earningsRanges');
        let earningsRanges;
        if (earningsRangesStr) {
            try {
                earningsRanges = JSON.parse(earningsRangesStr);
            } catch (parseError) {
                console.error('Error parsing earnings ranges:', parseError);
                earningsRanges = null;
            }
        }
        // Fallback to default ranges if not configured
        if (!earningsRanges || !Array.isArray(earningsRanges)) {
            earningsRanges = [
                {
                    minTHS: 0,
                    maxTHS: 10,
                    dailyReturnMin: 0.3,
                    dailyReturnMax: 0.5,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                },
                {
                    minTHS: 10,
                    maxTHS: 50,
                    dailyReturnMin: 0.4,
                    dailyReturnMax: 0.6,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                },
                {
                    minTHS: 50,
                    maxTHS: 999999,
                    dailyReturnMin: 0.5,
                    dailyReturnMax: 0.7,
                    monthlyReturnMin: 10.0,
                    monthlyReturnMax: 15.0
                }
            ];
        }
        // Find the appropriate range for the TH/s amount
        const applicableRange = earningsRanges.find((range)=>thsAmount >= range.minTHS && thsAmount <= range.maxTHS);
        if (applicableRange) {
            return {
                min: applicableRange.monthlyReturnMin || 10.0,
                max: applicableRange.monthlyReturnMax || 15.0
            };
        } else {
            // Fallback to highest range if no match found
            const highestRange = earningsRanges[earningsRanges.length - 1];
            return {
                min: highestRange.monthlyReturnMin || 10.0,
                max: highestRange.monthlyReturnMax || 15.0
            };
        }
    } catch (error) {
        console.error('Error getting monthly return limits:', error);
        // Fallback to default 10-15% range
        return {
            min: 10.0,
            max: 15.0
        };
    }
}
async function updateExistingMiningUnitsROI() {
    try {
        console.log('Updating existing mining units with new ROI configuration...');
        // Get all active mining units
        const activeMiningUnits = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.findMany({
            where: {
                status: 'ACTIVE',
                expiryDate: {
                    gt: new Date()
                }
            }
        });
        console.log(`Found ${activeMiningUnits.length} active mining units to update`);
        const updateResults = [];
        for (const unit of activeMiningUnits){
            try {
                // Calculate new ROI based on current TH/s amount
                const newROI = await calculateDynamicROI(unit.thsAmount);
                // Update the mining unit with new ROI
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.update({
                    where: {
                        id: unit.id
                    },
                    data: {
                        dailyROI: newROI
                    }
                });
                updateResults.push({
                    unitId: unit.id,
                    userId: unit.userId,
                    thsAmount: unit.thsAmount,
                    oldROI: unit.dailyROI,
                    newROI
                });
                console.log(`Updated unit ${unit.id}: ${unit.dailyROI}% -> ${newROI}%`);
            } catch (unitError) {
                console.error(`Error updating unit ${unit.id}:`, unitError);
            }
        }
        // Log the update process
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
            action: 'MINING_UNITS_ROI_UPDATED',
            details: {
                unitsUpdated: updateResults.length,
                totalUnits: activeMiningUnits.length,
                updateResults,
                timestamp: new Date().toISOString()
            }
        });
        console.log(`Successfully updated ${updateResults.length} mining units with new ROI configuration`);
        return {
            success: true,
            unitsUpdated: updateResults.length,
            totalUnits: activeMiningUnits.length,
            updateResults
        };
    } catch (error) {
        console.error('Error updating existing mining units ROI:', error);
        throw error;
    }
}
async function calculateDailyROI() {
    try {
        console.log('Starting daily ROI calculation with FIFO allocation...');
        // Get all users with active mining units
        const usersWithMiningUnits = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findMany({
            where: {
                miningUnits: {
                    some: {
                        status: 'ACTIVE',
                        expiryDate: {
                            gt: new Date()
                        }
                    }
                }
            },
            include: {
                miningUnits: {
                    where: {
                        status: 'ACTIVE',
                        expiryDate: {
                            gt: new Date()
                        }
                    }
                }
            }
        });
        console.log(`Found ${usersWithMiningUnits.length} users with active mining units`);
        const results = [];
        for (const user of usersWithMiningUnits){
            try {
                // Calculate total daily earnings for this user
                let totalDailyEarnings = 0;
                const unitEarnings = [];
                for (const unit of user.miningUnits){
                    const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;
                    totalDailyEarnings += dailyEarnings;
                    unitEarnings.push({
                        unitId: unit.id,
                        thsAmount: unit.thsAmount,
                        dailyEarnings
                    });
                }
                if (totalDailyEarnings > 0) {
                    // Create transaction first
                    const transaction = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transactionDb"].create({
                        userId: user.id,
                        type: 'MINING_EARNINGS',
                        amount: totalDailyEarnings,
                        description: `Daily mining earnings - Total: ${unitEarnings.map((u)=>`${u.thsAmount} TH/s`).join(', ')}`,
                        status: 'PENDING'
                    });
                    // Allocate earnings to mining units using FIFO logic
                    const allocations = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$miningUnitEarnings$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["allocateEarningsToUnits"])(user.id, totalDailyEarnings, 'MINING_EARNINGS', transaction.id, 'Daily mining ROI earnings');
                    results.push({
                        userId: user.id,
                        totalEarnings: totalDailyEarnings,
                        allocations,
                        unitsProcessed: user.miningUnits.length
                    });
                    console.log(`Allocated ${totalDailyEarnings} mining earnings to ${allocations.length} units for user ${user.id}`);
                }
            } catch (userError) {
                console.error(`Error processing mining earnings for user ${user.id}:`, userError);
            }
        }
        // Log the daily ROI calculation
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
            action: 'DAILY_ROI_CALCULATED',
            details: {
                usersProcessed: results.length,
                totalEarnings: results.reduce((sum, r)=>sum + r.totalEarnings, 0),
                totalAllocations: results.reduce((sum, r)=>sum + r.allocations.length, 0),
                timestamp: new Date().toISOString()
            }
        });
        console.log(`Daily ROI calculation completed. Processed ${results.length} users with FIFO allocation.`);
        return results;
    } catch (error) {
        console.error('Daily ROI calculation error:', error);
        throw error;
    }
}
async function processWeeklyEarnings() {
    try {
        console.log('Starting weekly earnings distribution...');
        // Get all pending mining earnings
        const pendingEarnings = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.findMany({
            where: {
                type: 'MINING_EARNINGS',
                status: 'PENDING'
            },
            include: {
                user: true
            }
        });
        console.log(`Found ${pendingEarnings.length} pending earnings transactions`);
        const userEarnings = new Map();
        // Group earnings by user
        for (const transaction of pendingEarnings){
            const currentTotal = userEarnings.get(transaction.userId) || 0;
            userEarnings.set(transaction.userId, currentTotal + transaction.amount);
        }
        const results = [];
        // Process each user's earnings
        for (const [userId, totalEarnings] of userEarnings){
            try {
                // Mark all pending transactions as completed
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.updateMany({
                    where: {
                        userId,
                        type: 'MINING_EARNINGS',
                        status: 'PENDING'
                    },
                    data: {
                        status: 'COMPLETED'
                    }
                });
                results.push({
                    userId,
                    totalEarnings
                });
            } catch (userError) {
                console.error(`Error processing earnings for user ${userId}:`, userError);
            }
        }
        // Log the weekly distribution
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
            action: 'WEEKLY_EARNINGS_DISTRIBUTED',
            details: {
                usersProcessed: results.length,
                totalDistributed: results.reduce((sum, r)=>sum + r.totalEarnings, 0),
                transactionsProcessed: pendingEarnings.length,
                timestamp: new Date().toISOString(),
                payoutTime: 'Sunday 00:00 AM GMT+5:30'
            }
        });
        console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);
        return results;
    } catch (error) {
        console.error('Weekly earnings distribution error:', error);
        throw error;
    }
}
async function expireOldMiningUnits() {
    try {
        console.log('Checking for expired mining units...');
        const expiredUnits = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.findMany({
            where: {
                status: 'ACTIVE',
                expiryDate: {
                    lte: new Date()
                }
            }
        });
        console.log(`Found ${expiredUnits.length} units to expire`);
        for (const unit of expiredUnits){
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["miningUnitDb"].expireUnit(unit.id);
            // Note: User active status is now computed dynamically
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                action: 'MINING_UNIT_EXPIRED',
                userId: unit.userId,
                details: {
                    miningUnitId: unit.id,
                    reason: '24_months_reached',
                    totalEarned: unit.totalEarned,
                    investmentAmount: unit.investmentAmount
                }
            });
        }
        return expiredUnits.length;
    } catch (error) {
        console.error('Mining unit expiry check error:', error);
        throw error;
    }
}
async function getMiningStats() {
    try {
        const stats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction([
            // Total TH/s sold
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.aggregate({
                _sum: {
                    thsAmount: true
                }
            }),
            // Active TH/s
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.aggregate({
                where: {
                    status: 'ACTIVE'
                },
                _sum: {
                    thsAmount: true
                }
            }),
            // Total investment
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.aggregate({
                _sum: {
                    investmentAmount: true
                }
            }),
            // Total earnings distributed
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.aggregate({
                where: {
                    type: 'MINING_EARNINGS',
                    status: 'COMPLETED'
                },
                _sum: {
                    amount: true
                }
            }),
            // Active mining units count
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.count({
                where: {
                    status: 'ACTIVE'
                }
            }),
            // Total mining units count
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.count()
        ]);
        return {
            totalTHSSold: stats[0]._sum.thsAmount || 0,
            activeTHS: stats[1]._sum.thsAmount || 0,
            totalInvestment: stats[2]._sum.investmentAmount || 0,
            totalEarningsDistributed: stats[3]._sum.amount || 0,
            activeMiningUnits: stats[4],
            totalMiningUnits: stats[5]
        };
    } catch (error) {
        console.error('Mining stats error:', error);
        throw error;
    }
}
async function calculateEstimatedEarnings(userId) {
    try {
        const activeMiningUnits = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["miningUnitDb"].findActiveByUserId(userId);
        if (activeMiningUnits.length === 0) {
            return {
                next7Days: 0,
                next30Days: 0,
                next365Days: 0,
                next2Years: 0
            };
        }
        let totalDaily = 0;
        for (const unit of activeMiningUnits){
            const dailyEarnings = unit.investmentAmount * unit.dailyROI / 100;
            const maxEarnings = unit.investmentAmount * 5;
            const remainingEarnings = maxEarnings - unit.totalEarned;
            // Use the lower of daily earnings or remaining earnings
            totalDaily += Math.min(dailyEarnings, remainingEarnings);
        }
        return {
            next7Days: totalDaily * 7,
            next30Days: totalDaily * 30,
            next365Days: totalDaily * 365,
            next2Years: totalDaily * 730
        };
    } catch (error) {
        console.error('Estimated earnings calculation error:', error);
        throw error;
    }
}
}}),

};

//# sourceMappingURL=src_lib_mining_ts_95ca439e._.js.map