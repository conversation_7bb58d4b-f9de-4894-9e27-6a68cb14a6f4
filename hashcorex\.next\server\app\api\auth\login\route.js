"use strict";(()=>{var e={};e.id=7758,e.ids=[7758],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>y,DY:()=>w,Lx:()=>f,Oj:()=>g,b9:()=>h,qc:()=>x});var a=t(85663),s=t(43205),n=t.n(s),i=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",u=async e=>await a.Ay.hash(e,12),d=async(e,r)=>await a.Ay.compare(e,r),c=e=>n().sign(e,o,{expiresIn:l}),p=e=>{try{return n().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await i.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},w=async e=>{let r,a;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await i.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await u(e.password),n=!1;do a=m(),n=!await i.Gy.findByReferralId(a);while(!n);let o=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},f=async e=>{let r=await i.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},g=e=>{let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||r.push("Password must contain at least one special character"),{valid:0===r.length,errors:r}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let r=await i.Gy.findById(e);return r?.role==="ADMIN"}},24005:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};t.r(a),t.d(a,{POST:()=>d});var s=t(96559),n=t(48088),i=t(37719),o=t(32190),l=t(12909),u=t(6710);async function d(e){let r="unknown";try{let t=await e.json();r=t.email||"unknown";let{password:a}=t;if(!r||!a)return o.NextResponse.json({success:!1,error:"Email and password are required"},{status:400});if(!(0,l.DT)(r))return o.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});let s=await (0,l.Lx)({email:r,password:a});await u.AJ.create({action:"USER_LOGIN",userId:s.user.id,details:{email:s.user.email,loginTime:new Date().toISOString()},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"});let n=o.NextResponse.json({success:!0,message:"Login successful",data:{user:s.user,token:s.token}});return n.cookies.set("auth-token",s.token,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800}),n}catch(t){console.error("Login error:",t);try{await u.AJ.create({action:"LOGIN_FAILED",details:{email:r,error:t.message,timestamp:new Date().toISOString()},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"})}catch(e){console.error("Failed to log login attempt:",e)}return o.NextResponse.json({success:!1,error:t.message||"Login failed"},{status:401})}}let c=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:h}=c;function w(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(24005));module.exports=a})();