const CHUNK_PUBLIC_PATH = "server/app/api/admin/email/stats/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_7199f95c._.js");
runtime.loadChunk("server/chunks/node_modules_next_8dee31c0._.js");
runtime.loadChunk("server/chunks/node_modules_nodemailer_a9f338b9._.js");
runtime.loadChunk("server/chunks/node_modules_6f2b02f4._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__aa52dbf8._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/email/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/email/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/email/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
