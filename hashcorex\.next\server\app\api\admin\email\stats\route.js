"use strict";(()=>{var e={};e.id=2802,e.ids=[2802],e.modules={2770:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>u});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(12909),c=r(61904);async function u(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,l.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=await c.g.getEmailStats();return o.NextResponse.json({success:!0,data:a})}catch(e){return console.error("Error getting email statistics:",e),o.NextResponse.json({success:!1,error:"Failed to get email statistics"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email/stats/route",pathname:"/api/admin/email/stats",filename:"route",bundlePath:"app/api/admin/email/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\stats\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:h}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>f,Lx:()=>w,Oj:()=>y,b9:()=>h,qc:()=>E});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",c=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),d=e=>i().sign(e,o,{expiresIn:l}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},f=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await c(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:d({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),E=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},14985:e=>{e.exports=require("dns")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},39902:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},61904:(e,t,r)=>{r.d(t,{b:()=>o,g:()=>n});var a=r(49526),s=r(6710);class i{async initializeTransporter(){try{let e=await s.prisma.sMTPConfiguration.findFirst({where:{isActive:!0}});if(!e)return void console.warn("No active SMTP configuration found");this.currentConfig=e,this.transporter=a.createTransporter({host:e.host,port:e.port,secure:e.secure,auth:{user:e.username,pass:e.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("✅ Email service initialized successfully")}catch(e){console.error("❌ Failed to initialize email service:",e),this.transporter=null,this.currentConfig=null}}async sendEmail(e){try{if((!this.transporter||!this.currentConfig)&&(await this.initializeTransporter(),!this.transporter||!this.currentConfig))throw Error("Email service not configured");let t={from:`"${this.currentConfig.senderName}" <${this.currentConfig.senderEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text};return await this.transporter.sendMail(t),await this.logEmail({userId:e.userId,recipient:e.to,subject:e.subject,templateId:e.templateId,status:"SENT",sentAt:new Date}),console.log(`✅ Email sent successfully to ${e.to}`),!0}catch(t){return console.error(`❌ Failed to send email to ${e.to}:`,t),await this.logEmail({userId:e.userId,recipient:e.to,subject:e.subject,templateId:e.templateId,status:"FAILED",errorMessage:t instanceof Error?t.message:"Unknown error"}),!1}}async sendTemplateEmail(e,t,r={},a){try{let s=await this.getTemplate(e);if(!s)throw Error(`Template '${e}' not found`);let i=this.processTemplate(s.subject,r),n=this.processTemplate(s.htmlContent,r),o=s.textContent?this.processTemplate(s.textContent,r):void 0;return await this.sendEmail({to:t,subject:i,html:n,text:o,templateId:s.id,userId:a})}catch(r){return console.error(`❌ Failed to send template email '${e}' to ${t}:`,r),!1}}processTemplate(e,t){let r=e;return Object.entries(t).forEach(([e,t])=>{let a=RegExp(`{{\\s*${e}\\s*}}`,"g");r=r.replace(a,String(t))}),r}async getTemplate(e){try{return await s.prisma.emailTemplate.findFirst({where:{name:e,isActive:!0}})}catch(t){return console.error(`❌ Failed to get template '${e}':`,t),null}}async logEmail(e){try{await s.prisma.emailLog.create({data:e})}catch(e){console.error("❌ Failed to log email:",e)}}async testConnection(){try{if(await this.initializeTransporter(),!this.transporter)return{success:!1,error:"Failed to initialize transporter"};return await this.transporter.verify(),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async getEmailStats(){try{let[e,t,r]=await Promise.all([s.prisma.emailLog.count({where:{status:"SENT"}}),s.prisma.emailLog.count({where:{status:"FAILED"}}),s.prisma.emailLog.findMany({take:10,orderBy:{createdAt:"desc"},include:{user:{select:{email:!0,firstName:!0,lastName:!0}},template:{select:{name:!0}}}})]);return{totalSent:e,totalFailed:t,recentEmails:r}}catch(e){return console.error("❌ Failed to get email stats:",e),{totalSent:0,totalFailed:0,recentEmails:[]}}}constructor(){this.transporter=null,this.currentConfig=null}}let n=new i,o={USER_REGISTRATION:"user-registration",EMAIL_VERIFICATION:"email-verification",PASSWORD_RESET:"password-reset",KYC_APPROVED:"kyc-approved",KYC_REJECTED:"kyc-rejected",WITHDRAWAL_REQUESTED:"withdrawal-requested",WITHDRAWAL_APPROVED:"withdrawal-approved",WITHDRAWAL_REJECTED:"withdrawal-rejected",MINING_UNIT_PURCHASED:"mining-unit-purchased",WEEKLY_EARNINGS:"weekly-earnings",BINARY_COMMISSION:"binary-commission",REFERRAL_COMMISSION:"referral-commission"}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,9526,3306],()=>r(2770));module.exports=a})();