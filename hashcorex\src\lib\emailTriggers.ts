import { emailService, EMAIL_EVENTS } from './emailService';
import { userDb } from './database';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  referralId: string;
}

interface EmailTriggerData {
  user: User;
  [key: string]: any;
}

class EmailTriggers {
  async sendWelcomeEmail(userData: EmailTriggerData): Promise<boolean> {
    try {
      const { user } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        referralId: user.referralId,
        loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/login`,
        dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.USER_REGISTRATION,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send welcome email:', error);
      return false;
    }
  }

  async sendEmailVerificationOTP(userData: EmailTriggerData & { otp: string }): Promise<boolean> {
    try {
      const { user, otp } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        otp,
        expiryMinutes: 10,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.EMAIL_VERIFICATION,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send email verification OTP:', error);
      return false;
    }
  }

  async sendPasswordResetOTP(userData: EmailTriggerData & { otp: string }): Promise<boolean> {
    try {
      const { user, otp } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        otp,
        expiryMinutes: 15,
        resetUrl: `${process.env.NEXT_PUBLIC_APP_URL}/reset-password`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.PASSWORD_RESET,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send password reset OTP:', error);
      return false;
    }
  }

  async sendKYCApprovalEmail(userData: EmailTriggerData): Promise<boolean> {
    try {
      const { user } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
        miningUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=mining`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.KYC_APPROVED,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send KYC approval email:', error);
      return false;
    }
  }

  async sendKYCRejectionEmail(userData: EmailTriggerData & { reason?: string }): Promise<boolean> {
    try {
      const { user, reason } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        reason: reason || 'Please ensure all documents are clear and valid.',
        kycUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=kyc`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.KYC_REJECTED,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send KYC rejection email:', error);
      return false;
    }
  }

  async sendWithdrawalRequestEmail(userData: EmailTriggerData & { 
    amount: number; 
    address: string; 
    transactionId: string;
  }): Promise<boolean> {
    try {
      const { user, amount, address, transactionId } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        amount: amount.toFixed(2),
        address,
        transactionId,
        walletUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=wallet`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.WITHDRAWAL_REQUESTED,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send withdrawal request email:', error);
      return false;
    }
  }

  async sendWithdrawalApprovalEmail(userData: EmailTriggerData & { 
    amount: number; 
    address: string; 
    transactionId: string;
    txHash?: string;
  }): Promise<boolean> {
    try {
      const { user, amount, address, transactionId, txHash } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        amount: amount.toFixed(2),
        address,
        transactionId,
        txHash: txHash || 'Processing...',
        explorerUrl: txHash ? `https://tronscan.org/#/transaction/${txHash}` : '',
        walletUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=wallet`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.WITHDRAWAL_APPROVED,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send withdrawal approval email:', error);
      return false;
    }
  }

  async sendWithdrawalRejectionEmail(userData: EmailTriggerData & { 
    amount: number; 
    reason?: string;
    transactionId: string;
  }): Promise<boolean> {
    try {
      const { user, amount, reason, transactionId } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        amount: amount.toFixed(2),
        reason: reason || 'Please contact support for more information.',
        transactionId,
        walletUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=wallet`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.WITHDRAWAL_REJECTED,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send withdrawal rejection email:', error);
      return false;
    }
  }

  async sendMiningUnitPurchaseEmail(userData: EmailTriggerData & { 
    packageName: string;
    amount: number;
    units: number;
    dailyROI: number;
  }): Promise<boolean> {
    try {
      const { user, packageName, amount, units, dailyROI } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        packageName,
        amount: amount.toFixed(2),
        units,
        dailyROI: dailyROI.toFixed(2),
        miningUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=mining`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.MINING_UNIT_PURCHASED,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send mining unit purchase email:', error);
      return false;
    }
  }

  async sendWeeklyEarningsEmail(userData: EmailTriggerData & { 
    totalEarnings: number;
    miningEarnings: number;
    referralCommissions: number;
    binaryCommissions: number;
  }): Promise<boolean> {
    try {
      const { user, totalEarnings, miningEarnings, referralCommissions, binaryCommissions } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        totalEarnings: totalEarnings.toFixed(2),
        miningEarnings: miningEarnings.toFixed(2),
        referralCommissions: referralCommissions.toFixed(2),
        binaryCommissions: binaryCommissions.toFixed(2),
        walletUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=wallet`,
        earningsUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=earnings`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.WEEKLY_EARNINGS,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send weekly earnings email:', error);
      return false;
    }
  }

  async sendReferralCommissionEmail(userData: EmailTriggerData & { 
    referredUserName: string;
    commissionAmount: number;
    packageName: string;
  }): Promise<boolean> {
    try {
      const { user, referredUserName, commissionAmount, packageName } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        referredUserName,
        commissionAmount: commissionAmount.toFixed(2),
        packageName,
        walletUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=wallet`,
        referralUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=referrals`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.REFERRAL_COMMISSION,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send referral commission email:', error);
      return false;
    }
  }

  async sendBinaryCommissionEmail(userData: EmailTriggerData & { 
    commissionAmount: number;
    matchedPoints: number;
    leftPoints: number;
    rightPoints: number;
  }): Promise<boolean> {
    try {
      const { user, commissionAmount, matchedPoints, leftPoints, rightPoints } = userData;
      
      const variables = {
        firstName: user.firstName,
        lastName: user.lastName,
        commissionAmount: commissionAmount.toFixed(2),
        matchedPoints: matchedPoints.toFixed(2),
        leftPoints: leftPoints.toFixed(2),
        rightPoints: rightPoints.toFixed(2),
        walletUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=wallet`,
        binaryUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?tab=binary`,
        supportEmail: '<EMAIL>',
        currentYear: new Date().getFullYear(),
      };

      return await emailService.sendTemplateEmail(
        EMAIL_EVENTS.BINARY_COMMISSION,
        user.email,
        variables,
        user.id
      );
    } catch (error) {
      console.error('❌ Failed to send binary commission email:', error);
      return false;
    }
  }
}

// Export singleton instance
export const emailTriggers = new EmailTriggers();
