"use strict";(()=>{var e={};e.id=7270,e.ids=[7270],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,a)=>{a.d(t,{DT:()=>y,DY:()=>g,Lx:()=>h,Oj:()=>w,b9:()=>f,qc:()=>E});var r=a(85663),n=a(43205),i=a.n(n),s=a(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await r.Ay.hash(e,12),d=async(e,t)=>await r.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let a=0;a<8;a++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let a=m(t);if(!a)return{authenticated:!1,user:null};let r=await s.Gy.findByEmail(a.email);return r?{authenticated:!0,user:r}:{authenticated:!1,user:null}},g=async e=>{let t,r;if(await s.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let a=await s.Gy.findByReferralId(e.referralCode);if(!a)throw Error("Invalid referral code");t=a.id}let n=await l(e.password),i=!1;do r=p(),i=!await s.Gy.findByReferralId(r);while(!i);let o=await s.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:n,referralId:r});if(t){let{placeUserByReferralType:r}=await a.e(2746).then(a.bind(a,2746)),n="general";"left"===e.placementSide?n="left":"right"===e.placementSide&&(n="right"),await r(t,o.id,n)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await s.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},y=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),E=async e=>{let t=await s.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39794:(e,t,a)=>{a.d(t,{Py:()=>s,k8:()=>l,kp:()=>u});var r=a(31183),n=a(6710);async function i(e){return await r.prisma.miningUnit.findMany({where:{userId:e,status:"ACTIVE",expiryDate:{gt:new Date}},orderBy:{createdAt:"asc"}})}async function s(e,t,a,s,u){let l=await i(e);if(0===l.length)throw Error("No active mining units found for earnings allocation");let d=[],c=t;for(let e of l){var m;if(c<=0)break;let t=Math.max(0,5*(m=e).investmentAmount-(m.miningEarnings+m.referralEarnings+m.binaryEarnings));if(t<=0)continue;let n=Math.min(c,t);if(n>0){let i={};switch(a){case"MINING_EARNINGS":i.miningEarnings={increment:n};break;case"DIRECT_REFERRAL":i.referralEarnings={increment:n};break;case"BINARY_BONUS":i.binaryEarnings={increment:n}}i.totalEarned={increment:n},await r.prisma.miningUnit.update({where:{id:e.id},data:i}),await r.prisma.miningUnitEarningsAllocation.create({data:{miningUnitId:e.id,transactionId:s,earningType:a,amount:n,description:u}}),d.push({miningUnitId:e.id,amount:n,remainingCapacity:t-n}),c-=n;let l=await r.prisma.miningUnit.findUnique({where:{id:e.id}});l&&function(e){let t=5*e.investmentAmount;return e.miningEarnings+e.referralEarnings+e.binaryEarnings>=t}(l)&&await o(e.id,"5x_investment_reached")}}return c>0&&(console.warn(`Unable to allocate ${c} to mining units - all units at capacity`),await n.AJ.create({action:"EARNINGS_ALLOCATION_OVERFLOW",userId:e,details:{totalAmount:t,allocatedAmount:t-c,overflowAmount:c,earningType:a,reason:"all_units_at_capacity"}})),d}async function o(e,t){let a=await r.prisma.miningUnit.findUnique({where:{id:e}});if(!a)throw Error(`Mining unit ${e} not found`);await r.prisma.miningUnit.update({where:{id:e},data:{status:"EXPIRED"}});let i=a.miningEarnings+a.referralEarnings+a.binaryEarnings;await n.AJ.create({action:"MINING_UNIT_EXPIRED",userId:a.userId,details:{miningUnitId:e,reason:t,totalEarned:i,miningEarnings:a.miningEarnings,referralEarnings:a.referralEarnings,binaryEarnings:a.binaryEarnings,investmentAmount:a.investmentAmount,multiplier:i/a.investmentAmount}}),console.log(`Mining unit ${e} expired due to ${t}. Total earnings: ${i}`)}async function u(e){return await r.prisma.miningUnit.findMany({where:{userId:e},orderBy:{createdAt:"asc"}})}async function l(e){return await r.prisma.miningUnitEarningsAllocation.findMany({where:{miningUnitId:e},include:{transaction:!0},orderBy:{allocatedAt:"desc"}})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53510:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var r={};a.r(r),a.d(r,{POST:()=>d});var n=a(96559),i=a(48088),s=a(37719),o=a(32190),u=a(12909),l=a(92731);async function d(e){try{let{authenticated:t,user:a}=await (0,u.b9)(e);if(!t||!a)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("ADMIN"!==a.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let r=await (0,l.dW)();return o.NextResponse.json({success:!0,message:"User active status updated successfully",data:{totalUsers:r.length,activeUsers:r.filter(e=>e.isActive).length,inactiveUsers:r.filter(e=>!e.isActive).length}})}catch(e){return console.error("User status update error:",e),o.NextResponse.json({success:!1,error:"Failed to update user status"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/update-user-status/route",pathname:"/api/admin/update-user-status",filename:"route",bundlePath:"app/api/admin/update-user-status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\update-user-status\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:f}=c;function g(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580,5315,3306,5112],()=>a(53510));module.exports=r})();