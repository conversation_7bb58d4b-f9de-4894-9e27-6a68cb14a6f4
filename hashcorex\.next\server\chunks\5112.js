"use strict";exports.id=5112,exports.ids=[5112],exports.modules={92731:(t,e,n)=>{n.d(e,{IT:()=>d,Oh:()=>u,WL:()=>l,calculateDynamicROI:()=>s,dW:()=>g,eB:()=>c,s2:()=>o});var i=n(31183),a=n(6710),r=n(39794);async function s(t){try{let e,n,i,r=await a.rs.get("earningsRanges");if(r)try{e=JSON.parse(r)}catch(t){console.error("Error parsing earnings ranges:",t),e=null}e&&Array.isArray(e)||(e=[{minTHS:0,maxTHS:10,dailyReturnMin:.3,dailyReturnMax:.5,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:10,maxTHS:50,dailyReturnMin:.4,dailyReturnMax:.6,monthlyReturnMin:10,monthlyReturnMax:15},{minTHS:50,maxTHS:999999,dailyReturnMin:.5,dailyReturnMax:.7,monthlyReturnMin:10,monthlyReturnMax:15}]);let s=e.find(e=>t>=e.minTHS&&t<=e.maxTHS);if(s)n=s.dailyReturnMin,i=s.dailyReturnMax;else{let t=e[e.length-1];n=t.dailyReturnMin,i=t.dailyReturnMax}let o=n+Math.random()*(i-n);return Math.round(100*o)/100}catch(e){if(console.error("Error calculating dynamic ROI:",e),t>=50)return .6;if(t>=10)return .5;return .4}}async function o(){try{console.log("Updating existing mining units with new ROI configuration...");let t=await i.prisma.miningUnit.findMany({where:{status:"ACTIVE",expiryDate:{gt:new Date}}});console.log(`Found ${t.length} active mining units to update`);let e=[];for(let n of t)try{let t=await s(n.thsAmount);await i.prisma.miningUnit.update({where:{id:n.id},data:{dailyROI:t}}),e.push({unitId:n.id,userId:n.userId,thsAmount:n.thsAmount,oldROI:n.dailyROI,newROI:t}),console.log(`Updated unit ${n.id}: ${n.dailyROI}% -> ${t}%`)}catch(t){console.error(`Error updating unit ${n.id}:`,t)}return await a.AJ.create({action:"MINING_UNITS_ROI_UPDATED",details:{unitsUpdated:e.length,totalUnits:t.length,updateResults:e,timestamp:new Date().toISOString()}}),console.log(`Successfully updated ${e.length} mining units with new ROI configuration`),{success:!0,unitsUpdated:e.length,totalUnits:t.length,updateResults:e}}catch(t){throw console.error("Error updating existing mining units ROI:",t),t}}async function l(){try{console.log("Starting daily ROI calculation with FIFO allocation...");let t=await i.prisma.user.findMany({where:{miningUnits:{some:{status:"ACTIVE",expiryDate:{gt:new Date}}}},include:{miningUnits:{where:{status:"ACTIVE",expiryDate:{gt:new Date}}}}});console.log(`Found ${t.length} users with active mining units`);let e=[];for(let n of t)try{let t=0,i=[];for(let e of n.miningUnits){let n=e.investmentAmount*e.dailyROI/100;t+=n,i.push({unitId:e.id,thsAmount:e.thsAmount,dailyEarnings:n})}if(t>0){let s=await a.DR.create({userId:n.id,type:"MINING_EARNINGS",amount:t,description:`Daily mining earnings - Total: ${i.map(t=>`${t.thsAmount} TH/s`).join(", ")}`,status:"PENDING"}),o=await (0,r.Py)(n.id,t,"MINING_EARNINGS",s.id,"Daily mining ROI earnings");e.push({userId:n.id,totalEarnings:t,allocations:o,unitsProcessed:n.miningUnits.length}),console.log(`Allocated ${t} mining earnings to ${o.length} units for user ${n.id}`)}}catch(t){console.error(`Error processing mining earnings for user ${n.id}:`,t)}return await a.AJ.create({action:"DAILY_ROI_CALCULATED",details:{usersProcessed:e.length,totalEarnings:e.reduce((t,e)=>t+e.totalEarnings,0),totalAllocations:e.reduce((t,e)=>t+e.allocations.length,0),timestamp:new Date().toISOString()}}),console.log(`Daily ROI calculation completed. Processed ${e.length} users with FIFO allocation.`),e}catch(t){throw console.error("Daily ROI calculation error:",t),t}}async function u(){try{console.log("Starting weekly earnings distribution...");let t=await i.prisma.transaction.findMany({where:{type:"MINING_EARNINGS",status:"PENDING"},include:{user:!0}});console.log(`Found ${t.length} pending earnings transactions`);let e=new Map;for(let n of t){let t=e.get(n.userId)||0;e.set(n.userId,t+n.amount)}let n=[];for(let[t,a]of e)try{await i.prisma.transaction.updateMany({where:{userId:t,type:"MINING_EARNINGS",status:"PENDING"},data:{status:"COMPLETED"}}),n.push({userId:t,totalEarnings:a})}catch(e){console.error(`Error processing earnings for user ${t}:`,e)}return await a.AJ.create({action:"WEEKLY_EARNINGS_DISTRIBUTED",details:{usersProcessed:n.length,totalDistributed:n.reduce((t,e)=>t+e.totalEarnings,0),transactionsProcessed:t.length,timestamp:new Date().toISOString(),payoutTime:"Sunday 00:00 AM GMT+5:30"}}),console.log(`Weekly earnings distribution completed. Processed ${n.length} users.`),n}catch(t){throw console.error("Weekly earnings distribution error:",t),t}}async function c(){try{console.log("Checking for expired mining units...");let t=await i.prisma.miningUnit.findMany({where:{status:"ACTIVE",expiryDate:{lte:new Date}}});for(let e of(console.log(`Found ${t.length} units to expire`),t))await a.tg.expireUnit(e.id),await a.AJ.create({action:"MINING_UNIT_EXPIRED",userId:e.userId,details:{miningUnitId:e.id,reason:"24_months_reached",totalEarned:e.totalEarned,investmentAmount:e.investmentAmount}});return t.length}catch(t){throw console.error("Mining unit expiry check error:",t),t}}async function d(t){try{let e=await a.tg.findActiveByUserId(t);if(0===e.length)return{next7Days:0,next30Days:0,next365Days:0,next2Years:0};let n=0;for(let t of e){let e=t.investmentAmount*t.dailyROI/100,i=5*t.investmentAmount-t.totalEarned;n+=Math.min(e,i)}return{next7Days:7*n,next30Days:30*n,next365Days:365*n,next2Years:730*n}}catch(t){throw console.error("Estimated earnings calculation error:",t),t}}async function g(){try{console.log("Updating all users active status based on mining units...");let t=await i.prisma.user.findMany({select:{id:!0,email:!0,firstName:!0,lastName:!0,isActive:!0,miningUnits:{where:{status:"ACTIVE",expiryDate:{gt:new Date}}}}}),e=[];for(let n of t){let t=n.miningUnits.length>0;n.isActive!==t&&(await i.prisma.user.update({where:{id:n.id},data:{isActive:t}}),await a.AJ.create({action:t?"USER_ACTIVATED_BY_MINING":"USER_DEACTIVATED_NO_MINING",userId:n.id,details:{previousStatus:n.isActive,newStatus:t,activeMiningUnits:n.miningUnits.length}})),e.push({userId:n.id,email:n.email,name:`${n.firstName} ${n.lastName}`,isActive:t,activeMiningUnits:n.miningUnits.length,statusChanged:n.isActive!==t})}return console.log(`Updated ${e.filter(t=>t.statusChanged).length} users' active status`),e}catch(t){throw console.error("Error updating users active status:",t),t}}}};