"use strict";(()=>{var e={};e.id=7368,e.ids=[7368],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10994:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{POST:()=>c});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),d=r(31183),l=r(6710);async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if("APPROVED"===r.kycStatus)return o.NextResponse.json({success:!1,error:"KYC already approved"},{status:400});if("PENDING"===r.kycStatus)return o.NextResponse.json({success:!1,error:"KYC submission already pending review"},{status:400});let s=await d.prisma.kYCDocument.findMany({where:{userId:r.id}});if(0===s.length)return o.NextResponse.json({success:!1,error:"No documents uploaded"},{status:400});let a=s.some(e=>"SELFIE"===e.documentType);if(!a)return o.NextResponse.json({success:!1,error:"Selfie document is required"},{status:400});let i=s.filter(e=>"ID_DOCUMENT"===e.documentType);if(0===i.length)return o.NextResponse.json({success:!1,error:"ID document is required"},{status:400});let n=i[0].idType,c=!1;if("PASSPORT"===n)c=i.some(e=>"FRONT"===e.documentSide);else{let e=i.some(e=>"FRONT"===e.documentSide),t=i.some(e=>"BACK"===e.documentSide);c=e&&t}if(!c)return o.NextResponse.json({success:!1,error:"PASSPORT"===n?"Passport front side is required":"Both front and back sides of ID are required"},{status:400});return await d.prisma.user.update({where:{id:r.id},data:{kycStatus:"PENDING"}}),await d.prisma.kYCDocument.updateMany({where:{userId:r.id},data:{status:"PENDING"}}),await l.AJ.create({action:"KYC_SUBMITTED",userId:r.id,details:{documentsCount:s.length,idType:n,hasCompleteIdDocuments:c,hasSelfie:a},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"KYC submitted successfully for review",data:{status:"PENDING",documentsCount:s.length,submittedAt:new Date().toISOString()}})}catch(e){return console.error("KYC submission error:",e),o.NextResponse.json({success:!1,error:"Failed to submit KYC"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/kyc/submit/route",pathname:"/api/kyc/submit",filename:"route",bundlePath:"app/api/kyc/submit/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\submit\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:h}=p;function y(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},12909:(e,t,r)=>{r.d(t,{DT:()=>x,DY:()=>h,Lx:()=>y,Oj:()=>w,b9:()=>f,qc:()=>g});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await s.Ay.hash(e,12),l=async(e,t)=>await s.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},h=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await d(e.password),i=!1;do s=m(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306],()=>r(10994));module.exports=s})();