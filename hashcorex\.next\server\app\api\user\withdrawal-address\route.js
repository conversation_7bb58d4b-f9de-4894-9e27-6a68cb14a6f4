"use strict";(()=>{var e={};e.id=2880,e.ids=[2880],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>x,DY:()=>m,Lx:()=>f,Oj:()=>y,b9:()=>h,qc:()=>g});var a=t(85663),s=t(43205),i=t.n(s),o=t(6710);let n=process.env.JWT_SECRET||"fallback-secret-key",d=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await a.Ay.hash(e,12),u=async(e,r)=>await a.Ay.compare(e,r),c=e=>i().sign(e,n,{expiresIn:d}),p=e=>{try{return i().verify(e,n)}catch(e){return null}},w=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},h=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let a=await o.Gy.findByEmail(t.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},m=async e=>{let r,a;if(await o.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await o.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let s=await l(e.password),i=!1;do a=w(),i=!await o.Gy.findByReferralId(a);while(!i);let n=await o.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(r){let{placeUserByReferralType:a}=await t.e(2746).then(t.bind(t,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(r,n.id,s)}return{id:n.id,email:n.email,referralId:n.referralId,kycStatus:n.kycStatus}},f=async e=>{let r=await o.Gy.findByEmail(e.email);if(!r||!await u(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>{let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||r.push("Password must contain at least one special character"),{valid:0===r.length,errors:r}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await o.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},83732:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>w});var a={};t.r(a),t.d(a,{GET:()=>l,POST:()=>u});var s=t(96559),i=t(48088),o=t(37719),n=t(32190),d=t(12909);async function l(e){try{let{authenticated:r,user:a}=await (0,d.b9)(e);if(!r||!a)return n.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{prisma:s}=await Promise.resolve().then(t.bind(t,31183)),i=await s.user.findUnique({where:{email:a.email},select:{id:!0,email:!0,withdrawalAddress:!0}});if(!i)return n.NextResponse.json({success:!1,error:"User not found"},{status:404});let o=i.withdrawalAddress||"";return n.NextResponse.json({success:!0,data:{withdrawalAddress:o}})}catch(e){return console.error("Get withdrawal address error:",e),n.NextResponse.json({success:!1,error:"Failed to get withdrawal address",details:e.message},{status:500})}}async function u(e){try{let{authenticated:r,user:a}=await (0,d.b9)(e);if(!r||!a)return n.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{withdrawalAddress:s}=await e.json();if(s&&!s.match(/^T[A-Za-z1-9]{33}$/))return n.NextResponse.json({success:!1,error:"Invalid USDT TRC20 address format"},{status:400});try{console.log("Attempting to update withdrawal address for user:",a.email),console.log("New withdrawal address:",s);let{prisma:e}=await Promise.resolve().then(t.bind(t,31183)),r=await e.user.update({where:{email:a.email},data:{withdrawalAddress:s||null}});console.log("Successfully updated user:",r.id)}catch(e){throw console.error("Database update error:",e),console.error("Error details:",e.message),console.error("Error code:",e.code),Error(`Database error: ${e.message}`)}return n.NextResponse.json({success:!0,message:"Withdrawal address updated successfully"})}catch(e){return console.error("Update withdrawal address error:",e),n.NextResponse.json({success:!1,error:"Failed to update withdrawal address",details:e.message},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/user/withdrawal-address/route",pathname:"/api/user/withdrawal-address",filename:"route",bundlePath:"app/api/user/withdrawal-address/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\user\\withdrawal-address\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:w,serverHooks:h}=c;function m(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:w})}},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,5315,3306],()=>t(83732));module.exports=a})();