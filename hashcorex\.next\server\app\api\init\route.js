"use strict";(()=>{var e={};e.id=1288,e.ids=[1288],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{e.exports=require("dns")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},39902:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51134:(e,t,r)=>{r.d(t,{c:()=>n});var i=r(92731),a=r(2746),s=r(6710);class o{constructor(){this.tasks=new Map,this.intervals=new Map,this.isInitialized=!1,this.setupTasks()}setupTasks(){this.addTask({id:"daily-roi",name:"Daily ROI Calculation",cronExpression:"0 1 0 * * *",handler:this.handleDailyROI.bind(this),isRunning:!1}),this.addTask({id:"weekly-payout",name:"Weekly Earnings Payout",cronExpression:"0 30 18 * * 0",handler:this.handleWeeklyPayout.bind(this),isRunning:!1}),this.addTask({id:"binary-matching",name:"Binary Matching",cronExpression:"0 30 9 * * 6",handler:this.handleBinaryMatching.bind(this),isRunning:!1})}addTask(e){this.tasks.set(e.id,e)}async handleDailyROI(){try{console.log("\uD83D\uDD04 Starting scheduled daily ROI calculation...");let e=await (0,i.eB)();console.log(`⏰ Expired ${e} old mining units`);let t=await (0,i.WL)();console.log(`💰 Processed ${t.length} users for daily ROI`);let r=t.reduce((e,t)=>e+t.totalEarnings,0);await s.AJ.create({action:"SCHEDULED_DAILY_ROI_EXECUTED",details:{usersProcessed:t.length,totalEarnings:r,expiredUnits:e,executionTime:new Date().toISOString(),scheduler:"server-side"}}),console.log(`✅ Daily ROI calculation completed. Total earnings: $${r.toFixed(2)}`)}catch(e){console.error("❌ Error in daily ROI calculation:",e),await s.AJ.create({action:"SCHEDULED_DAILY_ROI_ERROR",details:{error:e instanceof Error?e.message:"Unknown error",executionTime:new Date().toISOString(),scheduler:"server-side"}})}}async handleWeeklyPayout(){try{console.log("\uD83D\uDD04 Starting scheduled weekly earnings payout...");let e=await (0,i.Oh)(),t=e.reduce((e,t)=>e+t.totalEarnings,0);await s.AJ.create({action:"SCHEDULED_WEEKLY_PAYOUT_EXECUTED",details:{usersProcessed:e.length,totalDistributed:t,executionTime:new Date().toISOString(),payoutTime:"Sunday 00:00 AM GMT+5:30",scheduler:"server-side"}}),console.log(`✅ Weekly payout completed. Distributed: $${t.toFixed(2)} to ${e.length} users`)}catch(e){console.error("❌ Error in weekly payout:",e),await s.AJ.create({action:"SCHEDULED_WEEKLY_PAYOUT_ERROR",details:{error:e instanceof Error?e.message:"Unknown error",executionTime:new Date().toISOString(),scheduler:"server-side"}})}}async handleBinaryMatching(){try{console.log("\uD83D\uDD04 Starting scheduled binary matching...");let e=await (0,a.E5)(),t=e.reduce((e,t)=>e+t.payout,0),r=e.reduce((e,t)=>e+t.matchedPoints,0);await s.AJ.create({action:"SCHEDULED_BINARY_MATCHING_EXECUTED",details:{usersProcessed:e.length,totalPayouts:t,totalMatchedPoints:r,executionTime:new Date().toISOString(),matchingTime:"Saturday 15:00 GMT+5:30",scheduler:"server-side"}}),console.log(`✅ Binary matching completed. Payouts: $${t.toFixed(2)}, Matched points: ${r}`)}catch(e){console.error("❌ Error in binary matching:",e),await s.AJ.create({action:"SCHEDULED_BINARY_MATCHING_ERROR",details:{error:e instanceof Error?e.message:"Unknown error",executionTime:new Date().toISOString(),scheduler:"server-side"}})}}parseCronExpression(e){let t=e.split(" ");if(6!==t.length)throw Error("Invalid cron expression format");let[r,i,a,,,s]=t.map(e=>"*"===e?-1:parseInt(e)),o=new Date,n=new Date;if(-1!==a&&n.setUTCHours(a),-1!==i&&n.setUTCMinutes(i),-1!==r&&n.setUTCSeconds(r),n.setUTCMilliseconds(0),-1!==s){let e=(s-n.getUTCDay()+7)%7;0===e&&n<=o?n.setUTCDate(n.getUTCDate()+7):e>0&&n.setUTCDate(n.getUTCDate()+e)}else n<=o&&n.setUTCDate(n.getUTCDate()+1);return n.getTime()-o.getTime()}scheduleTask(e){try{let t=this.parseCronExpression(e.cronExpression);e.nextRun=new Date(Date.now()+t),console.log(`📅 Scheduled ${e.name} to run at ${e.nextRun.toISOString()}`);let r=setTimeout(async()=>{if(!e.isRunning){e.isRunning=!0,e.lastRun=new Date;try{await e.handler()}finally{e.isRunning=!1,this.scheduleTask(e)}}},t),i=this.intervals.get(e.id);i&&clearTimeout(i),this.intervals.set(e.id,r)}catch(t){console.error(`❌ Error scheduling task ${e.name}:`,t)}}start(){if(this.isInitialized)return void console.log("⚠️ Scheduler already initialized");for(let e of(console.log("\uD83D\uDE80 Starting server-side scheduler..."),this.tasks.values()))this.scheduleTask(e);this.isInitialized=!0,console.log(`✅ Scheduler initialized with ${this.tasks.size} tasks`)}stop(){for(let e of(console.log("\uD83D\uDED1 Stopping server-side scheduler..."),this.intervals.values()))clearTimeout(e);this.intervals.clear(),this.isInitialized=!1,console.log("✅ Scheduler stopped")}getTaskStatus(){let e=[];for(let t of this.tasks.values())e.push({id:t.id,name:t.name,cronExpression:t.cronExpression,lastRun:t.lastRun,nextRun:t.nextRun,isRunning:t.isRunning});return e}async runTaskManually(e){let t=this.tasks.get(e);if(!t)throw Error(`Task ${e} not found`);if(t.isRunning)throw Error(`Task ${e} is already running`);console.log(`🔧 Manually running task: ${t.name}`),t.isRunning=!0;try{await t.handler(),t.lastRun=new Date}finally{t.isRunning=!1}}}let n=new o},53189:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>f,serverHooks:()=>v,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>x});var i={};r.r(i),r.d(i,{GET:()=>g});var a=r(96559),s=r(48088),o=r(37719),n=r(32190),l=r(1827),d=r(51134),c=r(61904);let p=[{name:"user-registration",subject:"Welcome to HashCoreX - Your Account is Ready!",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to HashCoreX</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to HashCoreX!</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Solar-Powered Cloud Mining Platform</p>
    </div>
    
    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}} {{lastName}}!</h2>
        
        <p>Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable cryptocurrency mining community.</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #495057;">Your Account Details:</h3>
            <p><strong>Email:</strong> {{email}}</p>
            <p><strong>Referral ID:</strong> {{referralId}}</p>
        </div>
        
        <h3>Next Steps:</h3>
        <ol>
            <li>Complete your KYC verification for full access</li>
            <li>Explore our mining packages</li>
            <li>Start earning with solar-powered mining</li>
            <li>Invite friends and earn referral commissions</li>
        </ol>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Access Your Dashboard</a>
        </div>
        
        <p>If you have any questions, feel free to contact our support team at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>.</p>
        
        <p>Welcome aboard!</p>
        <p><strong>The HashCoreX Team</strong></p>
    </div>
    
    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            \xa9 {{currentYear}} HashCoreX. All rights reserved.<br>
            This email was sent to {{email}}
        </p>
    </div>
</body>
</html>`,textContent:`Welcome to HashCoreX!

Hello {{firstName}} {{lastName}}!

Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable cryptocurrency mining community.

Your Account Details:
- Email: {{email}}
- Referral ID: {{referralId}}

Next Steps:
1. Complete your KYC verification for full access
2. Explore our mining packages
3. Start earning with solar-powered mining
4. Invite friends and earn referral commissions

Access your dashboard: {{dashboardUrl}}

If you have any questions, feel free to contact our support team at {{supportEmail}}.

Welcome aboard!
The HashCoreX Team

\xa9 {{currentYear}} HashCoreX. All rights reserved.`,variables:{firstName:"User first name",lastName:"User last name",email:"User email address",referralId:"User referral ID",loginUrl:"Login page URL",dashboardUrl:"Dashboard URL",supportEmail:"Support email address",currentYear:"Current year"}},{name:"email-verification",subject:"HashCoreX - Email Verification Code",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Email Verification</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">HashCoreX Security</p>
    </div>
    
    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>
        
        <p>Please use the following verification code to verify your email address:</p>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <h2 style="color: #495057; font-size: 36px; letter-spacing: 8px; margin: 0; font-family: monospace;">{{otp}}</h2>
        </div>
        
        <p><strong>Important:</strong> This verification code will expire in {{expiryMinutes}} minutes for security reasons.</p>
        
        <p>If you didn't request this verification code, please ignore this email or contact our support team.</p>
        
        <p>For security reasons, never share this code with anyone.</p>
        
        <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
    </div>
    
    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            \xa9 {{currentYear}} HashCoreX. All rights reserved.<br>
            Need help? Contact us at {{supportEmail}}
        </p>
    </div>
</body>
</html>`,textContent:`Email Verification - HashCoreX

Hello {{firstName}}!

Please use the following verification code to verify your email address:

{{otp}}

Important: This verification code will expire in {{expiryMinutes}} minutes for security reasons.

If you didn't request this verification code, please ignore this email or contact our support team.

For security reasons, never share this code with anyone.

Best regards,
The HashCoreX Team

\xa9 {{currentYear}} HashCoreX. All rights reserved.
Need help? Contact us at {{supportEmail}}`,variables:{firstName:"User first name",lastName:"User last name",otp:"Verification code",expiryMinutes:"Code expiry time in minutes",supportEmail:"Support email address",currentYear:"Current year"}},{name:"password-reset",subject:"HashCoreX - Password Reset Code",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Password Reset</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">HashCoreX Security</p>
    </div>
    
    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>
        
        <p>We received a request to reset your password. Please use the following code to reset your password:</p>
        
        <div style="background: #fff3cd; padding: 30px; border-radius: 8px; margin: 20px 0; text-align: center; border: 1px solid #ffeaa7;">
            <h2 style="color: #856404; font-size: 36px; letter-spacing: 8px; margin: 0; font-family: monospace;">{{otp}}</h2>
        </div>
        
        <p><strong>Important:</strong> This reset code will expire in {{expiryMinutes}} minutes for security reasons.</p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetUrl}}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Reset Password</a>
        </div>
        
        <p>If you didn't request a password reset, please ignore this email or contact our support team immediately.</p>
        
        <p>For security reasons, never share this code with anyone.</p>
        
        <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
    </div>
    
    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            \xa9 {{currentYear}} HashCoreX. All rights reserved.<br>
            Need help? Contact us at {{supportEmail}}
        </p>
    </div>
</body>
</html>`,textContent:`Password Reset - HashCoreX

Hello {{firstName}}!

We received a request to reset your password. Please use the following code to reset your password:

{{otp}}

Important: This reset code will expire in {{expiryMinutes}} minutes for security reasons.

Reset your password: {{resetUrl}}

If you didn't request a password reset, please ignore this email or contact our support team immediately.

For security reasons, never share this code with anyone.

Best regards,
The HashCoreX Team

\xa9 {{currentYear}} HashCoreX. All rights reserved.
Need help? Contact us at {{supportEmail}}`,variables:{firstName:"User first name",lastName:"User last name",otp:"Reset code",expiryMinutes:"Code expiry time in minutes",resetUrl:"Password reset URL",supportEmail:"Support email address",currentYear:"Current year"}},{name:"kyc-approved",subject:"HashCoreX - KYC Verification Approved!",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Approved</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">KYC Approved!</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Verification Complete</p>
    </div>

    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Congratulations {{firstName}}!</h2>

        <p>Your KYC verification has been <strong>approved</strong>! You now have full access to all HashCoreX features.</p>

        <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;">
            <h3 style="margin-top: 0; color: #155724;">✅ What's Now Available:</h3>
            <ul style="color: #155724; margin: 0;">
                <li>Purchase mining units</li>
                <li>Withdraw earnings</li>
                <li>Access all premium features</li>
                <li>Higher transaction limits</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{miningUrl}}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; margin-right: 10px;">Start Mining</a>
            <a href="{{dashboardUrl}}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">View Dashboard</a>
        </div>

        <p>Thank you for completing the verification process. Start earning with our solar-powered mining platform!</p>

        <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
    </div>

    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            \xa9 {{currentYear}} HashCoreX. All rights reserved.<br>
            Need help? Contact us at {{supportEmail}}
        </p>
    </div>
</body>
</html>`,textContent:`KYC Approved - HashCoreX

Congratulations {{firstName}}!

Your KYC verification has been approved! You now have full access to all HashCoreX features.

What's Now Available:
- Purchase mining units
- Withdraw earnings
- Access all premium features
- Higher transaction limits

Start mining: {{miningUrl}}
View dashboard: {{dashboardUrl}}

Thank you for completing the verification process. Start earning with our solar-powered mining platform!

Best regards,
The HashCoreX Team

\xa9 {{currentYear}} HashCoreX. All rights reserved.
Need help? Contact us at {{supportEmail}}`,variables:{firstName:"User first name",lastName:"User last name",dashboardUrl:"Dashboard URL",miningUrl:"Mining page URL",supportEmail:"Support email address",currentYear:"Current year"}},{name:"kyc-rejected",subject:"HashCoreX - KYC Verification Requires Attention",htmlContent:`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Requires Attention</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">KYC Requires Attention</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Verification Update</p>
    </div>

    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}},</h2>

        <p>We've reviewed your KYC submission and need you to resubmit your documents.</p>

        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;">
            <h3 style="margin-top: 0; color: #721c24;">📋 Reason for Resubmission:</h3>
            <p style="color: #721c24; margin: 0;">{{reason}}</p>
        </div>

        <h3>Next Steps:</h3>
        <ol>
            <li>Review the feedback above</li>
            <li>Prepare clear, high-quality documents</li>
            <li>Resubmit your KYC application</li>
            <li>Wait for our team to review (usually 24-48 hours)</li>
        </ol>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{kycUrl}}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Resubmit KYC</a>
        </div>

        <p>If you have any questions about the verification process, please contact our support team.</p>

        <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
    </div>

    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            \xa9 {{currentYear}} HashCoreX. All rights reserved.<br>
            Need help? Contact us at {{supportEmail}}
        </p>
    </div>
</body>
</html>`,textContent:`KYC Requires Attention - HashCoreX

Hello {{firstName}},

We've reviewed your KYC submission and need you to resubmit your documents.

Reason for Resubmission:
{{reason}}

Next Steps:
1. Review the feedback above
2. Prepare clear, high-quality documents
3. Resubmit your KYC application
4. Wait for our team to review (usually 24-48 hours)

Resubmit KYC: {{kycUrl}}

If you have any questions about the verification process, please contact our support team.

Best regards,
The HashCoreX Team

\xa9 {{currentYear}} HashCoreX. All rights reserved.
Need help? Contact us at {{supportEmail}}`,variables:{firstName:"User first name",lastName:"User last name",reason:"Rejection reason",kycUrl:"KYC page URL",supportEmail:"Support email address",currentYear:"Current year"}}];async function u(){let{prisma:e}=await Promise.resolve().then(r.bind(r,6710));try{for(let t of(console.log("\uD83C\uDF31 Seeding email templates..."),p))await e.emailTemplate.upsert({where:{name:t.name},update:{subject:t.subject,htmlContent:t.htmlContent,textContent:t.textContent,variables:t.variables,isActive:!0},create:{name:t.name,subject:t.subject,htmlContent:t.htmlContent,textContent:t.textContent,variables:t.variables,isActive:!0}});console.log(`✅ Successfully seeded ${p.length} email templates`)}catch(e){throw console.error("❌ Error seeding email templates:",e),e}}async function h(){try{console.log("Initializing background services..."),await u(),await c.g.initializeTransporter(),await l.j.start(),d.c.start(),console.log("✅ Server-side scheduler started"),console.log("All background services initialized successfully")}catch(e){throw console.error("Error initializing background services:",e),e}}let m=!1;async function g(e){try{return m||(console.log("Initializing background services..."),await h(),m=!0,console.log("Background services initialized successfully")),n.NextResponse.json({success:!0,message:"Services initialized successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Error initializing services:",e),n.NextResponse.json({success:!1,error:"Failed to initialize services",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}setTimeout(async()=>{if(!m)try{console.log("Auto-initializing background services..."),await h(),m=!0,console.log("Background services auto-initialized successfully")}catch(e){console.error("Error auto-initializing services:",e)}},1e3);let f=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/init/route",pathname:"/api/init",filename:"route",bundlePath:"app/api/init/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\init\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:y,workUnitAsyncStorage:x,serverHooks:v}=f;function b(){return(0,o.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:x})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},61904:(e,t,r)=>{r.d(t,{b:()=>n,g:()=>o});var i=r(49526),a=r(6710);class s{async initializeTransporter(){try{let e=await a.prisma.sMTPConfiguration.findFirst({where:{isActive:!0}});if(!e)return void console.warn("No active SMTP configuration found");this.currentConfig=e,this.transporter=i.createTransporter({host:e.host,port:e.port,secure:e.secure,auth:{user:e.username,pass:e.password},tls:{rejectUnauthorized:!1}}),await this.transporter.verify(),console.log("✅ Email service initialized successfully")}catch(e){console.error("❌ Failed to initialize email service:",e),this.transporter=null,this.currentConfig=null}}async sendEmail(e){try{if((!this.transporter||!this.currentConfig)&&(await this.initializeTransporter(),!this.transporter||!this.currentConfig))throw Error("Email service not configured");let t={from:`"${this.currentConfig.senderName}" <${this.currentConfig.senderEmail}>`,to:e.to,subject:e.subject,html:e.html,text:e.text};return await this.transporter.sendMail(t),await this.logEmail({userId:e.userId,recipient:e.to,subject:e.subject,templateId:e.templateId,status:"SENT",sentAt:new Date}),console.log(`✅ Email sent successfully to ${e.to}`),!0}catch(t){return console.error(`❌ Failed to send email to ${e.to}:`,t),await this.logEmail({userId:e.userId,recipient:e.to,subject:e.subject,templateId:e.templateId,status:"FAILED",errorMessage:t instanceof Error?t.message:"Unknown error"}),!1}}async sendTemplateEmail(e,t,r={},i){try{let a=await this.getTemplate(e);if(!a)throw Error(`Template '${e}' not found`);let s=this.processTemplate(a.subject,r),o=this.processTemplate(a.htmlContent,r),n=a.textContent?this.processTemplate(a.textContent,r):void 0;return await this.sendEmail({to:t,subject:s,html:o,text:n,templateId:a.id,userId:i})}catch(r){return console.error(`❌ Failed to send template email '${e}' to ${t}:`,r),!1}}processTemplate(e,t){let r=e;return Object.entries(t).forEach(([e,t])=>{let i=RegExp(`{{\\s*${e}\\s*}}`,"g");r=r.replace(i,String(t))}),r}async getTemplate(e){try{return await a.prisma.emailTemplate.findFirst({where:{name:e,isActive:!0}})}catch(t){return console.error(`❌ Failed to get template '${e}':`,t),null}}async logEmail(e){try{await a.prisma.emailLog.create({data:e})}catch(e){console.error("❌ Failed to log email:",e)}}async testConnection(){try{if(await this.initializeTransporter(),!this.transporter)return{success:!1,error:"Failed to initialize transporter"};return await this.transporter.verify(),{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async getEmailStats(){try{let[e,t,r]=await Promise.all([a.prisma.emailLog.count({where:{status:"SENT"}}),a.prisma.emailLog.count({where:{status:"FAILED"}}),a.prisma.emailLog.findMany({take:10,orderBy:{createdAt:"desc"},include:{user:{select:{email:!0,firstName:!0,lastName:!0}},template:{select:{name:!0}}}})]);return{totalSent:e,totalFailed:t,recentEmails:r}}catch(e){return console.error("❌ Failed to get email stats:",e),{totalSent:0,totalFailed:0,recentEmails:[]}}}constructor(){this.transporter=null,this.currentConfig=null}}let o=new s,n={USER_REGISTRATION:"user-registration",EMAIL_VERIFICATION:"email-verification",PASSWORD_RESET:"password-reset",KYC_APPROVED:"kyc-approved",KYC_REJECTED:"kyc-rejected",WITHDRAWAL_REQUESTED:"withdrawal-requested",WITHDRAWAL_APPROVED:"withdrawal-approved",WITHDRAWAL_REJECTED:"withdrawal-rejected",MINING_UNIT_PURCHASED:"mining-unit-purchased",WEEKLY_EARNINGS:"weekly-earnings",BINARY_COMMISSION:"binary-commission",REFERRAL_COMMISSION:"referral-commission"}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580,9526,3306,2746,5112,9480,1827],()=>r(53189));module.exports=i})();