"use strict";(()=>{var e={};e.id=1068,e.ids=[1068],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>f,DY:()=>y,Lx:()=>w,Oj:()=>g,b9:()=>h,qc:()=>x});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",u=async e=>await a.Ay.hash(e,12),c=async(e,t)=>await a.Ay.compare(e,t),d=e=>i().sign(e,o,{expiresIn:l}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=m(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await u(e.password),i=!1;do a=p(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await c(e.password,t.password))throw Error("Invalid email or password");return{token:d({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},g=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},f=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},37707:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{POST:()=>d});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(12909),u=r(2746),c=r(6710);async function d(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==r.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});console.log(`Manual binary matching initiated by admin: ${r.email}`);let a=await (0,u.E5)();return await c.AJ.create({action:"MANUAL_BINARY_MATCHING_TRIGGERED",details:{triggeredBy:r.email,adminId:r.id,result:a,timestamp:new Date().toISOString()}}),console.log(`Manual binary matching completed. Processed ${a.usersProcessed} users with total payouts: $${a.totalPayouts.toFixed(2)}`),o.NextResponse.json({success:!0,message:"Binary matching process completed successfully",data:{usersProcessed:a.usersProcessed,totalPayouts:a.totalPayouts,matchingResults:a.matchingResults}})}catch(t){console.error("Manual binary matching error:",t);try{let{user:r}=await (0,l.b9)(e);await c.AJ.create({action:"MANUAL_BINARY_MATCHING_ERROR",details:{triggeredBy:r?.email||"unknown",error:t.message,timestamp:new Date().toISOString()}})}catch(e){console.error("Failed to log manual binary matching error:",e)}return o.NextResponse.json({success:!1,error:"Failed to process binary matching"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/binary-matching/manual/route",pathname:"/api/admin/binary-matching/manual",filename:"route",bundlePath:"app/api/admin/binary-matching/manual/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\binary-matching\\manual\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:y}=m;function w(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306,2746],()=>r(37707));module.exports=a})();