"use strict";(()=>{var e={};e.id=8483,e.ids=[8483],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>I,DY:()=>y,Lx:()=>h,Oj:()=>w,b9:()=>f,qc:()=>g});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await a.Ay.hash(e,12),l=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=m(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await l(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},I=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39902:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")},98625:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>k,routeModule:()=>N,serverHooks:()=>T,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>D});var a={};r.r(a),r.d(a,{POST:()=>g});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),d=r(6710),l=r(31183);let c=require("fs/promises");var p=r(39902),m=r(55511);let f={randomUUID:m.randomUUID},y=new Uint8Array(256),h=y.length,w=[];for(let e=0;e<256;++e)w.push((e+256).toString(16).slice(1));let I=function(e,t,r){if(f.randomUUID&&!t&&!e)return f.randomUUID();let a=(e=e||{}).random??e.rng?.()??(h>y.length-16&&((0,m.randomFillSync)(y),h=0),y.slice(h,h+=16));if(a.length<16)throw Error("Random bytes length must be >= 16");if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=a[e];return t}return function(e,t=0){return(w[e[t+0]]+w[e[t+1]]+w[e[t+2]]+w[e[t+3]]+"-"+w[e[t+4]]+w[e[t+5]]+"-"+w[e[t+6]]+w[e[t+7]]+"-"+w[e[t+8]]+w[e[t+9]]+"-"+w[e[t+10]]+w[e[t+11]]+w[e[t+12]]+w[e[t+13]]+w[e[t+14]]+w[e[t+15]]).toLowerCase()}(a)};async function g(e){try{let t,{authenticated:r,user:a}=await (0,u.b9)(e);if(!r||!a)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=await e.formData(),i=s.get("file"),n=s.get("documentType"),m=s.get("idType"),f=s.get("documentSide");if(!i)return o.NextResponse.json({success:!1,error:"No file provided"},{status:400});if(!n||!["ID_DOCUMENT","SELFIE"].includes(n))return o.NextResponse.json({success:!1,error:"Invalid document type"},{status:400});if("ID_DOCUMENT"===n){if(!m||!["NATIONAL_ID","PASSPORT","DRIVING_LICENSE"].includes(m))return o.NextResponse.json({success:!1,error:"Invalid ID type"},{status:400});if(!f||!["FRONT","BACK"].includes(f))return o.NextResponse.json({success:!1,error:"Invalid document side"},{status:400})}if(!i.type.startsWith("image/"))return o.NextResponse.json({success:!1,error:"Only image files are allowed"},{status:400});if(i.size>5242880)return o.NextResponse.json({success:!1,error:"File size must be less than 5MB"},{status:400});let y=(0,p.join)(process.cwd(),"public","uploads","kyc");try{await (0,c.mkdir)(y,{recursive:!0})}catch(e){}let h=i.name.split(".").pop(),w=[a.id,n];m&&w.push(m),f&&w.push(f),w.push(I());let g=`${w.join("_")}.${h}`,N=(0,p.join)(y,g),x=`/uploads/kyc/${g}`,D=await i.arrayBuffer(),T=Buffer.from(D);await (0,c.writeFile)(N,T);let k={userId:a.id,documentType:n};"ID_DOCUMENT"===n&&(k.idType=m,k.documentSide=f);let E=await l.prisma.kYCDocument.findFirst({where:k});if(E)t=await l.prisma.kYCDocument.update({where:{id:E.id},data:{filePath:x,status:"PENDING",reviewedAt:null,reviewedBy:null,rejectionReason:null}});else{let e={userId:a.id,documentType:n,filePath:x,status:"PENDING"};"ID_DOCUMENT"===n&&(e.idType=m,e.documentSide=f),t=await l.prisma.kYCDocument.create({data:e})}let S=await l.prisma.kYCDocument.findMany({where:{userId:a.id}}),v=S.some(e=>"SELFIE"===e.documentType),R=!1,C=S.find(e=>"ID_DOCUMENT"===e.documentType)?.idType;if(C){let e=S.filter(e=>"ID_DOCUMENT"===e.documentType&&e.idType===C);if("PASSPORT"===C)R=e.some(e=>"FRONT"===e.documentSide);else{let t=e.some(e=>"FRONT"===e.documentSide),r=e.some(e=>"BACK"===e.documentSide);R=t&&r}}return R&&v&&await l.prisma.user.update({where:{id:a.id},data:{kycStatus:"PENDING"}}),await d.AJ.create({action:"KYC_DOCUMENT_UPLOADED",userId:a.id,details:{documentType:n,idType:m||null,documentSide:f||null,fileName:g,fileSize:i.size,documentId:t.id},ipAddress:e.headers.get("x-forwarded-for")||"unknown",userAgent:e.headers.get("user-agent")||"unknown"}),o.NextResponse.json({success:!0,message:"Document uploaded successfully",data:{documentId:t.id,documentType:t.documentType,status:t.status}})}catch(e){return console.error("KYC document upload error:",e),o.NextResponse.json({success:!1,error:"Failed to upload document"},{status:500})}}let N=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/kyc/upload/route",pathname:"/api/kyc/upload",filename:"route",bundlePath:"app/api/kyc/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\kyc\\upload\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:x,workUnitAsyncStorage:D,serverHooks:T}=N;function k(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:D})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(98625));module.exports=a})();