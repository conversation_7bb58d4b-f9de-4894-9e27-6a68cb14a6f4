import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import nodemailer from 'nodemailer';

// POST - Test SMTP connection
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check admin role
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      host,
      port,
      secure,
      username,
      password,
      senderName,
      senderEmail,
    } = body;

    // Validation
    if (!host || !port || !username || !password || !senderName || !senderEmail) {
      return NextResponse.json(
        { success: false, error: 'All fields are required for testing' },
        { status: 400 }
      );
    }

    // Create transporter for testing
    const transporter = nodemailer.createTransport({
      host,
      port: parseInt(port),
      secure: Boolean(secure),
      auth: {
        user: username,
        pass: password,
      },
      tls: {
        rejectUnauthorized: false, // For development/testing
      },
    });

    // Test connection
    await transporter.verify();

    // Send test email
    const testMailOptions = {
      from: `"${senderName}" <${senderEmail}>`,
      to: senderEmail, // Send test email to sender email
      subject: 'HashCoreX SMTP Test Email',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">SMTP Configuration Test</h2>
          <p>This is a test email to verify your SMTP configuration is working correctly.</p>
          <p><strong>Configuration Details:</strong></p>
          <ul>
            <li>Host: ${host}</li>
            <li>Port: ${port}</li>
            <li>Secure: ${secure ? 'Yes' : 'No'}</li>
            <li>Username: ${username}</li>
            <li>Sender: ${senderName} &lt;${senderEmail}&gt;</li>
          </ul>
          <p>If you received this email, your SMTP configuration is working properly!</p>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 12px;">
            This is an automated test email from HashCoreX Email System.
          </p>
        </div>
      `,
      text: `
SMTP Configuration Test

This is a test email to verify your SMTP configuration is working correctly.

Configuration Details:
- Host: ${host}
- Port: ${port}
- Secure: ${secure ? 'Yes' : 'No'}
- Username: ${username}
- Sender: ${senderName} <${senderEmail}>

If you received this email, your SMTP configuration is working properly!

This is an automated test email from HashCoreX Email System.
      `,
    };

    await transporter.sendMail(testMailOptions);

    return NextResponse.json({
      success: true,
      message: 'SMTP connection test successful! A test email has been sent.',
    });
  } catch (error) {
    console.error('SMTP test error:', error);
    
    let errorMessage = 'SMTP connection test failed';
    
    if (error instanceof Error) {
      if (error.message.includes('EAUTH')) {
        errorMessage = 'Authentication failed. Please check your username and password.';
      } else if (error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Connection refused. Please check your host and port settings.';
      } else if (error.message.includes('ETIMEDOUT')) {
        errorMessage = 'Connection timeout. Please check your host and port settings.';
      } else if (error.message.includes('ENOTFOUND')) {
        errorMessage = 'Host not found. Please check your SMTP host setting.';
      } else {
        errorMessage = `SMTP Error: ${error.message}`;
      }
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 400 }
    );
  }
}
