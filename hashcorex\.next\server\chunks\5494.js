exports.id=5494,exports.ids=[5494],exports.modules={2470:(e,t,r)=>{Promise.resolve().then(r.bind(r,57445))},2643:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(60687),n=r(43210),o=r.n(n),l=r(24224),a=r(4780);let i=(0,l.F)("inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg",{variants:{variant:{primary:"bg-yellow-500 text-white focus:ring-yellow-500",secondary:"bg-gray-200 text-gray-800 focus:ring-gray-500",success:"bg-emerald-500 text-white focus:ring-emerald-500",danger:"bg-red-500 text-white focus:ring-red-500",warning:"bg-yellow-500 text-white focus:ring-yellow-500",destructive:"bg-red-600 text-white focus:ring-red-500",outline:"border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500",ghost:"text-gray-600 focus:ring-yellow-500 rounded-lg",link:"text-yellow-600 underline-offset-4 focus:ring-yellow-500",premium:"bg-slate-800 text-white focus:ring-slate-500",glass:"glass-morphism text-slate-900 backdrop-blur-xl border border-white/20"},size:{sm:"h-10 px-4 text-sm rounded-lg",md:"h-12 px-6 text-base rounded-xl",lg:"h-14 px-8 text-lg rounded-xl",xl:"h-16 px-10 text-xl rounded-2xl font-bold",icon:"h-12 w-12 rounded-xl"}},defaultVariants:{variant:"primary",size:"md"}}),c=o().forwardRef(({className:e,variant:t,size:r,loading:n,leftIcon:o,rightIcon:l,children:c,disabled:d,...h},x)=>(0,s.jsxs)("button",{className:(0,a.cn)(i({variant:t,size:r,className:e})),ref:x,disabled:d||n,...h,children:[n&&(0,s.jsx)("div",{className:"mr-2",children:(0,s.jsx)("div",{className:"spinner"})}),o&&!n&&(0,s.jsx)("span",{className:"mr-2",children:o}),c,l&&!n&&(0,s.jsx)("span",{className:"ml-2",children:l})]}));c.displayName="Button"},4780:(e,t,r)=>{"use strict";r.d(t,{D1:()=>m,Oj:()=>h,Yq:()=>i,ZU:()=>u,ZV:()=>a,cn:()=>o,jI:()=>x,lW:()=>d,r6:()=>c,vv:()=>l});var s=r(49384),n=r(82348);function o(...e){return(0,n.QP)((0,s.$)(e))}function l(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}function a(e,t=2){return new Intl.NumberFormat("en-US",{minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}function i(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}function d(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.focus(),t.select();try{return document.execCommand("copy"),Promise.resolve()}catch(e){return Promise.reject(e)}finally{document.body.removeChild(t)}}function h(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}function x(e){return e>=1e3?`${(e/1e3).toFixed(1)}K TH/s`:`${e.toFixed(2)} TH/s`}function u(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),n=Math.floor(r%864e5/36e5);return{days:s,hours:n,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}function m(){let e=new Date,t=new Date;t.setUTCDate(e.getUTCDate()+(6-e.getUTCDay())),t.setUTCHours(15,0,0,0),e>t&&t.setUTCDate(t.getUTCDate()+7);let r=t.getTime()-e.getTime(),s=Math.floor(r/864e5),n=Math.floor(r%864e5/36e5);return{days:s,hours:n,minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}}},26207:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},34014:(e,t,r)=>{Promise.resolve().then(r.bind(r,49567))},49567:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\hooks\\useAuth.tsx","useAuth")},57445:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,AuthProvider:()=>l});var s=r(60687),n=r(43210);let o=(0,n.createContext)(void 0),l=({children:e})=>{let[t,r]=(0,n.useState)(null),[l,a]=(0,n.useState)(!0),[i,c]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=localStorage.getItem("auth-user");if(e)try{let t=JSON.parse(e);r(t)}catch(e){console.error("Error parsing stored user:",e),localStorage.removeItem("auth-user")}d()},[]);let d=async()=>{try{let e=await fetch("/api/auth/me",{credentials:"include"});if(e.ok){let t=await e.json();t.success?(r(t.data),localStorage.setItem("auth-user",JSON.stringify(t.data))):(r(null),localStorage.removeItem("auth-user"))}else r(null),localStorage.removeItem("auth-user")}catch(e){console.error("Auth check failed:",e),r(null),localStorage.removeItem("auth-user")}finally{a(!1),c(!0)}},h=async(e,t)=>{a(!0);try{let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),n=await s.json();if(!n.success)throw Error(n.error||"Login failed");r(n.data.user),localStorage.setItem("auth-user",JSON.stringify(n.data.user)),c(!0)}catch(e){throw e}finally{a(!1)}},x=async(e,t,r,s,n,o,l)=>{a(!0);try{let a=l?`/api/auth/register?side=${l}`:"/api/auth/register",i=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,firstName:t,lastName:r,password:s,confirmPassword:n,referralCode:o})}),c=await i.json();if(!c.success)throw Error(c.error||"Registration failed");await h(e,s)}catch(e){throw e}finally{a(!1)}},u=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"})}catch(e){console.error("Logout error:",e)}finally{r(null),localStorage.removeItem("auth-user"),c(!0)}},m=async()=>{await d()};return(0,s.jsx)(o.Provider,{value:{user:t,loading:l,isInitialized:i,login:h,register:x,logout:u,refreshUser:m},children:e})},a=()=>{let e=(0,n.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},61135:()=>{},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71180:(e,t,r)=>{"use strict";r.d(t,{Lc:()=>l,hK:()=>a,NC:()=>o,MX:()=>n,Kj:()=>i});var s=r(60687);r(43210);let n=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"2",y1:"8",x2:"22",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"12",x2:"22",y2:"12",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"2",y1:"16",x2:"22",y2:"16",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"6",y1:"4",x2:"6",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"10",y1:"4",x2:"10",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"14",y1:"4",x2:"14",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"4",x2:"18",y2:"20",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("circle",{cx:"20",cy:"2",r:"1",fill:"currentColor"}),(0,s.jsx)("path",{d:"M19 1l1 1-1 1-1-1z",fill:"currentColor"})]}),o=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("rect",{x:"2",y:"6",width:"20",height:"12",rx:"2",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("rect",{x:"4",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"8",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"4",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"8",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"12",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("rect",{x:"16",y:"12",width:"3",height:"3",rx:"0.5",fill:"currentColor"}),(0,s.jsx)("circle",{cx:"20",cy:"4",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"20",y1:"4",x2:"20",y2:"6",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"18",y1:"2",x2:"22",y2:"2",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"19",y1:"1",x2:"21",y2:"3",stroke:"currentColor",strokeWidth:"1"}),(0,s.jsx)("line",{x1:"21",y1:"1",x2:"19",y2:"3",stroke:"currentColor",strokeWidth:"1"})]}),l=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 12h8M12 8v8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("line",{x1:"12",y1:"6",x2:"12",y2:"8",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"18",stroke:"currentColor",strokeWidth:"2"})]}),a=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("path",{d:"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z",fill:"currentColor"})]}),i=({className:e,size:t=24})=>(0,s.jsxs)("svg",{width:t,height:t,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,s.jsx)("line",{x1:"12",y1:"12",x2:"12",y2:"22",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{d:"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("path",{d:"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z",stroke:"currentColor",strokeWidth:"2",fill:"none"}),(0,s.jsx)("circle",{cx:"12",cy:"12",r:"1",fill:"currentColor"}),(0,s.jsx)("line",{x1:"10",y1:"22",x2:"14",y2:"22",stroke:"currentColor",strokeWidth:"2"})]})},76276:(e,t,r)=>{"use strict";r.d(t,{$n:()=>s.$,Zp:()=>i,Wu:()=>h,aR:()=>c,ZB:()=>d,pd:()=>x,Rh:()=>g,aF:()=>f,G_:()=>v,eC:()=>k});var s=r(2643),n=r(60687),o=r(43210),l=r.n(o),a=r(4780);let i=l().forwardRef(({className:e,children:t,...r},s)=>(0,n.jsx)("div",{ref:s,className:(0,a.cn)("rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl overflow-hidden",e),...r,children:t}));i.displayName="Card";let c=l().forwardRef(({className:e,children:t,...r},s)=>(0,n.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6 pb-4",e),...r,children:t}));c.displayName="CardHeader";let d=l().forwardRef(({className:e,children:t,...r},s)=>(0,n.jsx)("h3",{ref:s,className:(0,a.cn)("text-xl font-semibold leading-none tracking-tight text-dark-900",e),...r,children:t}));d.displayName="CardTitle",l().forwardRef(({className:e,children:t,...r},s)=>(0,n.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-gray-500",e),...r,children:t})).displayName="CardDescription";let h=l().forwardRef(({className:e,children:t,...r},s)=>(0,n.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",e),...r,children:t}));h.displayName="CardContent",l().forwardRef(({className:e,children:t,...r},s)=>(0,n.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",e),...r,children:t})).displayName="CardFooter";let x=l().forwardRef(({className:e,type:t,label:r,error:s,leftIcon:o,rightIcon:l,...i},c)=>(0,n.jsxs)("div",{className:"w-full",children:[r&&(0,n.jsx)("label",{className:"block text-sm font-medium text-dark-700 mb-2",children:r}),(0,n.jsxs)("div",{className:"relative",children:[o&&(0,n.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,n.jsx)("span",{className:"text-gray-400",children:o})}),(0,n.jsx)("input",{type:t,className:(0,a.cn)("flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300",o&&"pl-12",l&&"pr-12",s&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:c,...i}),l&&(0,n.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,n.jsx)("span",{className:"text-gray-400",children:l})})]}),s&&(0,n.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s})]}));x.displayName="Input";var u=r(51215),m=r(11860);let f=({isOpen:e,onClose:t,title:r,children:l,size:i="md",showCloseButton:c=!0,darkMode:d=!1})=>{if((0,o.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),(0,o.useEffect)(()=>{let r=e=>{"Escape"===e.key&&t()};return e&&document.addEventListener("keydown",r),()=>{document.removeEventListener("keydown",r)}},[e,t]),!e)return null;let h=(0,n.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),(0,n.jsxs)("div",{className:(0,a.cn)("relative w-full rounded-xl shadow-xl transform transition-all",d?"bg-slate-800":"bg-white",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[i]),onClick:e=>e.stopPropagation(),children:[(0,n.jsxs)("div",{className:(0,a.cn)("flex items-center justify-between p-6 border-b",d?"border-slate-700":"border-gray-200"),children:[(0,n.jsx)("h2",{className:(0,a.cn)("text-xl font-semibold",d?"text-white":"text-dark-900"),children:r}),c&&(0,n.jsx)(s.$,{variant:"ghost",size:"icon",onClick:t,className:"h-8 w-8 rounded-full",children:(0,n.jsx)(m.A,{className:"h-4 w-4"})})]}),(0,n.jsx)("div",{className:"p-6",children:l})]})]});return(0,u.createPortal)(h,document.body)},g=({size:e="md",className:t,text:r})=>(0,n.jsxs)("div",{className:(0,a.cn)("flex flex-col items-center justify-center",t),children:[(0,n.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-solar-500",{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e])}),r&&(0,n.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:r})]});var w=r(43649),y=r(5336),p=r(96882);let b=({isOpen:e,onClose:t,onConfirm:r,title:o,message:l,confirmText:i="Confirm",cancelText:c="Cancel",variant:d="default",darkMode:h=!1,loading:x=!1})=>{if(!e)return null;let f=(0,n.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),(0,n.jsxs)("div",{className:(0,a.cn)("relative w-full max-w-md rounded-xl shadow-xl transform transition-all",h?"bg-slate-800 border border-slate-700":"bg-white"),onClick:e=>e.stopPropagation(),children:[(0,n.jsxs)("div",{className:(0,a.cn)("flex items-center justify-between p-6 border-b",h?"border-slate-700":"border-gray-200"),children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(d){case"danger":return(0,n.jsx)(w.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,n.jsx)(w.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,n.jsx)(y.A,{className:"h-6 w-6 text-green-500"});default:return(0,n.jsx)(p.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,n.jsx)("h2",{className:(0,a.cn)("text-lg font-semibold",h?"text-white":"text-gray-900"),children:o})]}),(0,n.jsx)(s.$,{variant:"ghost",size:"icon",onClick:t,disabled:x,className:"h-8 w-8 rounded-full",children:(0,n.jsx)(m.A,{className:"h-4 w-4"})})]}),(0,n.jsx)("div",{className:"p-6",children:"string"==typeof l?(0,n.jsx)("p",{className:(0,a.cn)("text-sm leading-relaxed",h?"text-slate-300":"text-gray-600"),children:l}):(0,n.jsx)("div",{className:(0,a.cn)("text-sm leading-relaxed",h?"text-slate-300":"text-gray-600"),children:l})}),(0,n.jsxs)("div",{className:(0,a.cn)("flex items-center justify-end space-x-3 p-6 border-t",h?"border-slate-700":"border-gray-200"),children:[(0,n.jsx)(s.$,{variant:"outline",onClick:t,disabled:x,className:(0,a.cn)(h?"border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white":""),children:c}),(0,n.jsx)(s.$,{variant:(()=>{switch(d){case"danger":return"destructive";case"warning":return"warning";default:return"default"}})(),onClick:r,disabled:x,className:(0,a.cn)(x&&"opacity-50 cursor-not-allowed"),children:x?"Processing...":i})]})]})]});return(0,u.createPortal)(f,document.body)},v=()=>{let[e,t]=l().useState({isOpen:!1,title:"",message:"",onConfirm:()=>{}}),[r,s]=l().useState(!1),o=()=>{r||t(e=>({...e,isOpen:!1}))};return{showConfirm:e=>{t({isOpen:!0,...e,onConfirm:async()=>{s(!0);try{await e.onConfirm(),t(e=>({...e,isOpen:!1}))}catch(e){console.error("Confirm action failed:",e)}finally{s(!1)}}})},hideConfirm:o,ConfirmDialog:()=>(0,n.jsx)(b,{isOpen:e.isOpen,onClose:o,onConfirm:e.onConfirm,title:e.title,message:e.message,variant:e.variant,confirmText:e.confirmText,cancelText:e.cancelText,darkMode:e.darkMode,loading:r}),loading:r}};var j=r(93613);let C=({isOpen:e,onClose:t,title:r,message:o,variant:l="info",darkMode:i=!1,showCloseButton:c=!0,buttonText:d="OK",size:h="md"})=>{if(!e)return null;let x=(0,n.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:t,children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,n.jsxs)("div",{className:(0,a.cn)("relative w-full rounded-xl shadow-xl transform transition-all",i?"bg-slate-800 border border-slate-700":"bg-white",{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"}[h]),onClick:e=>e.stopPropagation(),children:[(0,n.jsxs)("div",{className:(0,a.cn)("flex items-center justify-between p-6 border-b",i?"border-slate-700":"border-gray-200"),children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(l){case"error":return(0,n.jsx)(j.A,{className:"h-6 w-6 text-red-500"});case"warning":return(0,n.jsx)(w.A,{className:"h-6 w-6 text-yellow-500"});case"success":return(0,n.jsx)(y.A,{className:"h-6 w-6 text-green-500"});default:return(0,n.jsx)(p.A,{className:"h-6 w-6 text-blue-500"})}})(),(0,n.jsx)("h2",{className:(0,a.cn)("text-lg font-semibold",i?"text-white":"text-gray-900"),children:r})]}),c&&(0,n.jsx)(s.$,{variant:"ghost",size:"icon",onClick:t,className:"h-8 w-8 rounded-full",children:(0,n.jsx)(m.A,{className:"h-4 w-4"})})]}),(0,n.jsx)("div",{className:"p-6",children:"string"==typeof o?(0,n.jsx)("p",{className:(0,a.cn)("text-sm leading-relaxed",i?"text-slate-300":"text-gray-600"),children:o}):(0,n.jsx)("div",{className:(0,a.cn)("text-sm leading-relaxed",i?"text-slate-300":"text-gray-600"),children:o})}),(0,n.jsx)("div",{className:(0,a.cn)("flex items-center justify-end p-6 border-t",i?"border-slate-700":"border-gray-200"),children:(0,n.jsx)(s.$,{variant:(()=>{switch(l){case"error":return"danger";case"warning":return"warning";case"success":return"success";default:return"primary"}})(),onClick:t,className:"min-w-[80px]",children:d})})]})]});return(0,u.createPortal)(x,document.body)},k=()=>{let[e,t]=l().useState({isOpen:!1,title:"",message:""}),r=()=>{t(e=>({...e,isOpen:!1}))};return{showMessage:e=>{t({isOpen:!0,...e})},hideMessage:r,MessageBoxComponent:()=>(0,n.jsx)(C,{...e,onClose:r})}}},86455:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var s=r(37413),n=r(25091),o=r.n(n);r(61135);var l=r(49567);let a={title:"HashCoreX - Solar-Powered Cloud Mining",description:"Sustainable cryptocurrency mining with solar energy. Earn daily ROI with our eco-friendly mining platform."};function i({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${o().variable} font-sans antialiased`,children:(0,s.jsx)(l.AuthProvider,{children:e})})})}}};