"use strict";(()=>{var e={};e.id=5409,e.ids=[5409],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>x,DY:()=>f,Lx:()=>y,Oj:()=>w,b9:()=>m,qc:()=>k});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await s.Ay.hash(e,12),c=async(e,t)=>await s.Ay.compare(e,t),d=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},h=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},m=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},f=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await l(e.password),i=!1;do s=h(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},y=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await c(e.password,t.password))throw Error("Invalid email or password");return{token:d({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),k=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72463:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>d});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),l=r(6710);async function c(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let s=await l.OZ.findByUserId(r.id);return o.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Support tickets fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch support tickets"},{status:500})}}async function d(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{subject:s,message:a,priority:i="MEDIUM",category:n}=await e.json();if(!s||!a)return o.NextResponse.json({success:!1,error:"Subject and message are required"},{status:400});if(!["LOW","MEDIUM","HIGH","URGENT"].includes(i))return o.NextResponse.json({success:!1,error:"Invalid priority level"},{status:400});let c=await l.OZ.create({userId:r.id,subject:s.trim(),message:a.trim(),priority:i,category:n?.trim()||null});return o.NextResponse.json({success:!0,data:c,message:"Support ticket created successfully"})}catch(e){return console.error("Support ticket creation error:",e),o.NextResponse.json({success:!1,error:"Failed to create support ticket"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/support/tickets/route",pathname:"/api/support/tickets",filename:"route",bundlePath:"app/api/support/tickets/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\support\\tickets\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:f}=p;function y(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306],()=>r(72463));module.exports=s})();