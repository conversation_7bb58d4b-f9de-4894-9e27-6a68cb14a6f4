import { depositVerificationService } from './depositVerificationService';
import { scheduler } from './scheduler';
import { emailService } from './emailService';
import { seedEmailTemplates } from './emailTemplates';

/**
 * Initialize all background services
 */
export async function initializeServices() {
  try {
    console.log('Initializing background services...');

    // Seed email templates
    await seedEmailTemplates();

    // Initialize email service
    await emailService.initializeTransporter();

    // Start deposit verification service
    await depositVerificationService.start();

    // Start the server-side scheduler for mining earnings and binary matching
    if (process.env.NODE_ENV === 'production' || process.env.ENABLE_SCHEDULER === 'true') {
      scheduler.start();
      console.log('✅ Server-side scheduler started');
    } else {
      console.log('⚠️ Scheduler disabled in development mode. Set ENABLE_SCHEDULER=true to enable.');
    }

    console.log('All background services initialized successfully');
  } catch (error) {
    console.error('Error initializing background services:', error);
    throw error;
  }
}

/**
 * Shutdown all background services
 */
export function shutdownServices() {
  try {
    console.log('Shutting down background services...');

    // Stop deposit verification service
    depositVerificationService.stop();

    // Stop the scheduler
    scheduler.stop();

    console.log('All background services shut down successfully');
  } catch (error) {
    console.error('Error shutting down background services:', error);
  }
}
