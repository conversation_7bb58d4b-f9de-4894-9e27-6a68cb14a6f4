"use strict";(()=>{var e={};e.id=5399,e.ids=[5399],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>w,DY:()=>y,Lx:()=>A,Oj:()=>h,b9:()=>m,qc:()=>I});var a=r(85663),s=r(43205),n=r.n(s),i=r(6710);let l=process.env.JWT_SECRET||"fallback-secret-key",o=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),c=e=>n().sign(e,l,{expiresIn:o}),p=e=>{try{return n().verify(e,l)}catch(e){return null}},f=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},m=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=p(t);if(!r)return{authenticated:!1,user:null};let a=await i.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await i.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await i.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),n=!1;do a=f(),n=!await i.Gy.findByReferralId(a);while(!n);let l=await i.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,l.id,s)}return{id:l.id,email:l.email,referralId:l.referralId,kycStatus:l.kycStatus}},A=async e=>{let t=await i.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},h=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},w=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),I=async e=>{let t=await i.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79306:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>u});var s=r(96559),n=r(48088),i=r(37719),l=r(32190),o=r(12909),d=r(6710);async function u(e){try{let{authenticated:t,user:r}=await (0,o.b9)(e);if(!t||!r)return l.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let{searchParams:a}=new URL(e.url),s=parseInt(a.get("limit")||"20"),n=parseInt(a.get("offset")||"0"),i=a.get("search")||"",u=a.get("type")||"",c=a.get("status")||"",p=a.get("dateFrom")||"",f=a.get("dateTo")||"",m={userId:r.id};u&&"ALL"!==u&&(m.type=u),c&&"ALL"!==c&&(m.status=c),(p||f)&&(m.createdAt={},p&&(m.createdAt.gte=new Date(p)),f&&(m.createdAt.lte=new Date(f+"T23:59:59.999Z")));let y={limit:Math.min(s,100),offset:n};i&&(y.search=i),u&&"ALL"!==u&&(y.types=[u]),c&&"ALL"!==c&&(y.status=c);let A=(await d.DR.findByUserId(r.id,y)).filter(e=>"ADMIN_CREDIT"!==e.type&&"ADMIN_DEBIT"!==e.type).map(e=>{if("WITHDRAWAL"===e.type&&e.reference)return{id:e.id,type:e.type,category:"TRANSACTION",amount:e.amount,description:e.description,status:e.status,reference:e.reference,createdAt:e.createdAt,txid:null,usdtAddress:null,rejectionReason:null,processedAt:null};if("DEPOSIT"===e.type){let t=e.description.match(/TX: ([a-fA-F0-9]+)/),r=t?t[1]:null;return{id:e.id,type:e.type,category:"TRANSACTION",amount:e.amount,description:e.description,status:e.status,reference:e.reference,createdAt:e.createdAt,txid:r,usdtAddress:null,rejectionReason:null,processedAt:null}}return{id:e.id,type:e.type,category:"TRANSACTION",amount:e.amount,description:e.description,status:e.status,reference:e.reference,createdAt:e.createdAt,txid:null,usdtAddress:null,rejectionReason:null,processedAt:null}});A.sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime());let h=A.slice(n,n+s);return l.NextResponse.json({success:!0,data:{transactions:h,pagination:{limit:s,offset:n,total:A.length,hasMore:n+s<A.length},filters:{transactionTypes:["ALL","MINING_EARNINGS","DIRECT_REFERRAL","BINARY_BONUS","DEPOSIT","WITHDRAWAL","PURCHASE"],statusOptions:["ALL","PENDING","COMPLETED","FAILED","CANCELLED","CONFIRMED","PENDING_VERIFICATION","WAITING_FOR_CONFIRMATIONS"]}}})}catch(e){return console.error("Wallet transactions fetch error:",e),l.NextResponse.json({success:!1,error:"Failed to fetch wallet transactions"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/wallet/transactions/route",pathname:"/api/wallet/transactions",filename:"route",bundlePath:"app/api/wallet/transactions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\transactions\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:f,serverHooks:m}=c;function y(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:f})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(79306));module.exports=a})();