export const defaultEmailTemplates = [
  {
    name: 'user-registration',
    subject: 'Welcome to HashCoreX - Your Account is Ready!',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to HashCoreX</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to HashCoreX!</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Solar-Powered Cloud Mining Platform</p>
    </div>
    
    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}} {{lastName}}!</h2>
        
        <p>Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable cryptocurrency mining community.</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #495057;">Your Account Details:</h3>
            <p><strong>Email:</strong> {{email}}</p>
            <p><strong>Referral ID:</strong> {{referralId}}</p>
        </div>
        
        <h3>Next Steps:</h3>
        <ol>
            <li>Complete your KYC verification for full access</li>
            <li>Explore our mining packages</li>
            <li>Start earning with solar-powered mining</li>
            <li>Invite friends and earn referral commissions</li>
        </ol>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{dashboardUrl}}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Access Your Dashboard</a>
        </div>
        
        <p>If you have any questions, feel free to contact our support team at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>.</p>
        
        <p>Welcome aboard!</p>
        <p><strong>The HashCoreX Team</strong></p>
    </div>
    
    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            © {{currentYear}} HashCoreX. All rights reserved.<br>
            This email was sent to {{email}}
        </p>
    </div>
</body>
</html>`,
    textContent: `Welcome to HashCoreX!

Hello {{firstName}} {{lastName}}!

Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable cryptocurrency mining community.

Your Account Details:
- Email: {{email}}
- Referral ID: {{referralId}}

Next Steps:
1. Complete your KYC verification for full access
2. Explore our mining packages
3. Start earning with solar-powered mining
4. Invite friends and earn referral commissions

Access your dashboard: {{dashboardUrl}}

If you have any questions, feel free to contact our support team at {{supportEmail}}.

Welcome aboard!
The HashCoreX Team

© {{currentYear}} HashCoreX. All rights reserved.`,
    variables: {
      firstName: 'User first name',
      lastName: 'User last name',
      email: 'User email address',
      referralId: 'User referral ID',
      loginUrl: 'Login page URL',
      dashboardUrl: 'Dashboard URL',
      supportEmail: 'Support email address',
      currentYear: 'Current year'
    }
  },
  {
    name: 'email-verification',
    subject: 'HashCoreX - Email Verification Code',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Email Verification</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">HashCoreX Security</p>
    </div>
    
    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>
        
        <p>Please use the following verification code to verify your email address:</p>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <h2 style="color: #495057; font-size: 36px; letter-spacing: 8px; margin: 0; font-family: monospace;">{{otp}}</h2>
        </div>
        
        <p><strong>Important:</strong> This verification code will expire in {{expiryMinutes}} minutes for security reasons.</p>
        
        <p>If you didn't request this verification code, please ignore this email or contact our support team.</p>
        
        <p>For security reasons, never share this code with anyone.</p>
        
        <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
    </div>
    
    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            © {{currentYear}} HashCoreX. All rights reserved.<br>
            Need help? Contact us at {{supportEmail}}
        </p>
    </div>
</body>
</html>`,
    textContent: `Email Verification - HashCoreX

Hello {{firstName}}!

Please use the following verification code to verify your email address:

{{otp}}

Important: This verification code will expire in {{expiryMinutes}} minutes for security reasons.

If you didn't request this verification code, please ignore this email or contact our support team.

For security reasons, never share this code with anyone.

Best regards,
The HashCoreX Team

© {{currentYear}} HashCoreX. All rights reserved.
Need help? Contact us at {{supportEmail}}`,
    variables: {
      firstName: 'User first name',
      lastName: 'User last name',
      otp: 'Verification code',
      expiryMinutes: 'Code expiry time in minutes',
      supportEmail: 'Support email address',
      currentYear: 'Current year'
    }
  },
  {
    name: 'password-reset',
    subject: 'HashCoreX - Password Reset Code',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Password Reset</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">HashCoreX Security</p>
    </div>
    
    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}}!</h2>
        
        <p>We received a request to reset your password. Please use the following code to reset your password:</p>
        
        <div style="background: #fff3cd; padding: 30px; border-radius: 8px; margin: 20px 0; text-align: center; border: 1px solid #ffeaa7;">
            <h2 style="color: #856404; font-size: 36px; letter-spacing: 8px; margin: 0; font-family: monospace;">{{otp}}</h2>
        </div>
        
        <p><strong>Important:</strong> This reset code will expire in {{expiryMinutes}} minutes for security reasons.</p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{resetUrl}}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Reset Password</a>
        </div>
        
        <p>If you didn't request a password reset, please ignore this email or contact our support team immediately.</p>
        
        <p>For security reasons, never share this code with anyone.</p>
        
        <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
    </div>
    
    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            © {{currentYear}} HashCoreX. All rights reserved.<br>
            Need help? Contact us at {{supportEmail}}
        </p>
    </div>
</body>
</html>`,
    textContent: `Password Reset - HashCoreX

Hello {{firstName}}!

We received a request to reset your password. Please use the following code to reset your password:

{{otp}}

Important: This reset code will expire in {{expiryMinutes}} minutes for security reasons.

Reset your password: {{resetUrl}}

If you didn't request a password reset, please ignore this email or contact our support team immediately.

For security reasons, never share this code with anyone.

Best regards,
The HashCoreX Team

© {{currentYear}} HashCoreX. All rights reserved.
Need help? Contact us at {{supportEmail}}`,
    variables: {
      firstName: 'User first name',
      lastName: 'User last name',
      otp: 'Reset code',
      expiryMinutes: 'Code expiry time in minutes',
      resetUrl: 'Password reset URL',
      supportEmail: 'Support email address',
      currentYear: 'Current year'
    }
  },
  {
    name: 'kyc-approved',
    subject: 'HashCoreX - KYC Verification Approved!',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Approved</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">KYC Approved!</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Verification Complete</p>
    </div>

    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Congratulations {{firstName}}!</h2>

        <p>Your KYC verification has been <strong>approved</strong>! You now have full access to all HashCoreX features.</p>

        <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;">
            <h3 style="margin-top: 0; color: #155724;">✅ What's Now Available:</h3>
            <ul style="color: #155724; margin: 0;">
                <li>Purchase mining units</li>
                <li>Withdraw earnings</li>
                <li>Access all premium features</li>
                <li>Higher transaction limits</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{miningUrl}}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; margin-right: 10px;">Start Mining</a>
            <a href="{{dashboardUrl}}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">View Dashboard</a>
        </div>

        <p>Thank you for completing the verification process. Start earning with our solar-powered mining platform!</p>

        <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
    </div>

    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            © {{currentYear}} HashCoreX. All rights reserved.<br>
            Need help? Contact us at {{supportEmail}}
        </p>
    </div>
</body>
</html>`,
    textContent: `KYC Approved - HashCoreX

Congratulations {{firstName}}!

Your KYC verification has been approved! You now have full access to all HashCoreX features.

What's Now Available:
- Purchase mining units
- Withdraw earnings
- Access all premium features
- Higher transaction limits

Start mining: {{miningUrl}}
View dashboard: {{dashboardUrl}}

Thank you for completing the verification process. Start earning with our solar-powered mining platform!

Best regards,
The HashCoreX Team

© {{currentYear}} HashCoreX. All rights reserved.
Need help? Contact us at {{supportEmail}}`,
    variables: {
      firstName: 'User first name',
      lastName: 'User last name',
      dashboardUrl: 'Dashboard URL',
      miningUrl: 'Mining page URL',
      supportEmail: 'Support email address',
      currentYear: 'Current year'
    }
  },
  {
    name: 'kyc-rejected',
    subject: 'HashCoreX - KYC Verification Requires Attention',
    htmlContent: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Requires Attention</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">KYC Requires Attention</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Verification Update</p>
    </div>

    <div style="background: #ffffff; padding: 30px; border: 1px solid #e0e0e0; border-top: none;">
        <h2 style="color: #333; margin-top: 0;">Hello {{firstName}},</h2>

        <p>We've reviewed your KYC submission and need you to resubmit your documents.</p>

        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;">
            <h3 style="margin-top: 0; color: #721c24;">📋 Reason for Resubmission:</h3>
            <p style="color: #721c24; margin: 0;">{{reason}}</p>
        </div>

        <h3>Next Steps:</h3>
        <ol>
            <li>Review the feedback above</li>
            <li>Prepare clear, high-quality documents</li>
            <li>Resubmit your KYC application</li>
            <li>Wait for our team to review (usually 24-48 hours)</li>
        </ol>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{kycUrl}}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Resubmit KYC</a>
        </div>

        <p>If you have any questions about the verification process, please contact our support team.</p>

        <p>Best regards,<br><strong>The HashCoreX Team</strong></p>
    </div>

    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #e0e0e0; border-top: none;">
        <p style="margin: 0; color: #6c757d; font-size: 12px;">
            © {{currentYear}} HashCoreX. All rights reserved.<br>
            Need help? Contact us at {{supportEmail}}
        </p>
    </div>
</body>
</html>`,
    textContent: `KYC Requires Attention - HashCoreX

Hello {{firstName}},

We've reviewed your KYC submission and need you to resubmit your documents.

Reason for Resubmission:
{{reason}}

Next Steps:
1. Review the feedback above
2. Prepare clear, high-quality documents
3. Resubmit your KYC application
4. Wait for our team to review (usually 24-48 hours)

Resubmit KYC: {{kycUrl}}

If you have any questions about the verification process, please contact our support team.

Best regards,
The HashCoreX Team

© {{currentYear}} HashCoreX. All rights reserved.
Need help? Contact us at {{supportEmail}}`,
    variables: {
      firstName: 'User first name',
      lastName: 'User last name',
      reason: 'Rejection reason',
      kycUrl: 'KYC page URL',
      supportEmail: 'Support email address',
      currentYear: 'Current year'
    }
  }
];

// Function to seed email templates
export async function seedEmailTemplates() {
  const { prisma } = await import('./database');
  
  try {
    console.log('🌱 Seeding email templates...');
    
    for (const template of defaultEmailTemplates) {
      await prisma.emailTemplate.upsert({
        where: { name: template.name },
        update: {
          subject: template.subject,
          htmlContent: template.htmlContent,
          textContent: template.textContent,
          variables: template.variables,
          isActive: true,
        },
        create: {
          name: template.name,
          subject: template.subject,
          htmlContent: template.htmlContent,
          textContent: template.textContent,
          variables: template.variables,
          isActive: true,
        },
      });
    }
    
    console.log(`✅ Successfully seeded ${defaultEmailTemplates.length} email templates`);
  } catch (error) {
    console.error('❌ Error seeding email templates:', error);
    throw error;
  }
}
