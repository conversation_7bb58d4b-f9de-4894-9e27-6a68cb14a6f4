"use strict";(()=>{var e={};e.id=1376,e.ids=[1376],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>h,Lx:()=>w,Oj:()=>y,b9:()=>f,qc:()=>x});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",l=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),c=e=>i().sign(e,o,{expiresIn:l}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=m(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},h=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=p(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:c({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),x=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},16918:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};r.r(a),r.d(a,{GET:()=>u});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(12909),d=r(31183);async function u(e){try{let{authenticated:t,user:r}=await (0,l.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if("ADMIN"!==r.role)return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{searchParams:a}=new URL(e.url),s=a.get("dateRange")||"30d";a.get("type");let i=a.get("status")||"all",n=parseInt(a.get("page")||"1"),u=parseInt(a.get("limit")||"20"),c=(n-1)*u,m={};if("all"!==s){let e=parseInt(s.replace("d",""));m={gte:new Date(Date.now()-24*e*36e5)}}let p={type:"DIRECT_REFERRAL"};"all"!==s&&(p.createdAt=m),"all"!==i&&(p.status=i);let f=await d.prisma.transaction.findMany({where:p,include:{user:{select:{id:!0,email:!0,firstName:!0,lastName:!0}}},orderBy:{createdAt:"desc"},take:u,skip:c}),h=await Promise.all(f.map(async e=>{let t=null;if(e.reference&&e.reference.startsWith("from_user:")){let r=e.reference.replace("from_user:","");t=await d.prisma.user.findUnique({where:{id:r},select:{id:!0,email:!0,firstName:!0,lastName:!0}})}if(!t&&e.user)for(let r of(await d.prisma.user.findMany({where:{referrerId:e.userId},select:{id:!0,email:!0,firstName:!0,lastName:!0,miningUnits:{select:{id:!0,investmentAmount:!0,createdAt:!0},orderBy:{createdAt:"desc"}}}}))){for(let a of r.miningUnits){let s=.1*a.investmentAmount,i=Math.abs(new Date(e.createdAt).getTime()-new Date(a.createdAt).getTime());if(.01>Math.abs(s-e.amount)&&i<=18e5){t={id:r.id,email:r.email,firstName:r.firstName,lastName:r.lastName};break}}if(t)break}let r=e.amount/.1;return{id:e.id,fromUserId:t?.id||"unknown",toUserId:e.userId,fromUser:t||{id:"unknown",email:"Unknown",firstName:"Unknown",lastName:"User"},toUser:e.user,amount:e.amount,commissionRate:10,originalAmount:r,type:"DIRECT_REFERRAL",description:e.description||"",createdAt:e.createdAt,status:e.status}})),w=await d.prisma.transaction.count({where:p});return o.NextResponse.json({success:!0,data:h,pagination:{total:w,limit:u,offset:c,hasMore:c+u<w}})}catch(e){return console.error("Referral commissions fetch error:",e),o.NextResponse.json({success:!1,error:"Failed to fetch referral commissions"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/referral-commissions/route",pathname:"/api/admin/referral-commissions",filename:"route",bundlePath:"app/api/admin/referral-commissions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\referral-commissions\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:f}=c;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(16918));module.exports=a})();