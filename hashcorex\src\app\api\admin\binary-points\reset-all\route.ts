import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST - Reset all binary points for all users (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    console.log('Starting binary points reset for all users...');

    // Get all users with binary points
    const usersWithPoints = await prisma.binaryPoints.findMany({
      select: {
        userId: true,
        leftPoints: true,
        rightPoints: true,
        user: {
          select: {
            email: true,
          },
        },
      },
    });

    console.log(`Found ${usersWithPoints.length} users with binary points to reset`);

    // Reset all binary points to 0
    const resetResult = await prisma.binaryPoints.updateMany({
      data: {
        leftPoints: 0,
        rightPoints: 0,
        matchedPoints: 0,
        flushDate: new Date(),
      },
    });

    console.log(`Reset binary points for ${resetResult.count} records`);

    // Log the admin action
    await prisma.systemLog.create({
      data: {
        action: 'ADMIN_BINARY_POINTS_RESET_ALL',
        details: JSON.stringify({
          adminId: user.id,
          adminEmail: user.email,
          resetCount: resetResult.count,
          usersAffected: usersWithPoints.map(u => ({
            userId: u.userId,
            email: u.user.email,
            previousLeftPoints: u.leftPoints,
            previousRightPoints: u.rightPoints,
          })),
          timestamp: new Date().toISOString(),
        }),
      },
    });

    return NextResponse.json({
      success: true,
      message: `Successfully reset binary points for ${resetResult.count} users`,
      data: {
        resetCount: resetResult.count,
        usersAffected: usersWithPoints.length,
      },
    });

  } catch (error) {
    console.error('Binary points reset error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to reset binary points' },
      { status: 500 }
    );
  }
}
