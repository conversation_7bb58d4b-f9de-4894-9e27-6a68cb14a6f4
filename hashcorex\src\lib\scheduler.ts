import { calculateDailyROI, processWeeklyEarnings, expireOldMiningUnits } from './mining';
import { processBinaryMatching } from './referral';
import { systemLogDb } from './database';

interface ScheduledTask {
  id: string;
  name: string;
  cronExpression: string;
  handler: () => Promise<void>;
  lastRun?: Date;
  nextRun?: Date;
  isRunning: boolean;
}

class ServerSideScheduler {
  private tasks: Map<string, ScheduledTask> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private isInitialized = false;

  constructor() {
    this.setupTasks();
  }

  private setupTasks() {
    // Daily ROI calculation - runs daily at 00:01 UTC
    this.addTask({
      id: 'daily-roi',
      name: 'Daily ROI Calculation',
      cronExpression: '0 1 0 * * *', // Daily at 00:01 UTC
      handler: this.handleDailyROI.bind(this),
      isRunning: false,
    });

    // Weekly earnings payout - runs Sunday at 18:30 UTC (00:00 AM GMT+5:30)
    this.addTask({
      id: 'weekly-payout',
      name: 'Weekly Earnings Payout',
      cronExpression: '0 30 18 * * 0', // Sunday at 18:30 UTC (00:00 AM GMT+5:30)
      handler: this.handleWeeklyPayout.bind(this),
      isRunning: false,
    });

    // Binary matching - runs weekly at 09:30 UTC (15:00 GMT+5:30) on Saturday
    this.addTask({
      id: 'binary-matching',
      name: 'Binary Matching',
      cronExpression: '0 30 9 * * 6', // Saturday at 09:30 UTC (15:00 GMT+5:30)
      handler: this.handleBinaryMatching.bind(this),
      isRunning: false,
    });
  }

  private addTask(task: ScheduledTask) {
    this.tasks.set(task.id, task);
  }

  private async handleDailyROI() {
    try {
      console.log('🔄 Starting scheduled daily ROI calculation...');
      
      // First, expire any old mining units
      const expiredCount = await expireOldMiningUnits();
      console.log(`⏰ Expired ${expiredCount} old mining units`);

      // Calculate daily ROI for all active units
      const roiResults = await calculateDailyROI();
      console.log(`💰 Processed ${roiResults.length} users for daily ROI`);

      const totalEarnings = roiResults.reduce((sum, result) => sum + result.totalEarnings, 0);

      // Log the execution
      await systemLogDb.create({
        action: 'SCHEDULED_DAILY_ROI_EXECUTED',
        details: {
          usersProcessed: roiResults.length,
          totalEarnings,
          expiredUnits: expiredCount,
          executionTime: new Date().toISOString(),
          scheduler: 'server-side',
        },
      });

      console.log(`✅ Daily ROI calculation completed. Total earnings: $${totalEarnings.toFixed(2)}`);
    } catch (error) {
      console.error('❌ Error in daily ROI calculation:', error);
      await systemLogDb.create({
        action: 'SCHEDULED_DAILY_ROI_ERROR',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          executionTime: new Date().toISOString(),
          scheduler: 'server-side',
        },
      });
    }
  }

  private async handleWeeklyPayout() {
    try {
      console.log('🔄 Starting scheduled weekly earnings payout...');
      
      const payoutResults = await processWeeklyEarnings();
      const totalDistributed = payoutResults.reduce((sum, result) => sum + result.totalEarnings, 0);

      // Log the execution
      await systemLogDb.create({
        action: 'SCHEDULED_WEEKLY_PAYOUT_EXECUTED',
        details: {
          usersProcessed: payoutResults.length,
          totalDistributed,
          executionTime: new Date().toISOString(),
          payoutTime: 'Sunday 00:00 AM GMT+5:30',
          scheduler: 'server-side',
        },
      });

      console.log(`✅ Weekly payout completed. Distributed: $${totalDistributed.toFixed(2)} to ${payoutResults.length} users`);
    } catch (error) {
      console.error('❌ Error in weekly payout:', error);
      await systemLogDb.create({
        action: 'SCHEDULED_WEEKLY_PAYOUT_ERROR',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          executionTime: new Date().toISOString(),
          scheduler: 'server-side',
        },
      });
    }
  }

  private async handleBinaryMatching() {
    try {
      console.log('🔄 Starting scheduled binary matching...');
      
      const matchingResults = await processBinaryMatching();
      const totalPayouts = matchingResults.reduce((sum, result) => sum + result.payout, 0);
      const totalMatchedPoints = matchingResults.reduce((sum, result) => sum + result.matchedPoints, 0);

      // Log the execution
      await systemLogDb.create({
        action: 'SCHEDULED_BINARY_MATCHING_EXECUTED',
        details: {
          usersProcessed: matchingResults.length,
          totalPayouts,
          totalMatchedPoints,
          executionTime: new Date().toISOString(),
          matchingTime: 'Saturday 15:00 GMT+5:30',
          scheduler: 'server-side',
        },
      });

      console.log(`✅ Binary matching completed. Payouts: $${totalPayouts.toFixed(2)}, Matched points: ${totalMatchedPoints}`);
    } catch (error) {
      console.error('❌ Error in binary matching:', error);
      await systemLogDb.create({
        action: 'SCHEDULED_BINARY_MATCHING_ERROR',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          executionTime: new Date().toISOString(),
          scheduler: 'server-side',
        },
      });
    }
  }

  private parseCronExpression(cronExpression: string): number {
    // Simple cron parser for our specific use cases
    // Format: "second minute hour dayOfMonth month dayOfWeek"
    const parts = cronExpression.split(' ');
    if (parts.length !== 6) {
      throw new Error('Invalid cron expression format');
    }

    const [second, minute, hour, , , dayOfWeek] = parts.map(p => p === '*' ? -1 : parseInt(p));
    
    const now = new Date();
    const target = new Date();
    
    // Set the target time
    if (hour !== -1) target.setUTCHours(hour);
    if (minute !== -1) target.setUTCMinutes(minute);
    if (second !== -1) target.setUTCSeconds(second);
    target.setUTCMilliseconds(0);

    // Handle day of week
    if (dayOfWeek !== -1) {
      const currentDay = target.getUTCDay();
      const daysUntilTarget = (dayOfWeek - currentDay + 7) % 7;
      
      if (daysUntilTarget === 0 && target <= now) {
        // If it's the same day but time has passed, schedule for next week
        target.setUTCDate(target.getUTCDate() + 7);
      } else if (daysUntilTarget > 0) {
        target.setUTCDate(target.getUTCDate() + daysUntilTarget);
      }
    } else {
      // Daily task
      if (target <= now) {
        target.setUTCDate(target.getUTCDate() + 1);
      }
    }

    return target.getTime() - now.getTime();
  }

  private scheduleTask(task: ScheduledTask) {
    try {
      const delay = this.parseCronExpression(task.cronExpression);
      task.nextRun = new Date(Date.now() + delay);
      
      console.log(`📅 Scheduled ${task.name} to run at ${task.nextRun.toISOString()}`);
      
      const timeout = setTimeout(async () => {
        if (!task.isRunning) {
          task.isRunning = true;
          task.lastRun = new Date();
          
          try {
            await task.handler();
          } finally {
            task.isRunning = false;
            // Reschedule the task
            this.scheduleTask(task);
          }
        }
      }, delay);

      // Clear any existing timeout
      const existingTimeout = this.intervals.get(task.id);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }
      
      this.intervals.set(task.id, timeout);
    } catch (error) {
      console.error(`❌ Error scheduling task ${task.name}:`, error);
    }
  }

  public start() {
    if (this.isInitialized) {
      console.log('⚠️ Scheduler already initialized');
      return;
    }

    console.log('🚀 Starting server-side scheduler...');
    
    // Schedule all tasks
    for (const task of this.tasks.values()) {
      this.scheduleTask(task);
    }
    
    this.isInitialized = true;
    console.log(`✅ Scheduler initialized with ${this.tasks.size} tasks`);
  }

  public stop() {
    console.log('🛑 Stopping server-side scheduler...');
    
    // Clear all timeouts
    for (const timeout of this.intervals.values()) {
      clearTimeout(timeout);
    }
    
    this.intervals.clear();
    this.isInitialized = false;
    console.log('✅ Scheduler stopped');
  }

  public getTaskStatus() {
    const status = [];
    for (const task of this.tasks.values()) {
      status.push({
        id: task.id,
        name: task.name,
        cronExpression: task.cronExpression,
        lastRun: task.lastRun,
        nextRun: task.nextRun,
        isRunning: task.isRunning,
      });
    }
    return status;
  }

  public async runTaskManually(taskId: string) {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    if (task.isRunning) {
      throw new Error(`Task ${taskId} is already running`);
    }

    console.log(`🔧 Manually running task: ${task.name}`);
    task.isRunning = true;
    
    try {
      await task.handler();
      task.lastRun = new Date();
    } finally {
      task.isRunning = false;
    }
  }
}

// Export singleton instance
export const scheduler = new ServerSideScheduler();
