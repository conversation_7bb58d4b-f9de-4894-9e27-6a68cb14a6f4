"use strict";(()=>{var e={};e.id=3008,e.ids=[3008],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>g,DY:()=>y,Lx:()=>f,Oj:()=>w,b9:()=>h,qc:()=>k});var a=r(85663),s=r(43205),i=r.n(s),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",c=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await a.Ay.hash(e,12),u=async(e,t)=>await a.Ay.compare(e,t),l=e=>i().sign(e,o,{expiresIn:c}),m=e=>{try{return i().verify(e,o)}catch(e){return null}},p=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},h=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=m(t);if(!r)return{authenticated:!1,user:null};let a=await n.Gy.findByEmail(r.email);return a?{authenticated:!0,user:a}:{authenticated:!1,user:null}},y=async e=>{let t,a;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let s=await d(e.password),i=!1;do a=p(),i=!await n.Gy.findByReferralId(a);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:a});if(t){let{placeUserByReferralType:a}=await r.e(2746).then(r.bind(r,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await a(t,o.id,s)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},f=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await u(e.password,t.password))throw Error("Invalid email or password");return{token:l({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},g=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),k=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78231:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};r.r(a),r.d(a,{GET:()=>u});var s=r(96559),i=r(48088),n=r(37719),o=r(32190),c=r(12909),d=r(31183);async function u(e){try{let{authenticated:t,user:r}=await (0,c.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,c.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let a=new URL(e.url),s=a.searchParams.get("search")||"",i=a.searchParams.get("status")||"ALL",n=parseInt(a.searchParams.get("page")||"1"),u=parseInt(a.searchParams.get("limit")||"20"),l=a.searchParams.get("sortBy")||"createdAt",m=a.searchParams.get("sortOrder")||"desc",p={};s&&(p.OR=[{firstName:{contains:s,mode:"insensitive"}},{lastName:{contains:s,mode:"insensitive"}},{email:{contains:s,mode:"insensitive"}},{referralId:{contains:s,mode:"insensitive"}}]),"ALL"!==i&&(p.kycStatus=i);let h=await d.prisma.user.findMany({where:{...p,kycDocuments:{some:{}}},include:{kycDocuments:{orderBy:{createdAt:"desc"}}},orderBy:{[l]:m},skip:(n-1)*u,take:u}),y=await d.prisma.user.count({where:{...p,kycDocuments:{some:{}}}}),f=h.map(e=>({userId:e.id,user:{firstName:e.firstName,lastName:e.lastName,email:e.email,referralId:e.referralId},documents:e.kycDocuments.map(e=>({id:e.id,documentType:e.documentType,idType:e.idType,documentSide:e.documentSide,documentUrl:e.filePath,status:e.status,submittedAt:e.createdAt.toISOString(),reviewedAt:e.reviewedAt?.toISOString(),rejectionReason:e.rejectionReason})),status:e.kycStatus,submittedAt:e.kycDocuments[0]?.createdAt.toISOString()||e.createdAt.toISOString()})),w=Math.ceil(y/u);return o.NextResponse.json({success:!0,data:{users:f,pagination:{currentPage:n,totalPages:w,totalCount:y,hasNext:n<w,hasPrev:n>1,limit:u},filters:{searchTerm:s,status:i,sortBy:l,sortOrder:m}}})}catch(e){return console.error("KYC search error:",e),o.NextResponse.json({success:!1,error:"Failed to search KYC data"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/kyc/search/route",pathname:"/api/admin/kyc/search",filename:"route",bundlePath:"app/api/admin/kyc/search/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\kyc\\search\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:h}=l;function y(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,5315,3306],()=>r(78231));module.exports=a})();