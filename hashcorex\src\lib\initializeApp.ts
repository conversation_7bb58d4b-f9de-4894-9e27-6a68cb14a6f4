import { scheduler } from './scheduler';

let isInitialized = false;

export function initializeApplication() {
  if (isInitialized) {
    return;
  }

  console.log('🚀 Initializing HashCoreX application...');

  // Start the server-side scheduler
  if (process.env.NODE_ENV === 'production' || process.env.ENABLE_SCHEDULER === 'true') {
    scheduler.start();
  } else {
    console.log('⚠️ Scheduler disabled in development mode. Set ENABLE_SCHEDULER=true to enable.');
  }

  isInitialized = true;
  console.log('✅ HashCoreX application initialized successfully');
}

export function shutdownApplication() {
  if (!isInitialized) {
    return;
  }

  console.log('🛑 Shutting down HashCoreX application...');
  
  // Stop the scheduler
  scheduler.stop();
  
  isInitialized = false;
  console.log('✅ HashCoreX application shutdown complete');
}

// Handle graceful shutdown
if (typeof process !== 'undefined') {
  process.on('SIGTERM', shutdownApplication);
  process.on('SIGINT', shutdownApplication);
  process.on('beforeExit', shutdownApplication);
}
