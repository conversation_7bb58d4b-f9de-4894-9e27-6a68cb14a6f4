import nodemailer from 'nodemailer';
import { prisma } from './database';

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  variables?: Record<string, any>;
}

export interface SMTPConfig {
  id: string;
  host: string;
  port: number;
  secure: boolean;
  username: string;
  password: string;
  senderName: string;
  senderEmail: string;
  isActive: boolean;
}

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  text?: string;
  templateId?: string;
  userId?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private currentConfig: SMTPConfig | null = null;

  async initializeTransporter(): Promise<void> {
    try {
      // Get active SMTP configuration
      const smtpConfig = await prisma.sMTPConfiguration.findFirst({
        where: { isActive: true },
      });

      if (!smtpConfig) {
        console.warn('No active SMTP configuration found');
        return;
      }

      this.currentConfig = smtpConfig;

      // Create transporter
      this.transporter = nodemailer.createTransporter({
        host: smtpConfig.host,
        port: smtpConfig.port,
        secure: smtpConfig.secure,
        auth: {
          user: smtpConfig.username,
          pass: smtpConfig.password,
        },
        tls: {
          rejectUnauthorized: false, // For development/testing
        },
      });

      // Verify connection
      await this.transporter.verify();
      console.log('✅ Email service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize email service:', error);
      this.transporter = null;
      this.currentConfig = null;
    }
  }

  async sendEmail(emailData: EmailData): Promise<boolean> {
    try {
      if (!this.transporter || !this.currentConfig) {
        await this.initializeTransporter();
        if (!this.transporter || !this.currentConfig) {
          throw new Error('Email service not configured');
        }
      }

      const mailOptions = {
        from: `"${this.currentConfig.senderName}" <${this.currentConfig.senderEmail}>`,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Log successful email
      await this.logEmail({
        userId: emailData.userId,
        recipient: emailData.to,
        subject: emailData.subject,
        templateId: emailData.templateId,
        status: 'SENT',
        sentAt: new Date(),
      });

      console.log(`✅ Email sent successfully to ${emailData.to}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to send email to ${emailData.to}:`, error);

      // Log failed email
      await this.logEmail({
        userId: emailData.userId,
        recipient: emailData.to,
        subject: emailData.subject,
        templateId: emailData.templateId,
        status: 'FAILED',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      });

      return false;
    }
  }

  async sendTemplateEmail(
    templateName: string,
    to: string,
    variables: Record<string, any> = {},
    userId?: string
  ): Promise<boolean> {
    try {
      const template = await this.getTemplate(templateName);
      if (!template) {
        throw new Error(`Template '${templateName}' not found`);
      }

      const processedSubject = this.processTemplate(template.subject, variables);
      const processedHtml = this.processTemplate(template.htmlContent, variables);
      const processedText = template.textContent 
        ? this.processTemplate(template.textContent, variables)
        : undefined;

      return await this.sendEmail({
        to,
        subject: processedSubject,
        html: processedHtml,
        text: processedText,
        templateId: template.id,
        userId,
      });
    } catch (error) {
      console.error(`❌ Failed to send template email '${templateName}' to ${to}:`, error);
      return false;
    }
  }

  private processTemplate(template: string, variables: Record<string, any>): string {
    let processed = template;
    
    // Replace variables in the format {{variableName}}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      processed = processed.replace(regex, String(value));
    });

    return processed;
  }

  private async getTemplate(name: string): Promise<EmailTemplate | null> {
    try {
      const template = await prisma.emailTemplate.findFirst({
        where: { 
          name,
          isActive: true,
        },
      });

      return template;
    } catch (error) {
      console.error(`❌ Failed to get template '${name}':`, error);
      return null;
    }
  }

  private async logEmail(logData: {
    userId?: string;
    recipient: string;
    subject: string;
    templateId?: string;
    status: 'PENDING' | 'SENT' | 'FAILED' | 'BOUNCED';
    errorMessage?: string;
    sentAt?: Date;
  }): Promise<void> {
    try {
      await prisma.emailLog.create({
        data: logData,
      });
    } catch (error) {
      console.error('❌ Failed to log email:', error);
    }
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      await this.initializeTransporter();
      if (!this.transporter) {
        return { success: false, error: 'Failed to initialize transporter' };
      }

      await this.transporter.verify();
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async getEmailStats(): Promise<{
    totalSent: number;
    totalFailed: number;
    recentEmails: any[];
  }> {
    try {
      const [totalSent, totalFailed, recentEmails] = await Promise.all([
        prisma.emailLog.count({ where: { status: 'SENT' } }),
        prisma.emailLog.count({ where: { status: 'FAILED' } }),
        prisma.emailLog.findMany({
          take: 10,
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: { email: true, firstName: true, lastName: true },
            },
            template: {
              select: { name: true },
            },
          },
        }),
      ]);

      return {
        totalSent,
        totalFailed,
        recentEmails,
      };
    } catch (error) {
      console.error('❌ Failed to get email stats:', error);
      return {
        totalSent: 0,
        totalFailed: 0,
        recentEmails: [],
      };
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Email event types for automated triggers
export const EMAIL_EVENTS = {
  USER_REGISTRATION: 'user-registration',
  EMAIL_VERIFICATION: 'email-verification',
  PASSWORD_RESET: 'password-reset',
  KYC_APPROVED: 'kyc-approved',
  KYC_REJECTED: 'kyc-rejected',
  WITHDRAWAL_REQUESTED: 'withdrawal-requested',
  WITHDRAWAL_APPROVED: 'withdrawal-approved',
  WITHDRAWAL_REJECTED: 'withdrawal-rejected',
  MINING_UNIT_PURCHASED: 'mining-unit-purchased',
  WEEKLY_EARNINGS: 'weekly-earnings',
  BINARY_COMMISSION: 'binary-commission',
  REFERRAL_COMMISSION: 'referral-commission',
} as const;

export type EmailEventType = typeof EMAIL_EVENTS[keyof typeof EMAIL_EVENTS];
