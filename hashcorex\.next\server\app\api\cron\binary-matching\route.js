"use strict";(()=>{var e={};e.id=1614,e.ids=[1614],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43286:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>l});var a={};t.r(a),t.d(a,{POST:()=>p});var s=t(96559),n=t(48088),o=t(37719),i=t(32190),c=t(2746),u=t(6710);async function p(e){try{let r=e.headers.get("authorization"),t=process.env.CRON_SECRET||"default-secret";if(r!==`Bearer ${t}`)return i.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});console.log("Starting binary matching cron job...");let a=await (0,c.E5)(),s=a.reduce((e,r)=>e+r.payout,0),n=a.reduce((e,r)=>e+r.matchedPoints,0);return await u.AJ.create({action:"BINARY_MATCHING_CRON_EXECUTED",details:{usersProcessed:a.length,totalPayouts:s,totalMatchedPoints:n,executionTime:new Date().toISOString(),matchingTime:"15:00 UTC (Weekly)"}}),i.NextResponse.json({success:!0,message:"Binary matching completed",data:{usersProcessed:a.length,totalPayouts:s,totalMatchedPoints:n}})}catch(e){return console.error("Binary matching cron job error:",e),await u.AJ.create({action:"BINARY_MATCHING_CRON_ERROR",details:{error:e.message,stack:e.stack,timestamp:new Date().toISOString()}}),i.NextResponse.json({success:!1,error:"Binary matching failed"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cron/binary-matching/route",pathname:"/api/cron/binary-matching",filename:"route",bundlePath:"app/api/cron/binary-matching/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\cron\\binary-matching\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:l,serverHooks:m}=d;function h(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:l})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,580,3306,2746],()=>t(43286));module.exports=a})();