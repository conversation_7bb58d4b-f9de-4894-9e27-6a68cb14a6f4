"use strict";(()=>{var e={};e.id=2784,e.ids=[2784],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,a)=>{a.d(t,{DT:()=>I,DY:()=>f,Lx:()=>w,Oj:()=>y,b9:()=>m,qc:()=>E});var r=a(85663),s=a(43205),i=a.n(s),n=a(6710);let l=process.env.JWT_SECRET||"fallback-secret-key",o=process.env.JWT_EXPIRES_IN||"7d",d=async e=>await r.Ay.hash(e,12),c=async(e,t)=>await r.Ay.compare(e,t),u=e=>i().sign(e,l,{expiresIn:o}),p=e=>{try{return i().verify(e,l)}catch(e){return null}},h=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let a=0;a<8;a++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},m=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let a=p(t);if(!a)return{authenticated:!1,user:null};let r=await n.Gy.findByEmail(a.email);return r?{authenticated:!0,user:r}:{authenticated:!1,user:null}},f=async e=>{let t,r;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let a=await n.Gy.findByReferralId(e.referralCode);if(!a)throw Error("Invalid referral code");t=a.id}let s=await d(e.password),i=!1;do r=h(),i=!await n.Gy.findByReferralId(r);while(!i);let l=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:s,referralId:r});if(t){let{placeUserByReferralType:r}=await a.e(2746).then(a.bind(a,2746)),s="general";"left"===e.placementSide?s="left":"right"===e.placementSide&&(s="right"),await r(t,l.id,s)}return{id:l.id,email:l.email,referralId:l.referralId,kycStatus:l.kycStatus}},w=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await c(e.password,t.password))throw Error("Invalid email or password");return{token:u({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},y=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},I=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),E=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},92551:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var r={};a.r(r),a.d(r,{GET:()=>c});var s=a(96559),i=a(48088),n=a(37719),l=a(32190),o=a(12909),d=a(6710);async function c(e){try{let{authenticated:t,user:a}=await (0,o.b9)(e);if(!t||!a)return l.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});let r=await d.k_.findByUserId(a.id),s=await d.DR.findByUserId(a.id,{limit:10}),i=await d.J6.findByUserId(a.id,{limit:5}),n=0,c=await d.DR.findByUserId(a.id);for(let e of c)if("COMPLETED"===e.status)switch(e.type){case"MINING_EARNINGS":case"DIRECT_REFERRAL":case"BINARY_BONUS":case"DEPOSIT":n+=e.amount;break;case"WITHDRAWAL":case"PURCHASE":n-=e.amount}let u=s.filter(e=>"PENDING"===e.status&&("MINING_EARNINGS"===e.type||"DIRECT_REFERRAL"===e.type||"BINARY_BONUS"===e.type)).reduce((e,t)=>e+t.amount,0),p=s.slice(0,20).map(e=>({id:e.id,type:e.type,amount:e.amount,description:e.description,status:e.status,createdAt:e.createdAt}));return l.NextResponse.json({success:!0,data:{balance:Math.max(0,r.availableBalance),availableBalance:r.availableBalance,pendingBalance:r.pendingBalance,totalDeposits:r.totalDeposits,totalWithdrawals:r.totalWithdrawals,totalEarnings:r.totalEarnings,lastUpdated:r.lastUpdated,legacyBalance:Math.max(0,n),balanceMatch:.01>Math.abs(r.availableBalance-n),pendingEarnings:u,recentTransactions:p,recentDeposits:i.map(e=>({id:e.id,transactionId:e.transactionId,amount:e.usdtAmount,status:e.status,createdAt:e.createdAt,processedAt:e.processedAt})),stats:{totalTransactions:c.length,completedDeposits:i.filter(e=>"COMPLETED"===e.status||"CONFIRMED"===e.status).length,pendingDeposits:i.filter(e=>"PENDING"===e.status||"PENDING_VERIFICATION"===e.status).length}}})}catch(e){return console.error("Wallet balance fetch error:",e),l.NextResponse.json({success:!1,error:"Failed to fetch wallet balance"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wallet/balance/route",pathname:"/api/wallet/balance",filename:"route",bundlePath:"app/api/wallet/balance/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\wallet\\balance\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:m}=u;function f(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580,5315,3306],()=>a(92551));module.exports=r})();