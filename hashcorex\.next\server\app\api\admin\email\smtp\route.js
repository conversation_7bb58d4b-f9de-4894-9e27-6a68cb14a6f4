"use strict";(()=>{var e={};e.id=4685,e.ids=[4685],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,r,t)=>{t.d(r,{DT:()=>x,DY:()=>w,Lx:()=>h,Oj:()=>y,b9:()=>f,qc:()=>g});var s=t(85663),a=t(43205),i=t.n(a),n=t(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await s.Ay.hash(e,12),d=async(e,r)=>await s.Ay.compare(e,r),c=e=>i().sign(e,o,{expiresIn:u}),p=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",r="HC";for(let t=0;t<8;t++)r+=e.charAt(Math.floor(Math.random()*e.length));return r},f=async e=>{let r=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!r)return{authenticated:!1,user:null};let t=p(r);if(!t)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(t.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},w=async e=>{let r,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let t=await n.Gy.findByReferralId(e.referralCode);if(!t)throw Error("Invalid referral code");r=t.id}let a=await l(e.password),i=!1;do s=m(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(r){let{placeUserByReferralType:s}=await t.e(2746).then(t.bind(t,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(r,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let r=await n.Gy.findByEmail(e.email);if(!r||!await d(e.password,r.password))throw Error("Invalid email or password");return{token:c({userId:r.id,email:r.email}),user:{id:r.id,email:r.email,referralId:r.referralId,kycStatus:r.kycStatus}}},y=e=>{let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||r.push("Password must contain at least one special character"),{valid:0===r.length,errors:r}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),g=async e=>{let r=await n.Gy.findById(e);return r?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},56254:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>c});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(12909),l=t(6710);async function d(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=await l.prisma.sMTPConfiguration.findFirst({where:{isActive:!0}});if(!s)return o.NextResponse.json({success:!0,data:null});let a={...s,password:s.password?"••••••••":""};return o.NextResponse.json({success:!0,data:a})}catch(e){return console.error("Error getting SMTP configuration:",e),o.NextResponse.json({success:!1,error:"Failed to get SMTP configuration"},{status:500})}}async function c(e){try{let{authenticated:r,user:t}=await (0,u.b9)(e);if(!r||!t)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(t.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{host:s,port:a,secure:i,username:n,password:d,senderName:c,senderEmail:p,isActive:m}=await e.json();if(!s||!a||!n||!c||!p)return o.NextResponse.json({success:!1,error:"All required fields must be provided"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(p))return o.NextResponse.json({success:!1,error:"Invalid sender email format"},{status:400});if(a<1||a>65535)return o.NextResponse.json({success:!1,error:"Invalid port number"},{status:400});let f=await l.prisma.sMTPConfiguration.findFirst({where:{isActive:!0}}),w={host:s,port:parseInt(a),secure:!!i,username:n,password:"••••••••"===d&&f?f.password:d,senderName:c,senderEmail:p,isActive:!!m};return f?await l.prisma.sMTPConfiguration.update({where:{id:f.id},data:w}):await l.prisma.sMTPConfiguration.create({data:w}),o.NextResponse.json({success:!0,message:"SMTP configuration saved successfully"})}catch(e){return console.error("Error saving SMTP configuration:",e),o.NextResponse.json({success:!1,error:"Failed to save SMTP configuration"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email/smtp/route",pathname:"/api/admin/email/smtp",filename:"route",bundlePath:"app/api/admin/email/smtp/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\smtp\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:w}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5315,3306],()=>t(56254));module.exports=s})();