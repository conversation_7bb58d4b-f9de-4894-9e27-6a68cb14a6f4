"use strict";(()=>{var e={};e.id=4685,e.ids=[4685],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,r)=>{r.d(t,{DT:()=>x,DY:()=>y,Lx:()=>h,Oj:()=>w,b9:()=>f,qc:()=>v});var s=r(85663),a=r(43205),i=r.n(a),n=r(6710);let o=process.env.JWT_SECRET||"fallback-secret-key",u=process.env.JWT_EXPIRES_IN||"7d",l=async e=>await s.Ay.hash(e,12),d=async(e,t)=>await s.Ay.compare(e,t),p=e=>i().sign(e,o,{expiresIn:u}),c=e=>{try{return i().verify(e,o)}catch(e){return null}},m=()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",t="HC";for(let r=0;r<8;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t},f=async e=>{let t=e.headers.get("authorization")?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return{authenticated:!1,user:null};let r=c(t);if(!r)return{authenticated:!1,user:null};let s=await n.Gy.findByEmail(r.email);return s?{authenticated:!0,user:s}:{authenticated:!1,user:null}},y=async e=>{let t,s;if(await n.Gy.findByEmail(e.email))throw Error("User already exists with this email");if(e.referralCode){let r=await n.Gy.findByReferralId(e.referralCode);if(!r)throw Error("Invalid referral code");t=r.id}let a=await l(e.password),i=!1;do s=m(),i=!await n.Gy.findByReferralId(s);while(!i);let o=await n.Gy.create({email:e.email,firstName:e.firstName,lastName:e.lastName,password:a,referralId:s});if(t){let{placeUserByReferralType:s}=await r.e(2746).then(r.bind(r,2746)),a="general";"left"===e.placementSide?a="left":"right"===e.placementSide&&(a="right"),await s(t,o.id,a)}return{id:o.id,email:o.email,referralId:o.referralId,kycStatus:o.kycStatus}},h=async e=>{let t=await n.Gy.findByEmail(e.email);if(!t||!await d(e.password,t.password))throw Error("Invalid email or password");return{token:p({userId:t.id,email:t.email}),user:{id:t.id,email:t.email,referralId:t.referralId,kycStatus:t.kycStatus}}},w=e=>{let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)||t.push("Password must contain at least one special character"),{valid:0===t.length,errors:t}},x=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),v=async e=>{let t=await n.Gy.findById(e);return t?.role==="ADMIN"}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(12909),l=r(6710);async function d(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let s=await l.prisma.adminSettings.findMany({where:{key:{in:["smtp_host","smtp_port","smtp_user","smtp_password","smtp_secure","smtp_from_email","smtp_from_name"]}}});if(0===s.length)return o.NextResponse.json({success:!0,data:null});let a=s.reduce((e,t)=>(e[t.key]=t.value,e),{}),i={...a,smtp_password:a.smtp_password?"••••••••":""};return o.NextResponse.json({success:!0,data:i})}catch(e){return console.error("Error getting SMTP configuration:",e),o.NextResponse.json({success:!1,error:"Failed to get SMTP configuration"},{status:500})}}async function p(e){try{let{authenticated:t,user:r}=await (0,u.b9)(e);if(!t||!r)return o.NextResponse.json({success:!1,error:"Not authenticated"},{status:401});if(!await (0,u.qc)(r.id))return o.NextResponse.json({success:!1,error:"Admin access required"},{status:403});let{smtp_host:s,smtp_port:a,smtp_secure:i,smtp_user:n,smtp_password:d,smtp_from_name:p,smtp_from_email:c}=await e.json();if(!s||!a||!n||!p||!c)return o.NextResponse.json({success:!1,error:"All required fields must be provided"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c))return o.NextResponse.json({success:!1,error:"Invalid sender email format"},{status:400});let m=parseInt(a);if(m<1||m>65535)return o.NextResponse.json({success:!1,error:"Invalid port number"},{status:400});let f=d;if("••••••••"===d){let e=await l.prisma.adminSettings.findUnique({where:{key:"smtp_password"}});f=e?.value||""}for(let e of[{key:"smtp_host",value:s},{key:"smtp_port",value:a.toString()},{key:"smtp_secure",value:i?"true":"false"},{key:"smtp_user",value:n},{key:"smtp_password",value:f},{key:"smtp_from_name",value:p},{key:"smtp_from_email",value:c}])await l.prisma.adminSettings.upsert({where:{key:e.key},update:{value:e.value},create:e});return o.NextResponse.json({success:!0,message:"SMTP configuration saved successfully"})}catch(e){return console.error("Error saving SMTP configuration:",e),o.NextResponse.json({success:!1,error:"Failed to save SMTP configuration"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/email/smtp/route",pathname:"/api/admin/email/smtp",filename:"route",bundlePath:"app/api/admin/email/smtp/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Hash_Minings\\hashcorex\\src\\app\\api\\admin\\email\\smtp\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:y}=c;function h(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{e.exports=require("buffer")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5315,3306],()=>r(33873));module.exports=s})();